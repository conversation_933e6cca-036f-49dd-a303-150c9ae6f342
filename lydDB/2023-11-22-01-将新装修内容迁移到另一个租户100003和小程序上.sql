INSERT INTO `t_account_center` ( `create_time`, `is_deleted`, `update_time`, `head_style`, `custom_style`, `get_cart_text`, `hide_cart_inlet`, `order_info`, `menu_style`, `code_style`, `tenant_id`) VALUES ('2023-05-10 11:08:25', 0, '2023-11-01 11:35:54', 1, '{\"backgroundImage\":\"http://rhksl0fkl.hd-bkt.clouddn.com/********/46f3bcd50a7841578ce34e017693f4cf.png\",\"cardColor\":\"#E84513\",\"textColor\":\"#F8F404\"}', '领卡文案', 1, '{\"afterSaleIcon\":{\"name\":\"售后\",\"url\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/e72e0fb8015c4daf9b341d9e4407e22d.png\"},\"waitIcon\":{\"name\":\"待付款\",\"url\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/9981a93c017948098be4056195a018fe.png\"},\"waitPickingIcon\":{\"name\":\"待提货\",\"url\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/a286e9e2ce03433e83373f6f5d5fd90a.png\"},\"deliveryIcon\":{\"name\":\"配送中\",\"url\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/b46aa7aedff449d8ad6624e5787ab49f.png\"},\"evaluateIcon\":{\"name\":\"评价中心\",\"url\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/3bb776cebf5c4835a8ce01b8950aa5ac.png\"},\"waitDelivered\":{\"name\":\"待发货\",\"url\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/2da9a606dd214b2b95327f6ff72db547.png\"}}', 2, 2, '100003');


INSERT INTO `t_account_center_menu` ( `create_time`, `is_deleted`, `update_time`, `menu_name`, `menu_icon_url`, `style_type`, `sort_index`, `p_id`, `hide_menu`, `allow_use`, `default_icon`, `split_flag`, `link_select_item`, `tenant_id`) VALUES ( '2023-11-01 11:35:54', 0, '2023-11-01 11:35:54', '购物车', 'https://cdn.gxnnlyd.com/********/7217f4f7afc94d6a95456106fee35ce9.png', 2, 9, 0, 1, 1, 'http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/%E5%9B%BE%E6%A0%87/gouwuche.png', 0, '{"name":"购物车","id":4,"type":0,"url":"/pages/index/index","append":"shopCar"}', '100003');
INSERT INTO `t_account_center_menu` ( `create_time`, `is_deleted`, `update_time`, `menu_name`, `menu_icon_url`, `style_type`, `sort_index`, `p_id`, `hide_menu`, `allow_use`, `default_icon`, `split_flag`, `link_select_item`, `tenant_id`) VALUES ( '2023-11-01 11:35:54', 0, '2023-11-01 11:35:54', '地址管理', 'https://cdn.gxnnlyd.com/********/0e34a13bbafd44fab639da4e9f2a9ad2.png', 2, 11, 0, 1, 1, 'https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/708afcdfc62443938c9f07ef5d07daf5.png', 0, '{"name":"地址管理","id":7,"type":0,"url":"/pages/address/address"}', '100003');
INSERT INTO `t_account_center_menu` ( `create_time`, `is_deleted`, `update_time`, `menu_name`, `menu_icon_url`, `style_type`, `sort_index`, `p_id`, `hide_menu`, `allow_use`, `default_icon`, `split_flag`, `link_select_item`, `tenant_id`) VALUES ( '2023-11-01 11:35:54', 0, '2023-11-01 11:35:54', '设置', 'https://cdn.gxnnlyd.com/********/efb453dceeb24267925448767e152c20.png', 2, 12, 0, 1, 1, 'https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/80c68ba092174f3ea2067adf8944dbb6.png', 0, '{"name":"设置","id":9,"type":0,"url":"/pages/mySetting/mySetting"}', '100003');
INSERT INTO `t_account_center_menu` ( `create_time`, `is_deleted`, `update_time`, `menu_name`, `menu_icon_url`, `style_type`, `sort_index`, `p_id`, `hide_menu`, `allow_use`, `default_icon`, `split_flag`, `link_select_item`, `tenant_id`) VALUES ( '2023-11-01 11:35:54', 0, '2023-11-01 11:35:54', '通惠证', 'https://cdn.gxnnlyd.com/100001/********/e28deac6c9644c419bc8b50ca3beb5f0.png', 2, 0, 0, 1, 0, '', 0, '{"id":13,"name":"通惠证","type":0,"url":"/modules/pages/myCoupon/myCoupon","append":"modules"}', '100003');
INSERT INTO `t_account_center_menu` ( `create_time`, `is_deleted`, `update_time`, `menu_name`, `menu_icon_url`, `style_type`, `sort_index`, `p_id`, `hide_menu`, `allow_use`, `default_icon`, `split_flag`, `link_select_item`, `tenant_id`) VALUES ( '2023-11-01 11:35:54', 0, '2023-11-01 11:35:54', '我的佣金', 'https://cdn.gxnnlyd.com/100001/********/2849496b91a544b9a2087c31d2942caa.png', 2, 0, 0, 1, 0, '', 0, '{"id":14,"name":"我的佣金","type":0,"url":"/modules/pages/myCommission/myCommission","append":"modules"}', '100003');
INSERT INTO `t_account_center_menu` (`create_time`, `is_deleted`, `update_time`, `menu_name`, `menu_icon_url`, `style_type`, `sort_index`, `p_id`, `hide_menu`, `allow_use`, `default_icon`, `split_flag`, `link_select_item`, `tenant_id`) VALUES ( '2023-11-01 11:35:54', 0, '2023-11-01 11:35:54', '积分商城', 'https://cdn.gxnnlyd.com/100001/********/534f1ef71fad4c56ad31d3c4c902ef7f.png', 2, 0, 0, 1, 0, '', 0, '{"id":15,"name":"积分商城","type":0,"url":"/modules/pages/pointsMall/pointsMall","append":"modules"}', '100003');
INSERT INTO `t_account_center_menu` ( `create_time`, `is_deleted`, `update_time`, `menu_name`, `menu_icon_url`, `style_type`, `sort_index`, `p_id`, `hide_menu`, `allow_use`, `default_icon`, `split_flag`, `link_select_item`, `tenant_id`) VALUES ( '2023-11-01 11:35:54', 0, '2023-11-01 11:35:54', '我的团队', 'https://cdn.gxnnlyd.com/100001/********/892ab2397e004b0387624a69d1da2e09.png', 2, 0, 0, 1, 0, '', 0, '{"id":16,"name":"我的团队","type":0,"url":"/modules/pages/myTeam/myTeam","append":"modules"}', '100003');
INSERT INTO `t_account_center_menu` (`create_time`, `is_deleted`, `update_time`, `menu_name`, `menu_icon_url`, `style_type`, `sort_index`, `p_id`, `hide_menu`, `allow_use`, `default_icon`, `split_flag`, `link_select_item`, `tenant_id`) VALUES ( '2023-11-01 11:35:54', 0, '2023-11-01 11:35:54', '商家信息', 'https://cdn.gxnnlyd.com/100001/********/7d0adc2312d84ee09a740af6480f0ecd.png', 2, 0, 0, 1, 0, '', 0, '{"id":18,"name":"商家信息","type":0,"url":"/merchant/pages/Information/Information","append":"merchant"}', '100003');
INSERT INTO `t_account_center_menu` (`create_time`, `is_deleted`, `update_time`, `menu_name`, `menu_icon_url`, `style_type`, `sort_index`, `p_id`, `hide_menu`, `allow_use`, `default_icon`, `split_flag`, `link_select_item`, `tenant_id`) VALUES ( '2023-11-01 11:35:54', 0, '2023-11-01 11:35:54', '客服', 'https://cdn.gxnnlyd.com/********/23fce3da2cd64f9fb02ff3ca60787b8f.png', 2, 0, 0, 1, 0, '', 0, '{"id":8,"name":"客服","type":0,"url":"","append":""}', '100003');


update t_integral_activity set tenant_id = '100003' , shop_id = '***********';

update t_integral_product set tenant_id = '100003' , shop_id = '***********';

update t_integral_rule set tenant_id = '100003' , shop_id = '***********';


INSERT INTO `t_member_level` (`id`, `update_time`, `create_time`, `is_deleted`, `disable`, `create_user_id`, `create_user_name`, `member_level`, `level`, `effective_time`, `member_card_name`, `legal_right`, `point_exchange`, `payment_amount`, `membercard_name`, `add_method`, `discount`, `remark`, `default_level`, `level_code`, `tenant_id`, `send_status`) VALUES ('1687344338591420544', '2023-08-04 14:07:06', '2023-08-04 14:06:54', 0, 0, NULL, NULL, '默认会员', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, b'1', NULL, '100003', NULL);


INSERT INTO `t_member_level_rights` ( `create_time`, `update_time`, `type`, `name`, `power_explain`, `enable`, `icon`, `is_delete`, `tenant_id`) VALUES ('2022-02-14 15:01:36', '2022-02-14 15:01:40', '商品折扣', '商品折扣', '商品折扣', 0, NULL, 0, '100003');
INSERT INTO `t_member_level_rights` ( `create_time`, `update_time`, `type`, `name`, `power_explain`, `enable`, `icon`, `is_delete`, `tenant_id`) VALUES ('2022-02-19 10:48:31', '2022-02-19 10:48:37', '积分加倍', '积分加倍', '积分加倍', 0, NULL, 0, '100003');


INSERT INTO `t_order_setting` ( `create_time`, `update_time`, `is_deleted`, `flash_order_overtime`, `normal_order_overtime`, `confirm_overtime`, `finish_overtime`, `comment_overtime`, `open_evaluate`, `afs_apply_number`, `merchant_confirm_overtime`, `user_return_overtime`, `kd_app_id`, `kd_app_key`, `payment_model`, `custom_from`, `order_notify`, `tenant_id`, `open_negative_order`, `shop_id`) VALUES ( '2023-05-10 10:40:03', '2023-05-12 14:11:29', 0, 30, 5, 10, 5, 3, 1, 3, 5, 7, NULL, NULL, '1', '[{\"key\":\"备注\",\"type\":\"text\",\"required\":false,\"placeholder\":\"特殊要求请备注\"}]', 1, '100003', 0, NULL);

INSERT INTO `t_order_share_setting` ( `create_time`, `update_time`, `is_deleted`, `background`, `title`, `tenant_id`, `shop_id`) VALUES ( '2023-05-10 16:42:21', '2023-08-13 19:05:26', 0, ' ', '{sname}', '100003', NULL);


update t_platform_account_info set tenant_id = '100003', shop_id = REPLACE(shop_id,'100001','100003')  where is_deleted = 0 and id > 562;


INSERT INTO `t_platform_shop_info` ( `logo_url`, `shop_name`, `status`, `due_time`, `is_due`, `shop_template_id`, `account_id`, `agree_on`, `package_id`, `is_deleted`, `create_time`, `update_time`, `create_join`, `is_privatization_deployment`, `business_hours`, `shop_phone`, `bind_mini_id`, `bind_mp_id`, `shop_template_detail_id`, `certificate_path`, `mch_key`, `mch_id`, `pay_type`, `package_order_id`, `mini_bottom_log`, `tenant_id`, `pc_user_num`) VALUES ( 'https://cdn.gxnnlyd.com/********/65f6f4a613a544de950ef6e57f80c5a0.png', '联易达软件开发', 2, '2023-05-10 13:22:39', 0, 1, 1, 1, 2, 0, '2023-05-10 13:24:57', '2023-11-14 17:45:43', NULL, NULL, '[\"08:30:00\",\"23:39:59\"]', '0771-5654338', NULL, NULL, NULL, NULL, NULL, NULL, 1, 2, 'https://cdn.gxnnlyd.com/********/9648af30b65a43e298d7c92e0d87eddd.png', '100003', '100');


update t_product set tenant_id = '100003', shop_id = REPLACE(shop_id,'100001','100003')  where is_deleted = 0 and id > 1481;


update t_product_buy_in set tenant_id = '100003', shop_id = REPLACE(shop_id,'100001','100003')  where is_deleted = 0 and id >= *****************32;


update t_product_buy_in_item set tenant_id = '100003'  where is_deleted = 0 and id >= 1719701672100139009;


update t_product_sec_unit set tenant_id = '100003'  where is_deleted = 0 and product_id > 1481;


update t_product_stock set tenant_id = '100003'  where is_deleted = 0 and product_id > 1481;


update t_product_unit set tenant_id = '100003'  where is_deleted = 0 and id >= 1719640080167809025;


update t_sale_mode set tenant_id = '100003', shop_id = REPLACE(shop_id,'100001','100003')  where is_deleted = 0 and id >= 124;


INSERT INTO `t_shop_commission_rule` (`id`, `create_time`, `update_time`, `is_deleted`, `create_user_name`, `create_user_id`, `last_modify_user_id`, `last_modify_user_name`, `tenant_id`, `open_flag`, `rule_type`, `parent_receive`, `above_parent_receive`, `min_cash_amount`, `max_cash_amount`, `cash_rate`, `cash_times`, `min_pay_amount`) VALUES (1719157523638702149, '2023-10-31 09:01:09', '2023-10-31 09:01:09', 0, NULL, NULL, NULL, NULL, '100003', 1, 101, 2.0000, 1.0000, 5.00, 50000.00, 3.0000, 3, NULL);


INSERT INTO `t_shop_guide_page` ( `url`, `path`, `link`, `app_id`, `type`, `create_time`, `update_time`, `is_deleted`, `tenant_id`, `shop_id`) VALUES ('https://cdn.gxnnlyd.com/20230512/f650c3b0b3884fbf879814f65b891257.jpg', 'pages/index/index', '{\"id\":null,\"type\":0,\"name\":\"\",\"url\":\"\",\"append\":\"\"}', NULL, 0, '2023-05-12 12:10:33', '2023-08-13 19:05:26', 0, '100003', '***********');


INSERT INTO `t_shop_guide_page_switch` ( `is_open`, `create_time`, `update_time`, `is_deleted`, `tenant_id`, `shop_id`) VALUES ( 1, '2023-05-10 14:35:45', '2023-08-04 14:22:53', 0, '100003', '***********');


update t_shop_pass_ticket set tenant_id = '100003'  where is_deleted = 0;


update t_shops_category set tenant_id = '100003'  where is_deleted = 0;


update t_shops_partner set tenant_id = '100003', shop_id = REPLACE(shop_id,'100001','100003')  where is_deleted = 0 and id >= 3;


update t_shops_partner set  prohibit_status = 0, main_flag = 1, shop_id = '***********'  where is_deleted = 0 and id = 1719623934999842818;


update t_shops_renovation_template set tenant_id = '100003', shop_id = REPLACE(shop_id,'100001','100003')  where is_deleted = 0 and tenant_id = '100001';


UPDATE `t_shops_renovation_plugin` SET `plugin_properties` = '[{\"icon\":\"\",\"value\":\"NavBar\",\"label\":\"底部导航\",\"id\":1698809755137,\"formData\":{\"menuList\":[{\"sortIndex\":0,\"selectedIconPath\":\"https://cdn.gxnnlyd.com/********/6d76300b594e4c1cac4ad670481a6e8a.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"\",\"linkSelectItem\":\"\",\"isHome\":true,\"text\":\"首页\",\"id\":\"1\",\"iconPath\":\"https://cdn.gxnnlyd.com/********/c63bb281399942a4bbfc9c732ab1cef4.png\",\"type\":5,\"linkName\":\"\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/home_page.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/home_page1.png\",\"getFrom\":\"bottomNav\"},{\"sortIndex\":1,\"selectedIconPath\":\"https://cdn.gxnnlyd.com/********/99a8867a791c4f358ed479c4dd7d2a1c.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"mall\",\"linkSelectItem\":\"\",\"isHome\":false,\"text\":\"分类\",\"id\":19,\"iconPath\":\"https://cdn.gxnnlyd.com/********/ee199fb71111424486e0fb8e2ea211d2.png\",\"type\":0,\"linkName\":\"商家分类\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_mall1.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_mall.png\",\"getFrom\":\"bottomNav\"},{\"sortIndex\":2,\"selectedIconPath\":\"https://cdn.gxnnlyd.com/********/23d7434948b54ea9871a449736f485e7.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"shopCar\",\"linkSelectItem\":\"\",\"isHome\":false,\"text\":\"购物车\",\"id\":4,\"iconPath\":\"https://cdn.gxnnlyd.com/********/fdab071018744b4ca84b4aab8ee27e8e.png\",\"type\":0,\"linkName\":\"购物车\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_cart1.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_cart.png\",\"getFrom\":\"bottomNav\"},{\"sortIndex\":3,\"selectedIconPath\":\"https://cdn.gxnnlyd.com/********/3f434c5a607749f49dd13276a9886a23.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"me\",\"linkSelectItem\":\"\",\"isHome\":false,\"text\":\"我的\",\"id\":3,\"iconPath\":\"https://cdn.gxnnlyd.com/********/95b069b2a45147ccbb0f22877cb3bd92.png\",\"type\":0,\"linkName\":\"个人中心\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/my1.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/my.png\",\"getFrom\":\"bottomNav\"}],\"defaultColor\":\"#7A7E83\",\"selectColor\":\"#F64E3F\",\"codeStyle\":1}}]', `tenant_id` = '100003', `shop_id` = '***********' WHERE `id` = 1;



UPDATE `t_shops_renovation_plugin` SET  `plugin_properties` = '[{\"icon\":\"\",\"value\":\"NavBar\",\"label\":\"云影生态\",\"id\":1695888877820,\"formData\":{\"menuList\":[{\"sortIndex\":0,\"selectedIconPath\":\"https://cdn.gxnnlyd.com/********/6d76300b594e4c1cac4ad670481a6e8a.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"\",\"linkSelectItem\":\"\",\"isHome\":true,\"text\":\"首页\",\"id\":\"229\",\"iconPath\":\"https://cdn.gxnnlyd.com/********/c63bb281399942a4bbfc9c732ab1cef4.png\",\"type\":5,\"linkName\":\"\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/home_page.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/home_page1.png\",\"getFrom\":\"bottomNav\"},{\"sortIndex\":1,\"selectedIconPath\":\"https://cdn.gxnnlyd.com/********/99a8867a791c4f358ed479c4dd7d2a1c.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"mall\",\"linkSelectItem\":\"\",\"isHome\":false,\"text\":\"分类\",\"id\":114,\"iconPath\":\"https://cdn.gxnnlyd.com/********/ee199fb71111424486e0fb8e2ea211d2.png\",\"type\":2,\"linkName\":\"云影生态\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_mall1.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_mall.png\",\"getFrom\":\"bottomNav\"},{\"sortIndex\":2,\"selectedIconPath\":\"https://cdn.gxnnlyd.com/********/23d7434948b54ea9871a449736f485e7.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"shopCar\",\"linkSelectItem\":\"\",\"isHome\":false,\"text\":\"购物车\",\"id\":4,\"iconPath\":\"https://cdn.gxnnlyd.com/********/fdab071018744b4ca84b4aab8ee27e8e.png\",\"type\":0,\"linkName\":\"购物车\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_cart1.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_cart.png\",\"getFrom\":\"bottomNav\"},{\"sortIndex\":3,\"selectedIconPath\":\"https://cdn.gxnnlyd.com/********/3f434c5a607749f49dd13276a9886a23.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"me\",\"linkSelectItem\":\"\",\"isHome\":false,\"text\":\"我的\",\"id\":3,\"iconPath\":\"https://cdn.gxnnlyd.com/********/95b069b2a45147ccbb0f22877cb3bd92.png\",\"type\":0,\"linkName\":\"个人中心\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/my1.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/my.png\",\"getFrom\":\"bottomNav\"}],\"defaultColor\":\"#7A7E83\",\"selectColor\":\"#F64E3F\",\"codeStyle\":1}}]' WHERE `id` = 62;



update t_shops_renovation_page set tenant_id = '100003', shop_id = REPLACE(shop_id,'100001','100003')  where is_deleted = 0 and tenant_id = '100001' and id != 221;


update t_shops_renovation_assembly set tenant_id = '100003', shop_id = REPLACE(shop_id,'100001','100003')  where is_deleted = 0 and tenant_id = '100001' and page_id != 221;

-- 71
INSERT INTO `t_shops_renovation_template` ( `is_deleted`, `type`, `colour`, `online_status`, `create_time`, `update_time`, `operator_id`, `operator_name`, `is_dev_template`, `name`, `is_copy_template`, `tenant_id`, `shop_id`) VALUES ( '0', NULL, '1', '1', '2023-05-10 13:30:12', '2023-05-10 13:30:12', NULL, NULL, '0', '首页', NULL, '100001', '10000100001');

-- 1——263
INSERT INTO `t_shops_renovation_page` (`template_id`, `is_deleted`, `page_name`, `create_time`, `update_time`, `operator_id`, `operator_name`, `is_def`, `copy_plugin_flag`, `type`, `model_id`, `tenant_id`, `shop_id`) VALUES (71, '0', '首页', '2023-05-10 11:52:51', '2023-05-10 11:53:20', NULL, NULL, '1', NULL, NULL, NULL, '100001', '10000100001');
-- 219——264
INSERT INTO `t_shops_renovation_page` (  `template_id`, `is_deleted`, `page_name`, `create_time`, `update_time`, `operator_id`, `operator_name`, `is_def`, `copy_plugin_flag`, `type`, `model_id`, `tenant_id`, `shop_id`) VALUES ( 71, '0', '易达云系列', '2023-05-12 10:59:42', '2023-05-12 10:59:49', NULL, NULL, '0', NULL, NULL, NULL, '100001', '10000100001');
-- 220——265
INSERT INTO `t_shops_renovation_page` ( `template_id`, `is_deleted`, `page_name`, `create_time`, `update_time`, `operator_id`, `operator_name`, `is_def`, `copy_plugin_flag`, `type`, `model_id`, `tenant_id`, `shop_id`) VALUES ( 71, '0', '海翔软件', '2023-05-12 10:59:50', '2023-05-12 10:59:57', NULL, NULL, '0', NULL, NULL, NULL, '100001', '10000100001');
-- 222——266
INSERT INTO `t_shops_renovation_page` ( `template_id`, `is_deleted`, `page_name`, `create_time`, `update_time`, `operator_id`, `operator_name`, `is_def`, `copy_plugin_flag`, `type`, `model_id`, `tenant_id`, `shop_id`) VALUES ( 71, '0', '企业客服', '2023-05-12 11:05:33', '2023-08-07 01:36:04', NULL, NULL, '0', NULL, NULL, NULL, '100001', '10000100001');
-- 223——267
INSERT INTO `t_shops_renovation_page` ( `template_id`, `is_deleted`, `page_name`, `create_time`, `update_time`, `operator_id`, `operator_name`, `is_def`, `copy_plugin_flag`, `type`, `model_id`, `tenant_id`, `shop_id`) VALUES ( 71, '0', '科荣软件', '2023-05-12 11:06:30', '2023-05-12 11:06:39', NULL, NULL, '0', NULL, NULL, NULL, '100001', '10000100001');
-- 224——268
INSERT INTO `t_shops_renovation_page` (  `template_id`, `is_deleted`, `page_name`, `create_time`, `update_time`, `operator_id`, `operator_name`, `is_def`, `copy_plugin_flag`, `type`, `model_id`, `tenant_id`, `shop_id`) VALUES ( 71, '0', '单据审批流', '2023-05-12 11:07:14', '2023-05-12 11:07:33', NULL, NULL, '0', NULL, NULL, NULL, '100001', '10000100001');
-- 225——269
INSERT INTO `t_shops_renovation_page` ( `template_id`, `is_deleted`, `page_name`, `create_time`, `update_time`, `operator_id`, `operator_name`, `is_def`, `copy_plugin_flag`, `type`, `model_id`, `tenant_id`, `shop_id`) VALUES (  71, '0', '定制开发', '2023-05-12 11:08:46', '2023-05-12 11:08:54', NULL, NULL, '0', NULL, NULL, NULL, '100001', '10000100001');
-- 226——270
INSERT INTO `t_shops_renovation_page` ( `template_id`, `is_deleted`, `page_name`, `create_time`, `update_time`, `operator_id`, `operator_name`, `is_def`, `copy_plugin_flag`, `type`, `model_id`, `tenant_id`, `shop_id`) VALUES ( 71, '0', '专属服务', '2023-05-12 11:09:39', '2023-05-12 11:09:48', NULL, NULL, '0', NULL, NULL, NULL, '100001', '10000100001');
-- 228——271
INSERT INTO `t_shops_renovation_page` (`template_id`, `is_deleted`, `page_name`, `create_time`, `update_time`, `operator_id`, `operator_name`, `is_def`, `copy_plugin_flag`, `type`, `model_id`, `tenant_id`, `shop_id`) VALUES (71, '0', '配套产品', '2023-05-12 12:00:06', '2023-05-12 12:00:13', NULL, NULL, '0', NULL, NULL, NULL, '100001', '10000100001');


INSERT INTO `t_shops_renovation_plugin` (`template_id`, `plugin_properties`, `is_deleted`, `create_time`, `update_time`, `operator_id`, `operator_name`, `plugin_name_cn`, `is_mandatory`, `is_selection`, `plugin_name_en`, `spare`, `copy_plugin_flag`, `tenant_id`, `shop_id`) VALUES (71, '[{\"icon\":\"\",\"value\":\"NavBar\",\"label\":\"底部导航\",\"id\":1698809755137,\"formData\":{\"menuList\":[{\"sortIndex\":0,\"selectedIconPath\":\"https://cdn.gxnnlyd.com/********/6d76300b594e4c1cac4ad670481a6e8a.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"\",\"linkSelectItem\":\"\",\"isHome\":true,\"text\":\"首页\",\"id\":\"263\",\"iconPath\":\"https://cdn.gxnnlyd.com/********/c63bb281399942a4bbfc9c732ab1cef4.png\",\"type\":5,\"linkName\":\"\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/home_page.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/home_page1.png\",\"getFrom\":\"bottomNav\"},{\"sortIndex\":1,\"selectedIconPath\":\"https://cdn.gxnnlyd.com/********/99a8867a791c4f358ed479c4dd7d2a1c.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"mall\",\"linkSelectItem\":\"\",\"isHome\":false,\"text\":\"分类\",\"id\":19,\"iconPath\":\"https://cdn.gxnnlyd.com/********/ee199fb71111424486e0fb8e2ea211d2.png\",\"type\":0,\"linkName\":\"商家分类\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_mall1.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_mall.png\",\"getFrom\":\"bottomNav\"},{\"sortIndex\":2,\"selectedIconPath\":\"https://cdn.gxnnlyd.com/********/23d7434948b54ea9871a449736f485e7.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"shopCar\",\"linkSelectItem\":\"\",\"isHome\":false,\"text\":\"购物车\",\"id\":4,\"iconPath\":\"https://cdn.gxnnlyd.com/********/fdab071018744b4ca84b4aab8ee27e8e.png\",\"type\":0,\"linkName\":\"购物车\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_cart1.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_cart.png\",\"getFrom\":\"bottomNav\"},{\"sortIndex\":3,\"selectedIconPath\":\"https://cdn.gxnnlyd.com/********/3f434c5a607749f49dd13276a9886a23.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"me\",\"linkSelectItem\":\"\",\"isHome\":false,\"text\":\"我的\",\"id\":3,\"iconPath\":\"https://cdn.gxnnlyd.com/********/95b069b2a45147ccbb0f22877cb3bd92.png\",\"type\":0,\"linkName\":\"个人中心\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/my1.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/my.png\",\"getFrom\":\"bottomNav\"}],\"defaultColor\":\"#7A7E83\",\"selectColor\":\"#F64E3F\",\"codeStyle\":1}}]', '0', '2023-05-10 09:47:16', '2023-11-01 11:36:08', NULL, NULL, '底部导航', '0', '1', 'navBar', NULL, NULL, '100001', '10000100001');


INSERT INTO `t_shops_renovation_assembly` (`page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES ( 264, '0', '{\"icon\":\"shangpin\",\"value\":\"Goods\",\"label\":\"商品\",\"id\":1683860446663,\"formData\":{\"ponentType\":2,\"goods\":[{\"id\":\"1177\",\"img\":\"https://cdn.gxnnlyd.com/20230512/0d3262ec031b425f9e1bc1887117d00e.png\",\"name\":\"易达云APP\",\"price\":\"365.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/0d3262ec031b425f9e1bc1887117d00e.png\",\"saleMode\":\"111\"},{\"id\":\"1175\",\"img\":\"https://cdn.gxnnlyd.com/20230512/93ba0ed913b74066ac0174ed7099556a.png\",\"name\":\"易达云·单一单据流程设置/个\",\"price\":\"1800.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/93ba0ed913b74066ac0174ed7099556a.png\",\"saleMode\":\"111\"},{\"id\":\"1176\",\"img\":\"https://cdn.gxnnlyd.com/20230512/479fba55c75c42a287a14e29dab8b7bf.png\",\"name\":\"易达云商城\",\"price\":\"2880.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/479fba55c75c42a287a14e29dab8b7bf.png\",\"saleMode\":\"111\"},{\"id\":\"1173\",\"img\":\"https://cdn.gxnnlyd.com/20230512/961fb167974e4c6fa0a0918239a23a1b.png\",\"name\":\"易达云·A3标准版\",\"price\":\"2880.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/961fb167974e4c6fa0a0918239a23a1b.png\",\"saleMode\":\"111\"},{\"id\":\"1174\",\"img\":\"https://cdn.gxnnlyd.com/20230512/9ccc021e7d294b20b49fcb8e6228873b.png\",\"name\":\"易达云·所有单据流程设计模块\",\"price\":\"9800.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/9ccc021e7d294b20b49fcb8e6228873b.png\",\"saleMode\":\"111\"},{\"id\":\"1172\",\"img\":\"https://cdn.gxnnlyd.com/20230512/1b63dc7aa7f74884a6b0f9794ac4c194.png\",\"name\":\"易达云·A9专业版\",\"price\":\"12800.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/1b63dc7aa7f74884a6b0f9794ac4c194.png\",\"saleMode\":\"111\"}],\"listStyle\":\"goods-style--three\",\"pagePadding\":17,\"titleStyle\":1,\"goodPadding\":12,\"goodsStyle\":\"is-none\",\"angle\":\"is-straight\",\"textStyle\":\"is-bold\",\"firstCatList\":[],\"currentCategoryId\":\"\",\"showContent\":{\"nameShow\":true,\"priceShow\":true,\"buttonShow\":true,\"buttonStyle\":1,\"buttonText\":\"\",\"tagShow\":true,\"tagStyle\":2},\"goodsCount\":6,\"goodsTemp\":{\"id\":1,\"name\":\"商品名称\",\"saleDescribe\":\"商品描述\",\"img\":\"https://qiniu-app.qtshe.com/u391.png\",\"endTime\":\"20:15:14\",\"price\":99,\"guide\":219,\"soldCount\":10,\"inventory\":120,\"deliveryTime\":\"06月24日 14:00\"}}}', '2023-05-12 11:01:43', '2023-05-12 11:01:43', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES ( 265, '0', '{\"icon\":\"shangpin\",\"value\":\"Goods\",\"label\":\"商品\",\"id\":1683860658247,\"formData\":{\"ponentType\":2,\"goods\":[{\"id\":\"1182\",\"img\":\"https://cdn.gxnnlyd.com/20230512/d544a9bc5311445dab9cdb7a7568afdc.jpg\",\"name\":\"（套装）海翔D5-商超版\",\"price\":\"1800.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/d544a9bc5311445dab9cdb7a7568afdc.jpg,https://cdn.gxnnlyd.com/20230512/203833286a284d0f9eb9986bf4df199b.png\",\"saleMode\":\"111\"},{\"id\":\"1181\",\"img\":\"https://cdn.gxnnlyd.com/20230512/90b0fd619c9b4b2aa97d5d4bf43b0ef7.jpg\",\"name\":\"（套装）海翔医药版-门店版\",\"price\":\"2500.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/90b0fd619c9b4b2aa97d5d4bf43b0ef7.jpg,https://cdn.gxnnlyd.com/20230512/42b0946cce2d40e398f7c33de665e012.jpg\",\"saleMode\":\"111\"},{\"id\":\"1179\",\"img\":\"https://cdn.gxnnlyd.com/20230512/2dfbe4284d2e4df2835ba3d9ef26d762.png\",\"name\":\"（套装）海翔医药版-单体版\",\"price\":\"2800.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/2dfbe4284d2e4df2835ba3d9ef26d762.png,https://cdn.gxnnlyd.com/20230512/843fb0a1c3f041e895408b3a4fec4263.jpg,https://cdn.gxnnlyd.com/20230512/8788e06d30414de38cf3fa359907e1e5.jpg,https://cdn.gxnnlyd.com/20230512/51fb7b37e7374b40b3e79949cca2fdab.jpg\",\"saleMode\":\"111\"},{\"id\":\"1178\",\"img\":\"https://cdn.gxnnlyd.com/20230512/bd4fb260a7b3481db24b69e093b557b4.png\",\"name\":\"海翔药业D8管理系统\",\"price\":\"2980.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/bd4fb260a7b3481db24b69e093b557b4.png\",\"saleMode\":\"111\"},{\"id\":\"1185\",\"img\":\"https://cdn.gxnnlyd.com/20230512/c78c2597934f4b02989dadb279cea2ad.jpg\",\"name\":\"（套装猫）海翔D5-企业版\",\"price\":\"3600.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/c78c2597934f4b02989dadb279cea2ad.jpg,https://cdn.gxnnlyd.com/20230512/f749a4afca9c4d95afdba2e91eb2e980.jpg,https://cdn.gxnnlyd.com/20230512/68c2e69df1e049bbbaf0d116fa934e10.jpg,https://cdn.gxnnlyd.com/20230512/e17abb0984784374bc3d0429d4b44d0c.jpg,https://cdn.gxnnlyd.com/20230512/e7f6b43bfa7649199e3d19204e4e3f1b.jpg,https://cdn.gxnnlyd.com/20230512/95afbeb2c13a490f8441857863a03475.jpg\",\"saleMode\":\"111\"},{\"id\":\"1180\",\"img\":\"https://cdn.gxnnlyd.com/20230512/3cb52aef115b46fab75516c4d2b4405b.jpg\",\"name\":\"（套装）海翔医药版-连锁版\",\"price\":\"4200.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/3cb52aef115b46fab75516c4d2b4405b.jpg,https://cdn.gxnnlyd.com/20230512/c214f41383fd45f0b56ff019e285ae37.jpg\",\"saleMode\":\"111\"}],\"listStyle\":\"goods-style--three\",\"pagePadding\":18,\"titleStyle\":1,\"goodPadding\":2,\"goodsStyle\":\"is-none\",\"angle\":\"is-straight\",\"textStyle\":\"is-bold\",\"firstCatList\":[],\"currentCategoryId\":\"\",\"showContent\":{\"nameShow\":true,\"priceShow\":true,\"buttonShow\":true,\"buttonStyle\":1,\"buttonText\":\"\",\"tagShow\":true,\"tagStyle\":3},\"goodsCount\":6,\"goodsTemp\":{\"id\":1,\"name\":\"商品名称\",\"saleDescribe\":\"商品描述\",\"img\":\"https://qiniu-app.qtshe.com/u391.png\",\"endTime\":\"20:15:14\",\"price\":99,\"guide\":219,\"soldCount\":10,\"inventory\":120,\"deliveryTime\":\"06月24日 14:00\"}}}', '2023-05-12 12:11:05', '2023-05-12 12:11:05', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES ( 267, '0', '{\"icon\":\"shangpin\",\"value\":\"Goods\",\"label\":\"商品\",\"id\":1683860808631,\"formData\":{\"ponentType\":2,\"goods\":[{\"id\":\"1190\",\"img\":\"https://cdn.gxnnlyd.com/20230512/40d4a0da971e4085a50f17c59ae3f07a.jpg\",\"name\":\"（套装）科荣套装-标准版\",\"price\":\"2480.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/40d4a0da971e4085a50f17c59ae3f07a.jpg\",\"saleMode\":\"111\"},{\"id\":\"1191\",\"img\":\"https://cdn.gxnnlyd.com/20230512/34c425db266c46a7ab7bd529ee462f50.jpg\",\"name\":\"（套装）科荣套装-专业版\",\"price\":\"3800.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/34c425db266c46a7ab7bd529ee462f50.jpg\",\"saleMode\":\"111\"}],\"listStyle\":\"goods-style--three\",\"pagePadding\":18,\"titleStyle\":1,\"goodPadding\":2,\"goodsStyle\":\"is-none\",\"angle\":\"is-straight\",\"textStyle\":\"is-bold\",\"firstCatList\":[],\"currentCategoryId\":\"\",\"showContent\":{\"nameShow\":true,\"priceShow\":true,\"buttonShow\":true,\"buttonStyle\":1,\"buttonText\":\"\",\"tagShow\":true,\"tagStyle\":3},\"goodsCount\":2,\"goodsTemp\":{\"id\":1,\"name\":\"商品名称\",\"saleDescribe\":\"商品描述\",\"img\":\"https://qiniu-app.qtshe.com/u391.png\",\"endTime\":\"20:15:14\",\"price\":99,\"guide\":219,\"soldCount\":10,\"inventory\":120,\"deliveryTime\":\"06月24日 14:00\"}}}', '2023-05-12 12:11:13', '2023-05-12 12:11:13', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (269, '0', '{\"icon\":\"shangpin\",\"value\":\"Goods\",\"label\":\"商品\",\"id\":1683860939783,\"formData\":{\"ponentType\":2,\"goods\":[{\"id\":\"1196\",\"img\":\"https://cdn.gxnnlyd.com/20230512/1f3dfc69b9b34b759782528e9c5dcc26.png\",\"name\":\"小程序定制开发\",\"price\":\"8000.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/1f3dfc69b9b34b759782528e9c5dcc26.png,https://cdn.gxnnlyd.com/20230512/f430a89525c947f1a7c69cf23392094c.png,https://cdn.gxnnlyd.com/20230512/099b0d6eb9d64d609642357c4f2dab5d.png\",\"saleMode\":\"111\"},{\"id\":\"1195\",\"img\":\"https://cdn.gxnnlyd.com/20230512/26e951a4a97d45fd92fc8f24e68f8952.png\",\"name\":\"ERP二次定制开发\",\"price\":\"20000.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/26e951a4a97d45fd92fc8f24e68f8952.png,https://cdn.gxnnlyd.com/20230512/db7eee85fbb04b298def3cab76ca6754.jpg,https://cdn.gxnnlyd.com/20230512/fd5de387b35b4d978f34da64df3c1e65.jpg\",\"saleMode\":\"111\"},{\"id\":\"1197\",\"img\":\"https://cdn.gxnnlyd.com/20230512/cf3d8310ad5341848f7a05d2771f6d42.png\",\"name\":\"接口开发（价格面议）\",\"price\":\"88888.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/cf3d8310ad5341848f7a05d2771f6d42.png\",\"saleMode\":\"111\"}],\"listStyle\":\"goods-style--three\",\"pagePadding\":18,\"titleStyle\":1,\"goodPadding\":2,\"goodsStyle\":\"is-none\",\"angle\":\"is-straight\",\"textStyle\":\"is-bold\",\"firstCatList\":[],\"currentCategoryId\":\"\",\"showContent\":{\"nameShow\":true,\"priceShow\":true,\"buttonShow\":true,\"buttonStyle\":1,\"buttonText\":\"\",\"tagShow\":true,\"tagStyle\":2},\"goodsCount\":3,\"goodsTemp\":{\"id\":1,\"name\":\"商品名称\",\"saleDescribe\":\"商品描述\",\"img\":\"https://qiniu-app.qtshe.com/u391.png\",\"endTime\":\"20:15:14\",\"price\":99,\"guide\":219,\"soldCount\":10,\"inventory\":120,\"deliveryTime\":\"06月24日 14:00\"}}}', '2023-05-12 12:11:22', '2023-05-12 12:11:22', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES ( 270, '0', '{\"icon\":\"shangpin\",\"value\":\"Goods\",\"label\":\"商品\",\"id\":1683860991079,\"formData\":{\"ponentType\":2,\"goods\":[{\"id\":\"1192\",\"img\":\"https://cdn.gxnnlyd.com/20230512/1c84ae4a4e9f43a98b109688d556de79.png\",\"name\":\"基础服务--功能培训\",\"price\":\"0.01\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/1c84ae4a4e9f43a98b109688d556de79.png\",\"saleMode\":\"111\"},{\"id\":\"1193\",\"img\":\"https://cdn.gxnnlyd.com/20230512/6cb398e40c7f4b41acf09fb300ca5519.png\",\"name\":\"专属服务-场景应用\",\"price\":\"8000.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/6cb398e40c7f4b41acf09fb300ca5519.png\",\"saleMode\":\"111\"},{\"id\":\"1194\",\"img\":\"https://cdn.gxnnlyd.com/20230512/c771baafdedf43a9ac0cd2217ef37507.png\",\"name\":\"管家服务--落地陪跑\",\"price\":\"30000.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/c771baafdedf43a9ac0cd2217ef37507.png\",\"saleMode\":\"111\"}],\"listStyle\":\"goods-style--three\",\"pagePadding\":18,\"titleStyle\":1,\"goodPadding\":2,\"goodsStyle\":\"is-none\",\"angle\":\"is-straight\",\"textStyle\":\"is-bold\",\"firstCatList\":[],\"currentCategoryId\":\"\",\"showContent\":{\"nameShow\":true,\"priceShow\":true,\"buttonShow\":true,\"buttonStyle\":1,\"buttonText\":\"\",\"tagShow\":true,\"tagStyle\":2},\"goodsCount\":3,\"goodsTemp\":{\"id\":1,\"name\":\"商品名称\",\"saleDescribe\":\"商品描述\",\"img\":\"https://qiniu-app.qtshe.com/u391.png\",\"endTime\":\"20:15:14\",\"price\":99,\"guide\":219,\"soldCount\":10,\"inventory\":120,\"deliveryTime\":\"06月24日 14:00\"}}}', '2023-05-12 12:11:26', '2023-05-12 12:11:26', NULL, NULL, '100001', '10000100001');


INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (271, '0', '{\"icon\":\"shangpin\",\"value\":\"Goods\",\"label\":\"商品\",\"id\":1683864016739,\"formData\":{\"ponentType\":2,\"goods\":[{\"id\":\"1200\",\"img\":\"https://cdn.gxnnlyd.com/20230512/cc58aebcd3ec4efdb1ae120dcfa6635f.jpg\",\"name\":\"条码枪-硬件产品\",\"price\":\"280.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/cc58aebcd3ec4efdb1ae120dcfa6635f.jpg,https://cdn.gxnnlyd.com/20230512/a45af09252f44df89335aa01dd189a1d.jpg,https://cdn.gxnnlyd.com/20230512/4596e33aa140459ba594ecf1785f9fc5.jpg,https://cdn.gxnnlyd.com/20230512/cbd461d1ba27441a856e8834ba469b26.jpg,https://cdn.gxnnlyd.com/20230512/282abec5594d4e899afb044e9c558ead.jpg\",\"saleMode\":\"111\"},{\"id\":\"1199\",\"img\":\"https://cdn.gxnnlyd.com/20230512/1b53374141d747da9dc57aeb5201cb7c.jpg\",\"name\":\"打印机-硬件产品\",\"price\":\"1400.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/1b53374141d747da9dc57aeb5201cb7c.jpg,https://cdn.gxnnlyd.com/20230512/066c0387abee4b1db14ee490e120aa95.jpg,https://cdn.gxnnlyd.com/20230512/6554eb1b980747409030a15053c60995.jpg,https://cdn.gxnnlyd.com/20230512/fc0502dcd4c840949cbedf5fdae8d418.jpg,https://cdn.gxnnlyd.com/20230512/5e3133e8339f4671af19104ab7369cc4.jpg,https://cdn.gxnnlyd.com/20230512/0d65892328c442a0a5bd2e1ee7667b3d.jpg\",\"saleMode\":\"111\"},{\"id\":\"1198\",\"img\":\"https://cdn.gxnnlyd.com/20230512/48c279ceacb24339bcf164fd915b4842.jpg\",\"name\":\"服务器-硬件产品\",\"price\":\"18000.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/48c279ceacb24339bcf164fd915b4842.jpg\",\"saleMode\":\"111\"}],\"listStyle\":\"goods-style--three\",\"pagePadding\":18,\"titleStyle\":1,\"goodPadding\":2,\"goodsStyle\":\"is-none\",\"angle\":\"is-straight\",\"textStyle\":\"is-bold\",\"firstCatList\":[],\"currentCategoryId\":\"\",\"showContent\":{\"nameShow\":true,\"priceShow\":true,\"buttonShow\":true,\"buttonStyle\":1,\"buttonText\":\"\",\"tagShow\":true,\"tagStyle\":2},\"goodsCount\":3,\"goodsTemp\":{\"id\":1,\"name\":\"商品名称\",\"saleDescribe\":\"商品描述\",\"img\":\"https://qiniu-app.qtshe.com/u391.png\",\"endTime\":\"20:15:14\",\"price\":99,\"guide\":219,\"soldCount\":10,\"inventory\":120,\"deliveryTime\":\"06月24日 14:00\"}}}', '2023-05-12 12:11:30', '2023-05-12 12:11:30', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` (`page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES ( 268, '0', '{\"icon\":\"shangpin\",\"value\":\"Goods\",\"label\":\"商品\",\"id\":1683860857311,\"formData\":{\"ponentType\":2,\"goods\":[{\"id\":\"1175\",\"img\":\"https://cdn.gxnnlyd.com/20230512/93ba0ed913b74066ac0174ed7099556a.png\",\"name\":\"易达云·单一单据流程设置/个\",\"price\":\"1800.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/93ba0ed913b74066ac0174ed7099556a.png\",\"saleMode\":\"111\"},{\"id\":\"1174\",\"img\":\"https://cdn.gxnnlyd.com/20230512/9ccc021e7d294b20b49fcb8e6228873b.png\",\"name\":\"易达云·所有单据流程设计模块\",\"price\":\"9800.00\",\"albumPics\":\"https://cdn.gxnnlyd.com/20230512/9ccc021e7d294b20b49fcb8e6228873b.png\",\"saleMode\":\"111\"}],\"listStyle\":\"goods-style--three\",\"pagePadding\":18,\"titleStyle\":1,\"goodPadding\":2,\"goodsStyle\":\"is-none\",\"angle\":\"is-straight\",\"textStyle\":\"is-bold\",\"firstCatList\":[],\"currentCategoryId\":\"\",\"showContent\":{\"nameShow\":true,\"priceShow\":true,\"buttonShow\":true,\"buttonStyle\":1,\"buttonText\":\"\",\"tagShow\":true,\"tagStyle\":2},\"goodsCount\":2,\"goodsTemp\":{\"id\":1,\"name\":\"商品名称\",\"saleDescribe\":\"商品描述\",\"img\":\"https://qiniu-app.qtshe.com/u391.png\",\"endTime\":\"20:15:14\",\"price\":99,\"guide\":219,\"soldCount\":10,\"inventory\":120,\"deliveryTime\":\"06月24日 14:00\"}}}', '2023-05-12 14:02:15', '2023-05-12 14:02:15', NULL, NULL, '100001', '10000100001');


INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES ( 266, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1691927497692,\"formData\":{\"borderWidth\":30,\"layoutWidth\":5,\"layoutHeight\":5,\"showMethod\":7,\"pageMargin\":0,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":5,\"height\":5,\"img\":\"https://cdn.gxnnlyd.com/100001/20230814/4a9db05d4da8471388a4662e641c2bd6.png\",\"link\":{\"id\":8,\"type\":0,\"name\":\"客服\",\"url\":\"\",\"append\":\"\"},\"linkName\":\"\"}]}}', '2023-08-24 10:36:34', '2023-08-24 10:36:34', NULL, NULL, '100001', '10000100001');


INSERT INTO `t_shops_renovation_assembly` (`page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES ( 263, '0', '{\"icon\":\"sousuo\",\"value\":\"Search\",\"label\":\"搜索\",\"id\":1683860436647,\"formData\":{\"showStyle\":\"is-style-one\",\"keyWord\":\"请输入关键词搜索\",\"hotWord\":[\"易达\"]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');


INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES ( 263, '0', '{\"icon\":\"lunbotu\",\"value\":\"GoodSwiper\",\"label\":\"商品轮播图\",\"id\":1683860622343,\"formData\":{\"type\":\"GoodSwiper\",\"radio\":1,\"btmInput\":\"\",\"btnImg\":\"https://qiniu-app.qtshe.com/u391.png\",\"btnlink\":\"\",\"margin\":8,\"swiperList\":[{\"title\":\"\",\"img\":\"https://cdn.gxnnlyd.com/100001/20230808/b780cc8179d84cac9c1784b2ddc57a1f.png\",\"link\":{\"id\":222,\"type\":5,\"name\":\"企业客服\",\"url\":\"/pages/index/custom/custom\",\"append\":222},\"linkName\":\"\"},{\"title\":\"\",\"img\":\"https://cdn.gxnnlyd.com/100001/20230822/586a789dfccd4dec9e2c7d785da022cd.png\",\"link\":{\"id\":1173,\"type\":1,\"name\":\"易达云·A3标准版\",\"url\":\"/subcontract/pages/detail/detail\",\"append\":\"\",\"limitType\":1,\"maxPrice\":2880,\"minPrice\":2880},\"linkName\":\"\"},{\"title\":\"\",\"img\":\"https://cdn.gxnnlyd.com/100001/20230822/0b7087a05c4f4155b3f8c5c1c5179311.png\",\"link\":{\"id\":1176,\"type\":1,\"name\":\"易达·商城小程序\",\"url\":\"/subcontract/pages/detail/detail\",\"append\":\"\",\"limitType\":1,\"maxPrice\":6800,\"minPrice\":6800},\"linkName\":\"\"}],\"padding\":0,\"sidePadding\":14,\"imageStyle\":1,\"imageAngle\":1,\"indicator\":1,\"height\":400,\"shownum\":3,\"interval\":5}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');


INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES ( 263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1691333647879,\"formData\":{\"borderWidth\":0,\"layoutWidth\":6,\"layoutHeight\":6,\"showMethod\":7,\"pageMargin\":15,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":6,\"height\":1,\"img\":\"https://cdn.gxnnlyd.com/100001/20230808/4b02156e84a84ecfa21b37ca7c703236.png\",\"link\":{\"id\":222,\"type\":5,\"name\":\"企业客服\",\"url\":\"/pages/index/custom/custom\",\"append\":222},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1691488430346,\"formData\":{\"borderWidth\":14,\"layoutWidth\":7,\"layoutHeight\":7,\"showMethod\":7,\"pageMargin\":0,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":7,\"height\":1,\"img\":\"https://cdn.gxnnlyd.com/100001/20230808/fa54705ecad9424a811366df6b4122c5.png\",\"link\":{\"id\":\"\",\"type\":0,\"name\":\"\",\"url\":\"\",\"append\":\"\"},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"dianpudaohang\",\"value\":\"StoreNavigation\",\"label\":\"店铺导航\",\"id\":1683861051121,\"formData\":{\"storeNavigations\":[{\"navName\":\"软件产品\",\"fontColor\":\"#333333\",\"navIcon\":\"https://cdn.gxnnlyd.com/100001/20230808/2f5c10c1c6f041d3a5b5bedd8b3d29c2.png\",\"pathLink\":\"\",\"linkUrl\":\"/pages/index/custom/custom\",\"linkName\":\"易达云系列\",\"append\":219,\"type\":5,\"id\":219},{\"navName\":\"专属服务\",\"fontColor\":\"#333333\",\"navIcon\":\"https://cdn.gxnnlyd.com/100001/20230808/00772b74aed840588596d2a90d26fb84.png\",\"pathLink\":\"\",\"linkUrl\":\"/pages/index/custom/custom\",\"linkName\":\"专属服务\",\"append\":226,\"type\":5,\"id\":226},{\"navName\":\"定制开发\",\"fontColor\":\"#333333\",\"navIcon\":\"https://cdn.gxnnlyd.com/100001/20230808/344b390bc2e94ffca4232eb6c7f36745.png\",\"pathLink\":\"\",\"linkUrl\":\"/pages/index/custom/custom\",\"linkName\":\"定制开发\",\"append\":225,\"type\":5,\"id\":225},{\"navName\":\"关于我们\",\"fontColor\":\"#333333\",\"navIcon\":\"https://cdn.gxnnlyd.com/100001/20230808/e6c025463a8949aabac3c55787725e59.png\",\"pathLink\":\"\",\"linkUrl\":\"https://mp.weixin.qq.com/s/NI5yUcpc9YY-bstuhcxLSw\",\"linkName\":\"自定义链接\",\"append\":\"\",\"type\":6,\"id\":999}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1691334941571,\"formData\":{\"borderWidth\":0,\"layoutWidth\":7,\"layoutHeight\":7,\"showMethod\":7,\"pageMargin\":0,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":7,\"height\":7,\"img\":\"https://cdn.gxnnlyd.com/100001/20230822/8da42ac542264f2e9c558c11881a7c2e.jpg\",\"link\":{\"id\":\"\",\"type\":0,\"name\":\"\",\"url\":\"\",\"append\":\"\"},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"zhanweifu\",\"value\":\"BlankPaceholder\",\"label\":\"空白占位\",\"id\":1683862542985,\"formData\":{\"height\":20}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683862561633,\"formData\":{\"borderWidth\":0,\"layoutWidth\":7,\"layoutHeight\":7,\"showMethod\":7,\"pageMargin\":0,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":7,\"height\":1,\"img\":\"https://cdn.gxnnlyd.com/20230512/370995494d024c2882656652a1b3922a.png\",\"link\":{\"id\":219,\"type\":5,\"name\":\"易达云系列\",\"url\":\"/pages/index/custom/custom\",\"append\":219},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` (`page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683862585753,\"formData\":{\"borderWidth\":25,\"layoutWidth\":4,\"layoutHeight\":4,\"showMethod\":7,\"pageMargin\":15,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":4,\"height\":2,\"img\":\"https://cdn.gxnnlyd.com/20230512/37f8787a1579467688af402c788775c2.jpg\",\"link\":{\"id\":1172,\"type\":1,\"name\":\"易达云·A9专业版\",\"url\":\"/subcontract/pages/detail/detail\",\"append\":\"\",\"limitType\":1,\"maxPrice\":22800,\"minPrice\":12800},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` (`page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683862707357,\"formData\":{\"borderWidth\":2,\"layoutWidth\":4,\"layoutHeight\":4,\"showMethod\":7,\"pageMargin\":15,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":4,\"height\":1,\"img\":\"https://cdn.gxnnlyd.com/100001/20230823/4c84ec7a7a9f451bbb9657528e7e3f3b.jpg\",\"link\":{\"id\":1177,\"type\":1,\"name\":\"易达APP\",\"url\":\"/subcontract/pages/detail/detail\",\"append\":\"\",\"limitType\":1,\"maxPrice\":999,\"minPrice\":365},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` (`page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683862766985,\"formData\":{\"borderWidth\":2,\"layoutWidth\":4,\"layoutHeight\":4,\"showMethod\":7,\"pageMargin\":15,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":4,\"height\":1,\"img\":\"https://cdn.gxnnlyd.com/100001/20230823/d5f9cfb9ac824cd5a0a58d3ac94d4a05.jpg\",\"link\":{\"id\":1173,\"type\":1,\"name\":\"易达云·A3标准版\",\"url\":\"/subcontract/pages/detail/detail\",\"append\":\"\",\"limitType\":1,\"maxPrice\":9999,\"minPrice\":2880},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` (`page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683862743146,\"formData\":{\"borderWidth\":2,\"layoutWidth\":4,\"layoutHeight\":4,\"showMethod\":7,\"pageMargin\":15,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":4,\"height\":1,\"img\":\"https://cdn.gxnnlyd.com/100001/20230823/d98e3692c7524106a94c4804fdeeaac5.jpg\",\"link\":{\"id\":1176,\"type\":1,\"name\":\"易达·商城小程序\",\"url\":\"/subcontract/pages/detail/detail\",\"append\":\"\",\"limitType\":1,\"maxPrice\":5800,\"minPrice\":2880},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` (`page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683862779329,\"formData\":{\"borderWidth\":2,\"layoutWidth\":4,\"layoutHeight\":4,\"showMethod\":7,\"pageMargin\":15,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":4,\"height\":1,\"img\":\"https://cdn.gxnnlyd.com/100001/20230823/e5999ff92e7f4c5194d35060cf501952.jpg\",\"link\":{\"id\":1172,\"type\":1,\"name\":\"易达云·A9专业版\",\"url\":\"/subcontract/pages/detail/detail\",\"append\":\"\",\"limitType\":1,\"maxPrice\":29999,\"minPrice\":12800},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"zhanweifu\",\"value\":\"BlankPaceholder\",\"label\":\"空白占位\",\"id\":1683862988706,\"formData\":{\"height\":20}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683862446010,\"formData\":{\"borderWidth\":0,\"layoutWidth\":7,\"layoutHeight\":7,\"showMethod\":7,\"pageMargin\":0,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":7,\"height\":2,\"img\":\"https://cdn.gxnnlyd.com/20230512/523f33bc0dc04e08bf173fe25f530b7e.png\",\"link\":{\"id\":\"\",\"type\":0,\"name\":\"\",\"url\":\"\",\"append\":\"\"},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` (`page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES ( 263, '0', '{\"icon\":\"zhanweifu\",\"value\":\"BlankPaceholder\",\"label\":\"空白占位\",\"id\":1683862816881,\"formData\":{\"height\":10}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` (`page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES ( 263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683862876657,\"formData\":{\"borderWidth\":0,\"layoutWidth\":7,\"layoutHeight\":7,\"showMethod\":7,\"pageMargin\":0,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":7,\"height\":1,\"img\":\"https://cdn.gxnnlyd.com/20230512/2c2777e47b3f43efbb001adbe852dff4.png\",\"link\":{\"id\":224,\"type\":5,\"name\":\"单据审批流\",\"url\":\"/pages/index/custom/custom\",\"append\":224},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` (`page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683862821410,\"formData\":{\"borderWidth\":25,\"layoutWidth\":4,\"layoutHeight\":4,\"showMethod\":7,\"pageMargin\":15,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":4,\"height\":2,\"img\":\"https://cdn.gxnnlyd.com/20230512/a62f2e1aff8f435da2a769f201023313.jpg\",\"link\":{\"id\":1174,\"type\":1,\"name\":\"易达云·所有单据流程设计模块\",\"url\":\"/subcontract/pages/detail/detail\",\"append\":\"\",\"limitType\":0,\"maxPrice\":9800,\"minPrice\":9800},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683862947386,\"formData\":{\"borderWidth\":2,\"layoutWidth\":4,\"layoutHeight\":4,\"showMethod\":7,\"pageMargin\":15,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":4,\"height\":1,\"img\":\"https://cdn.gxnnlyd.com/100001/20230823/0e6a06cc025247e8b54ca34b36c6de90.jpg\",\"link\":{\"id\":1175,\"type\":1,\"name\":\"易达云·单一单据流程设置/个\",\"url\":\"/subcontract/pages/detail/detail\",\"append\":\"\",\"limitType\":0,\"maxPrice\":1800,\"minPrice\":1800},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683862965586,\"formData\":{\"borderWidth\":2,\"layoutWidth\":4,\"layoutHeight\":4,\"showMethod\":7,\"pageMargin\":15,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":4,\"height\":1,\"img\":\"https://cdn.gxnnlyd.com/100001/20230823/9bb69576dfab4331be47e0e0b8c966e1.jpg\",\"link\":{\"id\":1174,\"type\":1,\"name\":\"易达云·所有单据流程设计模块\",\"url\":\"/subcontract/pages/detail/detail\",\"append\":\"\",\"limitType\":0,\"maxPrice\":9800,\"minPrice\":9800},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` (`page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"zhanweifu\",\"value\":\"BlankPaceholder\",\"label\":\"空白占位\",\"id\":1683863359930,\"formData\":{\"height\":20}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES ( 263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683863013601,\"formData\":{\"borderWidth\":0,\"layoutWidth\":7,\"layoutHeight\":7,\"showMethod\":7,\"pageMargin\":0,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":7,\"height\":1,\"img\":\"https://cdn.gxnnlyd.com/20230512/dd7fc5168d994f0aa36ad86f4ad131c6.png\",\"link\":{\"id\":226,\"type\":5,\"name\":\"专属服务\",\"url\":\"/pages/index/custom/custom\",\"append\":226},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` (`page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683863030777,\"formData\":{\"borderWidth\":25,\"layoutWidth\":4,\"layoutHeight\":4,\"showMethod\":7,\"pageMargin\":15,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":4,\"height\":2,\"img\":\"https://cdn.gxnnlyd.com/20230512/e9b8bf4f6a7c4bfa86096d47a1bd9c9e.jpg\",\"link\":{\"id\":1194,\"type\":1,\"name\":\"管家服务--落地陪跑\",\"url\":\"/subcontract/pages/detail/detail\",\"append\":\"\",\"limitType\":0,\"maxPrice\":30000,\"minPrice\":30000},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683863064401,\"formData\":{\"borderWidth\":2,\"layoutWidth\":4,\"layoutHeight\":4,\"showMethod\":7,\"pageMargin\":15,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":4,\"height\":1,\"img\":\"https://cdn.gxnnlyd.com/100001/20230823/********************************.jpg\",\"link\":{\"id\":1192,\"type\":1,\"name\":\"基础服务--功能培训\",\"url\":\"/subcontract/pages/detail/detail\",\"append\":\"\",\"limitType\":0,\"maxPrice\":0.01,\"minPrice\":0.01},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683863110395,\"formData\":{\"borderWidth\":2,\"layoutWidth\":4,\"layoutHeight\":4,\"showMethod\":7,\"pageMargin\":15,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":4,\"height\":1,\"img\":\"https://cdn.gxnnlyd.com/100001/20230823/ffc4e0aa25944d9badbfb03f4b866035.jpg\",\"link\":{\"id\":1193,\"type\":1,\"name\":\"专属服务-场景应用\",\"url\":\"/subcontract/pages/detail/detail\",\"append\":\"\",\"limitType\":0,\"maxPrice\":8000,\"minPrice\":8000},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` (`page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES ( 263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683863132322,\"formData\":{\"borderWidth\":2,\"layoutWidth\":4,\"layoutHeight\":4,\"showMethod\":7,\"pageMargin\":15,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":4,\"height\":1,\"img\":\"https://cdn.gxnnlyd.com/100001/20230823/e1768e0015224bc8b1b6e7460061dfb0.jpg\",\"link\":{\"id\":1194,\"type\":1,\"name\":\"管家服务--落地陪跑\",\"url\":\"/subcontract/pages/detail/detail\",\"append\":\"\",\"limitType\":0,\"maxPrice\":30000,\"minPrice\":30000},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES ( 263, '0', '{\"icon\":\"zhanweifu\",\"value\":\"BlankPaceholder\",\"label\":\"空白占位\",\"id\":1683863240611,\"formData\":{\"height\":20}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` (`page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683863245530,\"formData\":{\"borderWidth\":0,\"layoutWidth\":7,\"layoutHeight\":7,\"showMethod\":7,\"pageMargin\":0,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":7,\"height\":1,\"img\":\"https://cdn.gxnnlyd.com/20230512/53ae763c4ad44fb7ae1652490033dbe5.png\",\"link\":{\"id\":220,\"type\":5,\"name\":\"海翔软件\",\"url\":\"/pages/index/custom/custom\",\"append\":220},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` (`page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683863259530,\"formData\":{\"borderWidth\":25,\"layoutWidth\":4,\"layoutHeight\":4,\"showMethod\":7,\"pageMargin\":15,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":4,\"height\":2,\"img\":\"https://cdn.gxnnlyd.com/20230512/210db75a545f4f5ab89d8212fbe14816.jpg\",\"link\":{\"id\":1178,\"type\":1,\"name\":\"海翔药业D8管理系统\",\"url\":\"/subcontract/pages/detail/detail\",\"append\":\"\",\"limitType\":0,\"maxPrice\":2980,\"minPrice\":2980},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683863321962,\"formData\":{\"borderWidth\":2,\"layoutWidth\":4,\"layoutHeight\":4,\"showMethod\":7,\"pageMargin\":15,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":4,\"height\":1,\"img\":\"https://cdn.gxnnlyd.com/100001/20230823/9e41a75de8d44d058cfd2d93e55687c8.jpg\",\"link\":{\"id\":1179,\"type\":1,\"name\":\"（套装）海翔医药版-单体版\",\"url\":\"/subcontract/pages/detail/detail\",\"append\":\"\",\"limitType\":0,\"maxPrice\":2800,\"minPrice\":2800},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683863342754,\"formData\":{\"borderWidth\":2,\"layoutWidth\":4,\"layoutHeight\":4,\"showMethod\":7,\"pageMargin\":15,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":4,\"height\":1,\"img\":\"https://cdn.gxnnlyd.com/100001/20230823/754ed4b14e9a4022a4796d3126e22ace.jpg\",\"link\":{\"id\":1178,\"type\":1,\"name\":\"海翔药业D8管理系统\",\"url\":\"/subcontract/pages/detail/detail\",\"append\":\"\",\"limitType\":0,\"maxPrice\":2980,\"minPrice\":2980},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"zhanweifu\",\"value\":\"BlankPaceholder\",\"label\":\"空白占位\",\"id\":1683863516018,\"formData\":{\"height\":20}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683863377555,\"formData\":{\"borderWidth\":0,\"layoutWidth\":6,\"layoutHeight\":6,\"showMethod\":7,\"pageMargin\":0,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":6,\"height\":2,\"img\":\"https://cdn.gxnnlyd.com/20230512/d6aed36b114c4b4c9ea3bd7901eba2f3.png\",\"link\":{\"id\":228,\"type\":5,\"name\":\"配套产品\",\"url\":\"/pages/index/custom/custom\",\"append\":228},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683863435778,\"formData\":{\"borderWidth\":0,\"layoutWidth\":2,\"layoutHeight\":1,\"showMethod\":0,\"pageMargin\":15,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":1,\"height\":1,\"img\":\"https://cdn.gxnnlyd.com/20230512/b6087268ea1442a3a880e9acb0984b4e.png\",\"link\":{\"id\":1201,\"type\":1,\"name\":\"（加密）文档防泄露\",\"url\":\"/subcontract/pages/detail/detail\",\"append\":\"\",\"limitType\":0,\"maxPrice\":1500,\"minPrice\":1500},\"linkName\":\"\"},{\"x\":1,\"y\":0,\"width\":1,\"height\":1,\"img\":\"https://cdn.gxnnlyd.com/20230512/65d93ea5ecab4cf491389eabf1f8156f.png\",\"link\":{\"id\":1203,\"type\":1,\"name\":\"路格温湿度系统\",\"url\":\"/subcontract/pages/detail/detail\",\"append\":\"\",\"limitType\":0,\"maxPrice\":1000,\"minPrice\":1000},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683863472210,\"formData\":{\"borderWidth\":12,\"layoutWidth\":7,\"layoutHeight\":7,\"showMethod\":7,\"pageMargin\":0,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":7,\"height\":1,\"img\":\"https://cdn.gxnnlyd.com/20230512/66a213fbd3074349816d748cd9f4760a.png\",\"link\":{\"id\":111,\"type\":2,\"name\":\"联易达产品及服务\",\"url\":\"/pages/index/index\",\"append\":\"mall\"},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');

INSERT INTO `t_shops_renovation_assembly` ( `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`, `shop_id`) VALUES (263, '0', '{\"icon\":\"mofang\",\"value\":\"CubeBox\",\"label\":\"魔方\",\"id\":1683863535754,\"formData\":{\"borderWidth\":30,\"layoutWidth\":7,\"layoutHeight\":7,\"showMethod\":7,\"pageMargin\":10,\"width\":2,\"subEntry\":[{\"x\":0,\"y\":0,\"width\":7,\"height\":2,\"img\":\"https://cdn.gxnnlyd.com/100001/20230813/131804b700a1426ca55c418c88754e22.png\",\"link\":{\"id\":\"\",\"type\":0,\"name\":\"\",\"url\":\"\",\"append\":\"\"},\"linkName\":\"\"}]}}', '2023-10-27 10:12:11', '2023-10-27 10:12:11', NULL, NULL, '100001', '10000100001');


update t_shops_settled set tenant_id = '100003'  where is_deleted = 0 and tenant_id = '100001';

update t_shops_show_category set tenant_id = '100003'  where is_deleted = 0 and tenant_id = '100001';


update t_show_category set tenant_id = '100003', shop_id = REPLACE(shop_id,'100001','100003')  where is_deleted = 0 and tenant_id = '100001' and sale_mode >=124;


update t_supplier set tenant_id = '100003', shop_id = REPLACE(shop_id,'100001','100003')  where is_deleted = 0 and tenant_id = '100001' and id >=650;


INSERT INTO `t_system_conf` ( `param_key`, `param_value`, `status`, `remark`, `is_deleted`, `create_time`, `update_time`, `tenant_id`) VALUES ( 'currentOssType', '1', 1, NULL, 0, '2023-05-10 14:29:24', '2023-11-10 18:01:24', '100003');

INSERT INTO `t_system_conf` ( `param_key`, `param_value`, `status`, `remark`, `is_deleted`, `create_time`, `update_time`, `tenant_id`) VALUES ( 'storage_qiniouyun', '{\"qiniuAccessKey\":\"-Y3maCW91rXVFuLGpAm3JbHwrD9de5xM1uYBYJQr\",\"qiniuBucketName\":\"lydpro\",\"qiniuDomain\":\"https://cdn.gxnnlyd.com\",\"qiniuSecretKey\":\"RTTGC_kOGcI-iMticwa7qz16Ep7QyKo-ev5R87BL\"}', 1, NULL, 0, '2023-11-09 18:05:54', '2023-11-10 18:01:24', '100003');


update t_warehouse set tenant_id = '100003', shop_id = REPLACE(shop_id,'100001','100003')  where is_deleted = 0 and tenant_id = '100001' and id >=1719700891464667138;


INSERT INTO `t_platform_mini_config` (`id`, `is_deleted`, `update_time`, `create_time`, `app_id`, `app_secret`, `tenant_id`) VALUES (1686913470859853943, 0, '2023-08-03 09:35:04', '2023-08-03 09:34:47', 'wx65f0cc6893825777', '1c0d864b6c8cc8e8cd0e913230b2f04b', '100003');


update t_product_show_category a LEFT JOIN t_product b ON a.product_id = b.id set a.tenant_id = b.tenant_id, a.shop_id = b.shop_id WHERE a.is_deleted = 0 AND a.tenant_id != b.tenant_id;

update t_sku_stock a LEFT JOIN t_product b ON a.product_id = b.id set a.tenant_id = b.tenant_id, a.shop_id = b.shop_id WHERE a.is_deleted = 0 AND a.tenant_id != b.tenant_id;

-- select * from t_warehouse where is_deleted = 0 ;














