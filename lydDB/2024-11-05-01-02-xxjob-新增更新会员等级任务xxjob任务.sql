INSERT INTO `xxl_job_group` (`id`, `app_name`, `title`, `order`, `address_type`, `address_list`) VALUES (4, 'xxl-job-executor-account', '用户信息执行器', 3, 1, '*************:9529');

INSERT INTO `xxl_job_info` ( `job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES ( 4, '0 0 0 * * ?', '用户会员等级定时任务', '2024-11-05 17:00:26', '2024-11-05 17:53:18', 'xxl', '', 'FIRST', 'UpdateMiniAccountLevel', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-11-05 17:00:26', '', 1, *************, *************);


	
