#### payment-open

##### nacos配置

version: 0.2
# 数据源
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: root
      password: 123456
      url: ***********************************************************************************************************************************************************************************************************************************************************
  rabbitmq:
    host: 127.0.0.1
    username: admin
    password: 123456
    port: 5671


# 是否启动LCN负载均衡策略(优化选项，开启与否，功能不受影响)
tx-lcn:
  logger:
    enabled: true
    driver-class-name: ${spring.datasource.druid.driver-class-name}
    jdbc-url: ${spring.datasource.druid.url}
    username: ${spring.datasource.druid.username}
    password: ${spring.datasource.druid.password}
  ribbon:
    loadbalancer:
      dtx:
        enabled: true
# tx-manager 的配置地址，可以指定TM集群中的任何一个或多个地址
# tx-manager 下集群策略，每个TC都会从始至终<断线重连>与TM集群保持集群大小个连接。
# TM方，每有TM进入集群，会找到所有TC并通知其与新TM建立连接。
# TC方，启动时按配置与集群建立连接，成功后，会再与集群协商，查询集群大小并保持与所有TM的连接
  client:
    manager-address: 
redis:
  network:
    isintra: false
  intranet:
    host: 127.0.0.1
  outernet:
    host: 127.0.0.1
  port: 6379
  timeout: 3000
  password: '123456'
  database: 0    
  
#过滤表
gruul:
  tenant:
    use_shop: false
    tables:
      - t_payment
      - t_payment_wechat
      - t_payment_record

pay:
  notify: "https://2866458.nnlyd.com/api/payment-open/notify"
  workerId: 1
  datacenterId: 1
  ipsWorkerId: 2
  ipsDatacenterId: 2
  ipsVersion: 2.0.0
  ipsURL: https://api.ips.com.cn
  ipsTradePlatformPaySubUrl: /trade/platform/pay
  ipsNotify: https://2866458.nnlyd.com/api/payment-open/ips_notify
  wxRefundUrl: https://2866458.nnlyd.com/api/payment-open/wx_refund_notify
  transferUrl: https://api.mch.weixin.qq.com/v3/fund-app/mch-transfer/transfer-bills
  queryTransFerUrl: https://api.mch.weixin.qq.com/v3/fund-app/mch-transfer/transfer-bills/out-bill-no/{out_bill_no}
xxl:
  job:
    admin:
      addresses: http://127.0.0.1:8181/xxl-job-admin/
    accessToken:
    executor:
      appname: xxl-job-executor-payment
      address:
      ip: 127.0.0.1
      port: 9530
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30
# Logger Config
logging:
  file: /log/mall/payment-open.log
  level:
    root: info
    com.baomidou: debug
    com.medusa: debug
monitor:
  useRun: true  #是否使用心跳监听
  useLog: true  #是否打印日志
  businessName: ""   #基础库名(baseType=public时可以为空,不填写)
  applicationName: "支付服务"  #服务名称
  baseType: "public"     #基础库类型  public-支撑基础库   business-业务基础库
  serviceType: "universalService" #服务类型    universalService-通用服务  commissionService-定制服务


