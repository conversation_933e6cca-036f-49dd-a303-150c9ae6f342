

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_platform_mini_config
-- ----------------------------
DROP TABLE IF EXISTS `t_platform_mini_config`;
CREATE TABLE `t_platform_mini_config`  (
  `id` bigint NOT NULL COMMENT 'id',
  `is_deleted` tinyint(1) NULL DEFAULT NULL COMMENT '删除状态：0->未删除；1->已删除 ',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `app_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '小程序appId',
  `app_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '小程序appSecret',
  `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '小程序信息配置' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;

