INSERT INTO `xxl_job_group` (`id`, `app_name`, `title`, `order`, `address_type`, `address_list`) VALUES (3, 'xxl-job-executor-logistics', '物流信息执行器', 2, 1, '*************:9528');

INSERT INTO `xxl_job_registry` (`id`, `registry_group`, `registry_key`, `registry_value`, `update_time`) VALUES (23, 'EXECUTOR', 'xxl-job-executor-logistics', '*************:9528', '2024-05-15 20:52:27');

INSERT INTO `xxl_job_info` (`id`, `job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (6, 3, '0 0/5 * * * ?', '查看微信发货是否确认收货，确认则同步程序订单完成确认收货', '2024-05-15 20:35:17', '2024-05-15 20:48:46', 'xxl', '', 'FIRST', 'UpdateWxDeliverStatus', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-15 20:35:17', '', 1, 1715777400000, 1715777700000);
