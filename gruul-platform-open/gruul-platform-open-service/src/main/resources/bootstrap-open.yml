server:
  port: 10200
spring:
  application:
    name: platform-open
  cloud:
    nacos:
      discovery:
        #nacos的ip
        server-addr: 127.0.0.1:8848
        #自己电脑的ip
        ip: 127.0.0.1
        # 注册实例到哪个命名空间
        namespace: ae67bdfd-0fa7-4f10-8a5f-15761be07d7b
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ae67bdfd-0fa7-4f10-8a5f-15761be07d7b
        file-extension: yml
        shared-dataids: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  autoconfigure:
    exclude: org.springframework.cloud.gateway.config.GatewayAutoConfiguration,org.springframework.cloud.gateway.config.GatewayClassPathWarningAutoConfiguration
  profiles:
    active: open
  servlet:
    multipart:
      enabled: false
logging:
  file: /log/mall/platform-open/application.log
  pattern:
    file: '%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID}){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx'
  level:
    com.alibaba.nacos: error
    root: debug
