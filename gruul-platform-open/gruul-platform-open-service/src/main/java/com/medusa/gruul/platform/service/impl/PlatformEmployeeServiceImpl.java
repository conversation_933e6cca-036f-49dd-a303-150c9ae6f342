package com.medusa.gruul.platform.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.constant.enums.SourceTypeEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.platform.api.entity.*;
import com.medusa.gruul.platform.api.model.dto.OutPlatformDepartmentDto;
import com.medusa.gruul.platform.api.model.dto.OutPlatformEmployeeDto;
import com.medusa.gruul.platform.api.model.dto.PlatformEmployeeDto;
import com.medusa.gruul.platform.api.model.vo.EmployeeVo;
import com.medusa.gruul.platform.api.model.vo.PlatformPositionVo;
import com.medusa.gruul.platform.mapper.AccountInfoMapper;
import com.medusa.gruul.platform.mapper.PlatformDepartmentMapper;
import com.medusa.gruul.platform.mapper.PlatformEmployeeMapper;
import com.medusa.gruul.platform.model.dto.BindStoreFrontDto;
import com.medusa.gruul.platform.model.dto.BindingWarehouseDto;
import com.medusa.gruul.platform.model.param.EmployeeParam;
import com.medusa.gruul.platform.service.IPlatformDepartmentService;
import com.medusa.gruul.platform.service.IPlatformEmployeePositionService;
import com.medusa.gruul.platform.service.IPlatformEmployeeService;
import com.medusa.gruul.platform.service.IPlatformPositionService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:43 2024/9/12
 */
@Service
public class PlatformEmployeeServiceImpl extends ServiceImpl<PlatformEmployeeMapper, PlatformEmployee>implements IPlatformEmployeeService {

    @Autowired
    private AccountInfoMapper accountInfoMapper;
    @Autowired
    private IPlatformPositionService positionService;
    @Override
    public PlatformEmployee newAdd(OutPlatformEmployeeDto outPlatformEmployeeDto) {
        PlatformEmployee platformEmployee = new PlatformEmployee();
        if(null == outPlatformEmployeeDto.getId()){
            //新增
            int numberCount = this.count(new LambdaQueryWrapper<PlatformEmployee>()
                    .eq(PlatformEmployee::getEmpNumber, outPlatformEmployeeDto.getEmpNumber()));
            if(numberCount > 0){
                throw new ServiceException("职员编号已存在！", SystemCode.DATA_EXISTED.getCode());
            }
            BeanUtil.copyProperties(outPlatformEmployeeDto, platformEmployee);
            platformEmployee.setSourceType(SourceTypeEnum.OTHER.getStatus());
            this.save(platformEmployee);
        }else{
            //更新
            PlatformEmployee dbData = this.baseMapper.selectById(outPlatformEmployeeDto.getId());
            if(null == dbData){
                throw new ServiceException("id对应的记录不存在！", SystemCode.DATA_NOT_EXIST.getCode());
            }
            BeanUtil.copyProperties(outPlatformEmployeeDto, platformEmployee);
            platformEmployee.setId(dbData.getId());
            this.updateById(platformEmployee);
        }
        return platformEmployee;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addEmployee(PlatformEmployeeDto platformEmployeeDto) {
        PlatformEmployee platformEmployee = new PlatformEmployee();
        //新增
        int numberCount = this.count(new LambdaQueryWrapper<PlatformEmployee>()
                .eq(PlatformEmployee::getEmpNumber, platformEmployeeDto.getEmpNumber()));
        if(numberCount > 0){
            throw new ServiceException("职员编号已存在！", SystemCode.DATA_EXISTED.getCode());
        }
        BeanUtil.copyProperties(platformEmployeeDto, platformEmployee);
        //本系统添加
        platformEmployee.setSourceType(SourceTypeEnum.LOCAL.getStatus());
        this.save(platformEmployee);
        platformEmployee.setOutId(platformEmployee.getId()+"");

        this.updateById(platformEmployee);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEmployee(PlatformEmployeeDto platformEmployeeDto) {
        if(platformEmployeeDto.getId() == null){
            throw new ServiceException("职员id不能为空");
        }
        Long id = platformEmployeeDto.getId();
        PlatformEmployee platformEmployee = this.getById(id);
        if(platformEmployee == null || platformEmployee.equals("")){
            throw new ServiceException("职员不存在");
        }
        int numberCount = this.count(new LambdaQueryWrapper<PlatformEmployee>()
                .eq(PlatformEmployee::getEmpNumber, platformEmployeeDto.getEmpNumber())
                .ne(PlatformEmployee::getId,id));
        if(numberCount > 0){
            throw new ServiceException("职员编号已存在！", SystemCode.DATA_EXISTED.getCode());
        }
        BeanUtil.copyProperties(platformEmployeeDto, platformEmployee);
        this.updateById(platformEmployee);

        LambdaQueryWrapper<AccountInfo>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountInfo::getEmployeeId,platformEmployee.getOutId());
        List<AccountInfo> accountInfoList = accountInfoMapper.selectList(wrapper);
        if(accountInfoList!=null&&accountInfoList.size()>0){
            AccountInfo accountInfo = accountInfoList.get(0);
            accountInfo.setEmployeeName(platformEmployee.getEmpFullName());
            accountInfoMapper.updateById(accountInfo);
        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteEmployee(PlatformEmployeeDto platformEmployeeDto) {
        Long id = platformEmployeeDto.getId();
        if(id==null){
            throw new ServiceException("职员id不能为空");
        }
        PlatformEmployee platformEmployee = this.getById(id);
        if(platformEmployee == null){
            throw new ServiceException("职员不存在");
        }
        LambdaQueryWrapper<AccountInfo>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountInfo::getEmployeeId,platformEmployee.getOutId());
        Integer count = accountInfoMapper.selectCount(wrapper);
        if(count>0){
            throw new ServiceException("存在用户绑定该职员，不能删除！");
        }
        this.removeById(id);
    }

    @Override
    public String getEmpNumber() {
        String  lastEmpNumber = baseMapper.getLastEmpNumber();
        String empNumber = "";
        lastEmpNumber  = lastEmpNumber==null?"":lastEmpNumber.trim();
        if(lastEmpNumber.matches("\\w*\\d$")){
            for(int j=lastEmpNumber.length()-1;j>=0;j--){
                if(!Character.isDigit(lastEmpNumber.charAt(j))||(Character.isDigit(lastEmpNumber.charAt(j))&&j==0)){
                    String numStr=lastEmpNumber;
                    if(j>0||(!Character.isDigit(lastEmpNumber.charAt(j))&&j==0)){
                        numStr=lastEmpNumber.substring(j+1);
                    }
                    String numStr2=String.valueOf(Long.parseLong(numStr));
                    String zero=numStr.substring(0,numStr.indexOf(numStr2));
                    long num=Long.parseLong(numStr2);
                    if(!lastEmpNumber.equals(numStr)){
                        lastEmpNumber=lastEmpNumber.substring(0,j+1);
                    }else{
                        lastEmpNumber="";
                    }
                    if(String.valueOf(num+1).length()>String.valueOf(numStr2).length()&&zero.length()>0){
                        lastEmpNumber+=zero.substring(0,zero.length()-1)+(num+1);
                    }else{
                        lastEmpNumber+=zero+(num+1);
                    }
                    break;
                }
            }
            empNumber = lastEmpNumber;
        }
        return empNumber;
    }


    @Override
    public PageUtils<EmployeeVo> searchEmployee(EmployeeParam employeeParam) {

        IPage<EmployeeVo> page = this.baseMapper.searchEmployee(new Page<>(employeeParam.getCurrent(), employeeParam.getSize()), employeeParam);
        List<EmployeeVo> records = page.getRecords();
        if (records != null && !records.isEmpty()) {
            List<Long> ids = records.stream().map(EmployeeVo::getId).collect(Collectors.toList());
            Map<Long, List<PlatformPositionVo>> map = positionService.getPositionsByEmployeeIds(ids);
            for (EmployeeVo employeeVo : records) {
                List<PlatformPositionVo> positions = map.get(employeeVo.getId());
                if (positions != null && !positions.isEmpty()) {
                    employeeVo.setPositionList(positions);
                }
            }
        }
        return new PageUtils<>(page);
    }

    @Override
    public PageUtils<EmployeeVo> searchBindEmployee(EmployeeParam employeeParam) {
        if(StringUtils.isEmpty(employeeParam.getUserId())){
            employeeParam.setUserId("");
        }
        IPage<EmployeeVo> page = this.baseMapper.searchBindEmployee(new Page<>(employeeParam.getCurrent(), employeeParam.getSize()), employeeParam);

        return new PageUtils<>(page);
    }

    @Override
    public void bindingWarehouse(BindingWarehouseDto dto) {
        //职员id
        String employeeId = dto.getEmployeeId();
        //仓库名称
        String warehouseFullName = dto.getWarehouseFullName();
        //仓库标识
        String stockCode = dto.getStockCode();

        PlatformEmployee platformEmployee = this.baseMapper.selectById(employeeId);
        if(platformEmployee!=null){
            platformEmployee.setStockCode(stockCode);
            platformEmployee.setWarehouseFullName(warehouseFullName);
            this.baseMapper.updateById(platformEmployee);
        }else{
            throw new ServiceException("职员不存在！");
        }

    }

    @Override
    public void bindingStoreFront(BindStoreFrontDto dto) {

        //职员id
        String employeeId = dto.getEmployeeId();
        //仓库名称
        String storeFrontName = dto.getStoreFrontName();
        //仓库标识
        String storeFrontCode = dto.getStoreFrontCode();

        PlatformEmployee platformEmployee = this.baseMapper.selectById(employeeId);
        if(platformEmployee!=null){
            platformEmployee.setStoreFrontName(storeFrontName);
            platformEmployee.setStoreFrontCode(storeFrontCode);
            this.baseMapper.updateById(platformEmployee);
        }else{
            throw new ServiceException("职员不存在！");
        }

    }

    @Override
    public boolean editWorkStatus(PlatformEmployeeDto dto) {
        Long id = dto.getId();
        Integer workStatus = dto.getWorkStatus();
        if (id==null || workStatus==null){
            throw new ServiceException("缺少参数！");
        }
        PlatformEmployee employee = new PlatformEmployee();
        employee.setId(id);
        employee.setWorkStatus(workStatus);

        return  updateById(employee);
    }
    @Override
    public boolean batchEditWorkStatus(String ids,Integer workStatus) {
        if (ids==null || workStatus==null){
            throw new ServiceException("缺少参数！");
        }
        String[] idArray = ids.split(",");
        ArrayList<PlatformEmployee> list = new ArrayList<>(idArray.length);
        for (String id : idArray){
            PlatformEmployee employee = new PlatformEmployee();
            employee.setId(Long.parseLong( id));
            employee.setWorkStatus(workStatus);
            list.add(employee);
        }
        return  this.updateBatchById(list);
    }
}
