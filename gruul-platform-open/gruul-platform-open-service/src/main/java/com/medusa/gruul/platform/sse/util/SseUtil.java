package com.medusa.gruul.platform.sse.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import com.alibaba.fastjson.JSON;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.LoginTerminalEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.IDUtil;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.platform.conf.PlatformRedis;
import com.medusa.gruul.platform.sse.dto.SseEventDto;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SSE连接管理工具类
 */
@Slf4j
@Component
public class SseUtil {

    /**
     * SSE连接超时时间30分
     */
    private static final long SSE_TIMEOUT = 30 * 60 * 1000L;

    /**
     * Redis中客户端的key + clientId>>ClientInfo
     */
    private static final String CLIENT_INFO_KEY_PREFIX = "sse:client:info:";

    /**
     * Redis中租户客户端关系集合 tenantId -> Set<clientId>
     */
    private static final String TENANT_CLIENT_SET_KEY_PREFIX = "sse:tenant:client:set:";

    private volatile PlatformRedis platformRedis;

    /**
     * 连接存储：clientId-> SseEmitter
     */
    private static final Map<String, SseEmitter> CLIENT_CONNECTIONS = new ConcurrentHashMap<>();



    public SseEmitter createConnection(String clientId,LoginTerminalEnum loginType) {
        // 获取租户ID
        String tenantId = TenantContextHolder.getTenantId();
        if (StrUtil.isBlank(clientId)) {
            clientId = getClientId( loginType);
        }
        if (StrUtil.isBlank(tenantId) || StrUtil.isBlank(clientId) || loginType == null ) {
            throw new ServiceException("缺少参数");
        }
        return createConnection(tenantId, clientId, loginType);
    }


    /**
     *
     * 创建SSE连接
     *
     * @param tenantId 租户ID
     * @param clientId 客户端ID
     * @param loginType 登录类型
     * @return SseEmitter
     */
    public SseEmitter createConnection(String tenantId, String clientId, LoginTerminalEnum loginType) {
        if (StrUtil.isBlank(tenantId) || StrUtil.isBlank(clientId) || loginType == null) {
            throw new ServiceException("租户ID、客户端ID loginType 不能为空");
        }


        // 如果已存在连接，先关闭旧连接
        removeConnection(tenantId, clientId);

        SseEmitter emitter = new SseEmitter(SSE_TIMEOUT);
        
        // 设置连接回调
        emitter.onCompletion(() -> {
            log.info("SSE连接完成: tenantId={}, clientId={}", tenantId, clientId);
            removeConnection(tenantId, clientId);
        });

        emitter.onTimeout(() -> {
            log.warn("SSE连接超时: tenantId={}, clientId={}", tenantId, clientId);
            removeConnection(tenantId, clientId);
        });

        emitter.onError((throwable) -> {
            log.error("SSE连接错误: tenantId={}, clientId={}, error={}", tenantId, clientId, throwable.getMessage());
            removeConnection(tenantId, clientId);
        });

        // 存储连接
        CLIENT_CONNECTIONS.put(clientId, emitter);

        // 保存客户端信息到Redis
        ClientInfo clientInfo = new ClientInfo(tenantId, clientId, loginType);
        saveClientInfoToRedis(clientInfo);

        int count = getAllClientIdsFromRedis().size();
        log.info("新建SSE连接: tenantId={}, clientId={}, loginType={}, 当前连接数={}",
                tenantId, clientId, loginType, count);

        // 发送连接成功消息 将clientId传回
        SseEventDto connectEvent = new SseEventDto(tenantId, "callback", clientId);
        sendToClient(emitter, connectEvent);

        return emitter;
    }


    /**
     * 指定客户端id - 发送消息给
     * @param event 事件消息
     *
     */
    public boolean sendToClient(SseEventDto event) {
        String tenantId = event.getTenantId();
        String clientId = event.getTargetClientId();

        if (StrUtil.isBlank(tenantId) || StrUtil.isBlank(clientId)) {
            log.warn("租户ID或客户端ID为空，无法发送消息");
            return false;
        }

        SseEmitter emitter = CLIENT_CONNECTIONS.get(clientId);
        if (emitter == null) {
            log.warn("客户端{}没有SSE连接", clientId);
            return false;
        }

        return sendToClient(emitter, event);
    }


    /**
     * 发送消息给租户下所有客户端
     */
    public int sendToTenant(SseEventDto event) {
        String tenantId = event.getTenantId();
        LoginTerminalEnum loginType = event.getLoginType();

        if (StrUtil.isBlank(tenantId)) {
            log.warn("租户ID为空，无法发送消息");
            return 0;
        }

        // 获取租户下的所有客户端ID
        Set<String> tenantClientIds = getTenantClientIds(tenantId);
        if (CollectionUtil.isEmpty(tenantClientIds)) {
            log.warn("租户{}下没有SSE连接", tenantId);
            return 0;
        }

        int successCount = 0;
        for (String clientId : tenantClientIds) {
            SseEmitter emitter = CLIENT_CONNECTIONS.get(clientId);
            if (emitter == null) {
                removeConnection(tenantId, clientId);
                continue;
            }

            // 检查登录类型过滤
            if (loginType != null) {
                ClientInfo clientInfo = getClientInfo(clientId);
                if (clientInfo == null || !loginType.equals(clientInfo.getLoginType())) {
                    continue;
                }
            }

            if (sendToClient(emitter, event)) {
                successCount++;
            }
        }

        log.info("向租户{}发送消息，成功发送{}个客户端", tenantId, successCount);
        return successCount;
    }

    /**
     * 发送消息给所有连接
     */
    public int sendToAll(SseEventDto event) {
        int successCount = 0;

        // 获取所有租户ID
        Set<String> allTenantIds = getAllTenantIds();

        for (String tenantId : allTenantIds) {
            // 为每个租户创建一个副本，设置对应的tenantId
            SseEventDto tenantEvent = new SseEventDto(tenantId, event.getEventType(), event.getData());
            tenantEvent.setEventId(event.getEventId());
            tenantEvent.setLoginType(event.getLoginType());
            tenantEvent.setTargetClientId(event.getTargetClientId());

            successCount += sendToTenant(tenantEvent);
        }
        return successCount;
    }

    /**
     * 发送消息给指定SseEmitter
     */
    private boolean sendToClient(SseEmitter emitter, SseEventDto event) {
        try {
            if (StrUtil.isBlank(event.getEventId())) 
                event.setEventId(IDUtil.getId().toString());
            
            
            emitter.send(SseEmitter.event()
                    .id(event.getEventId())
                    .name(event.getEventType())
                    .data(event.getData()));
            return true;
        } catch (IOException e) {
            log.error("发送SSE消息失败: {}", e.getMessage());
            return false;
        }
    }

    public void removeConnection(LoginTerminalEnum loginType) {
        String clientId = getClientId(loginType);
        removeConnection(TenantContextHolder.getTenantId(), clientId);
    }
    /**
     * 移除连接
     */
    public void removeConnection(String tenantId, String clientId) {
        SseEmitter emitter = CLIENT_CONNECTIONS.remove(clientId);
        if (emitter != null) {
            // 关闭
            emitter.complete();
            // 删除客户端
            removeClientInfo(clientId);
            int count = getAllClientIdsFromRedis().size();
            log.info("移除SSE连接: tenantId={}, clientId={}, 当前连接数={}", tenantId, clientId, count);
        }
    }
    // 密钥
    static final byte[] key = SecureUtil.generateKey(SymmetricAlgorithm.AES.getValue()).getEncoded();

    public static String getClientId(LoginTerminalEnum loginType) {
        // token太长 ->使用shopUserId+登录类型
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (curUser != null) {
            String shopUserId = curUser.getUserId();
            if (StrUtil.isBlank(shopUserId)) {
                throw new ServiceException("无法获取客户端ID");
            }
            String clientSource = shopUserId+loginType.name();
            // 16位加密
            return   SecureUtil.aes( key).encryptHex(clientSource);
        }
        throw new ServiceException("无法获取客户端ID");
    }
    /**
     * 保存客户端信息到Redis
     */
    private void saveClientInfoToRedis(ClientInfo clientInfo) {
        String key = CLIENT_INFO_KEY_PREFIX + clientInfo.getClientId();
        String tenantClientSetKey = TENANT_CLIENT_SET_KEY_PREFIX + clientInfo.getTenantId();

        // 保存客户端信息对象
        getRedis().setObject(key, clientInfo);
        // 添加到租户的客户端集合
        getRedis().sadd(tenantClientSetKey, clientInfo.getClientId());
        // 设置过期时间
        getRedis().expire(key, (int) (SSE_TIMEOUT / 1000));
        getRedis().expire(tenantClientSetKey, (int) (SSE_TIMEOUT / 1000));
    }

    /**
     * 从Redis获取客户端信息
     */
    private ClientInfo getClientInfo(String clientId) {
        String key = CLIENT_INFO_KEY_PREFIX + clientId;
        return getRedis().getObject(key, new ClientInfo());
    }

    /**
     * 删除客户端信息
     */
    private void removeClientInfo(String clientId) {
        String key = CLIENT_INFO_KEY_PREFIX + clientId;

        // 先获取客户端信息以获得租户ID
        ClientInfo clientInfo = getRedis().getObject(key, new ClientInfo());

        // 删除客户端信息
        getRedis().del(key);

        // 从租户的客户端集合中移除客户端ID
        if (clientInfo != null && StrUtil.isNotBlank(clientInfo.getTenantId())) {
            String tenantClientSetKey = TENANT_CLIENT_SET_KEY_PREFIX + clientInfo.getTenantId();
            getRedis().srem(tenantClientSetKey, clientId);
        }
    }

    /**
     * 获取指定租户的客户端ID集合
     */
    private Set<String> getTenantClientIds(String tenantId) {
        String tenantClientSetKey = TENANT_CLIENT_SET_KEY_PREFIX + tenantId;
        Set<String> clientIds = getRedis().smembers(tenantClientSetKey);
        return clientIds != null ? clientIds : new HashSet<>();
    }
    /**
     * 获取所有租户ID
     */
    public Set<String> getAllTenantIds() {
        Set<String> tenantIds = new HashSet<>();
        // 获取所有租户的客户端集合key
        Set<String> tenantKeys = getRedis().keys(TENANT_CLIENT_SET_KEY_PREFIX + "*");
        if (tenantKeys != null) {
            for (String tenantKey : tenantKeys) {
                // 从key中提取租户ID
                String tenantId = tenantKey.substring(TENANT_CLIENT_SET_KEY_PREFIX.length());
                tenantIds.add(tenantId);
            }
        }
        return tenantIds;
    }
    /**
     * 获取所有客户端ID
     */
    private Set<String> getAllClientIdsFromRedis() {
        Set<String> allClientIds = new HashSet<>();
        // 获取所有租户的客户端集合key
        Set<String> tenantKeys = getRedis().keys(TENANT_CLIENT_SET_KEY_PREFIX + "*");
        if (tenantKeys != null) {
            for (String tenantKey : tenantKeys) {
                Set<String> clientIds = getRedis().smembers(tenantKey);
                if (clientIds != null) {
                    allClientIds.addAll(clientIds);
                }
            }
        }
        return allClientIds;
    }


    public int getConnectionCount() {
        return getAllClientIdsFromRedis().size();
    }
    
    public int getTenantConnectionCount(String tenantId) {
        return getTenantClientIds(tenantId).size();
    }

    // RedisManager 不是Bean ->  懒加载
    private PlatformRedis getRedis(){
        if (platformRedis == null){
            synchronized (this){
                if (platformRedis == null){
                    platformRedis = new PlatformRedis();
                }
            }
        }
        return platformRedis;
    }


    /**
     * 客户端信息
     */
    @Data
    @NoArgsConstructor
    public static class ClientInfo {
        private String tenantId;
        private String clientId;
        private LoginTerminalEnum loginType;
        private Long connectTime;

        public ClientInfo(String tenantId, String clientId, LoginTerminalEnum loginType) {
            this.tenantId = tenantId;
            this.clientId = clientId;
            this.loginType = loginType;
            this.connectTime = System.currentTimeMillis();
        }

    }
}
