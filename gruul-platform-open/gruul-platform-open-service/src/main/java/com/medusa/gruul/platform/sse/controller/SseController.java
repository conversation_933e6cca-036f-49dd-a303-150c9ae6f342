package com.medusa.gruul.platform.sse.controller;

import cn.hutool.core.util.StrUtil;
import com.medusa.gruul.common.core.annotation.EscapeLogin;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.LoginTerminalEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.platform.sse.dto.SseEventDto;
import com.medusa.gruul.platform.sse.util.SseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * SSE长连接控制器
 */
@Slf4j
@RestController
@RequestMapping("/sse")
@Api(tags = "SSE长连接接口", value = "SSE长连接接口")
public class SseController {

    private SseUtil sseUtil;

    /**
     * 建立SSE连接
     *
     * @param loginType 登录类型：PC/MINI/H5
     * @param clientId 自定义客户端ID，为空则使用token
     * @return SseEmitter
     */
    @GetMapping(value = "/connect", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation(value = "建立SSE连接")
    public SseEmitter connect(@RequestParam LoginTerminalEnum loginType,
            @RequestParam(required = false) String clientId) {
        return sseUtil.createConnection(clientId,loginType);
    }

    /**
     * 关闭SSE连接
     * @return Result
     */
    @PostMapping("/close")
    @ApiOperation(value = "关闭SSE连接")
    public Result<String> close(@RequestParam LoginTerminalEnum loginType,
                                @RequestParam(required = false) String clientId) {
        if (loginType == null){
            throw new ServiceException("缺少登录类型");
        }
        if (StrUtil.isBlank(clientId)){
            sseUtil.removeConnection(loginType);
        }else {
            sseUtil.removeConnection(TenantContextHolder.getTenantId(),clientId);
        }
        return Result.ok("连接已关闭");
    }



    /**
     * 发送消息给租户下所有客户端
     * @return Result
     */
    @PostMapping("/sendToTenant")
    @EscapeLogin
    @ApiOperation(value = "发送消息给租户下所有客户端")
    public Result<Integer> sendToTenant(@RequestBody SseEventDto  event) {

        int successCount = sseUtil.sendToTenant(event);

        return Result.ok(successCount, "消息发送完成");

    }

    /**
     * 发送消息给所有连接
     */
    @PostMapping("/sendToAll")
    @EscapeLogin
    @ApiOperation(value = "发送消息给所有连接")
    public Result<Integer> sendToAll(@RequestBody @Valid SseEventDto event) {

        Integer successCount = sseUtil.sendToAll(event);
        return Result.ok(successCount, "消息发送完成");
    }

}
