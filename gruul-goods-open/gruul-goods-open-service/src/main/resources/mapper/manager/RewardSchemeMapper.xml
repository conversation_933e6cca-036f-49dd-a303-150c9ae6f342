<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.goods.mapper.manager.RewardSchemeMapper">
    <resultMap id="baseMapVo" type="com.medusa.gruul.goods.api.model.vo.manager.RewardSchemeVo">
        <id column="id" property="id"/>
        <result column="bill_no" property="billNo"/>
        <result column="bill_date" property="billDate"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="name" property="name"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="status" property="status"/>
        <result column="approval_status" property="approvalStatus"/>
        <result column="approval_reason" property="approvalReason"/>
        <result column="approval_time" property="approvalTime"/>
        <result column="remark" property="remark"/>
    </resultMap>
    <resultMap id="baseMapDetVo" type="com.medusa.gruul.goods.api.model.vo.manager.RewardSchemeShowVo">
        <id column="id" property="id"/>
        <result column="reward_id" property="rewardId"/>
        <result column="bill_no" property="billNo"/>
        <result column="bill_date" property="billDate"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="name" property="name"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="remark" property="remark"/>
        <result column="reward_type" property="rewardType"/>
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="sku_id" property="skuId"/>
        <result column="sku_name" property="skuName"/>
        <result column="price_type" property="priceType"/>
        <result column="member_level_id" property="memberLevelId"/>
        <result column="member_level_name" property="memberLevelName"/>
        <result column="det_remark" property="detRemark"/>
        <result column="member_type_id" property="memberTypeId"/>
        <result column="order_member_type_ids" property="orderMemberTypeIds"/>
        <result column="commission_title" property="commissionTitle"/>
        <result column="one_commission_rate" property="oneCommissionRate"/>
        <result column="two_commission_rate" property="twoCommissionRate"/>
        <result column="one_commission_amount" property="oneCommissionAmount"/>
        <result column="two_commission_amount" property="twoCommissionAmount"/>
        <result column="commission_amount_source" property="commissionAmountSource"/>
        <result column="commission_amount_source_name" property="commissionAmountSourceName"/>
    </resultMap>





    <select id="getReceiptNo" resultType="java.lang.String">
        SELECT MAX(bill_no) FROM t_reward_scheme where bill_no like CONCAT('%',#{time},'%')
    </select>


    <select id="searchRewardScheme" resultMap="baseMapVo">
        SELECT
            t1.id,
            t1.bill_no,
            DATE_FORMAT( t1.bill_date, '%Y-%m-%d' ) AS bill_date,
            DATE_FORMAT( t1.start_time, '%Y-%m-%d' ) AS start_time,
            DATE_FORMAT( t1.end_time, '%Y-%m-%d' ) AS end_time,
            t1.name,
            t1.user_id,
            t2.nike_name AS user_name,
            t1.status,
            t1.approval_status,
            t1.approval_reason,
            DATE_FORMAT( t1.approval_time, '%Y-%m-%d' ) AS approval_time,
            t1.remark
        FROM
            t_reward_scheme t1
        LEFT JOIN
            t_platform_account_info t2 ON t1.user_id = t2.id
        WHERE
            t1.is_deleted = 0
        <if test="paramMap.billDateStartTime!=null and paramMap.billDateStartTime!='' and paramMap.billDateEndTime!=null and paramMap.billDateEndTime!=''">
            and t1.bill_Date >= #{paramMap.billDateStartTime} and t1.bill_Date &lt;= #{paramMap.billDateEndTime}
        </if>
        <if test="paramMap.startTime!=null and paramMap.startTime!='' and paramMap.endTime!=null and paramMap.endTime!=''">
            and t1.start_time >= #{paramMap.startTime} and t1.start_time&lt;=#{paramMap.endTime}
        </if>
        <if test="paramMap.createUserName!=null and paramMap.createUserName!=''">
            and t2.nike_name like CONCAT('%',#{paramMap.createUserName},'%')
        </if>
        <if test="paramMap.status!=null ">
            and t1.status = #{paramMap.status}
        </if>
        <if test="paramMap.remark!=null and paramMap.remark!=''">
            and t1.remark like CONCAT('%',#{paramMap.remark},'%')
        </if>
        <if test="paramMap.name!=null and paramMap.name!=''">
            and t1.name like CONCAT('%',#{paramMap.name},'%')
        </if>
        <if test="paramMap.approvalStatus!=null ">
            and t1.approval_status = #{paramMap.approvalStatus}
        </if>
        <if test="paramMap.billNoSort != null and paramMap.billNoSort==1">
            order by t1.bill_no asc
        </if>
        <if test="paramMap.billNoSort != null and paramMap.billNoSort==2">
            order by t1.bill_no desc
        </if>
        <if test="paramMap.billDateSort != null and paramMap.billDateSort==1">
            order by t1.bill_date asc
        </if>
        <if test="paramMap.billDateSort != null and paramMap.billDateSort==2">
            order by t1.bill_date desc
        </if>
    </select>
    <select id="getRewardScheme" resultMap="baseMapVo">
        SELECT
        t1.id,
        t1.bill_no,
        DATE_FORMAT( t1.bill_date, '%Y-%m-%d' ) AS bill_date,
        DATE_FORMAT( t1.start_time, '%Y-%m-%d' ) AS start_time,
        DATE_FORMAT( t1.end_time, '%Y-%m-%d' ) AS end_time,
        t1.name,
        t1.user_id,
        t2.nike_name AS user_name,
        t1.status,
        t1.approval_status,
        t1.approval_reason,
        DATE_FORMAT( t1.approval_time, '%Y-%m-%d' ) AS approval_time,
        t1.remark
        FROM
        t_reward_scheme t1
        LEFT JOIN
        t_platform_account_info t2 ON t1.user_id = t2.id
        WHERE
        t1.is_deleted = 0 and t1.id = #{id}
    </select>

    <select id="searchRewardSchemeDet" resultMap="baseMapDetVo">
        SELECT
            t1.id,
            t1.reward_id,
            t2.bill_no,
            DATE_FORMAT( t2.bill_date, '%Y-%m-%d' ) AS bill_date,
            DATE_FORMAT( t2.start_time, '%Y-%m-%d' ) AS start_time,
            DATE_FORMAT( t2.end_time, '%Y-%m-%d' ) AS end_time,
            t2.name,
            t2.user_id,
            t3.nike_name AS user_name,
            t2.create_user_id,
            t4.nike_name AS create_user_name,
            t2.remark,
            t1.reward_type,
            t1.product_id,
            t5.name AS product_name,
            t1.sku_id,
            t6.specs as sku_name,
            t1.price_type,
            t1.member_level_id,
            t7.member_level as member_level_name,
            t1.remark as det_remark,
            t1.commission_title,
            t1.one_commission_rate,
            t1.two_commission_rate,
            t1.one_commission_amount,
            t1.two_commission_amount,
            t1.commission_amount_source,
            t1.member_type_id,
            t1.order_member_type_ids,
            CASE
                WHEN t1.commission_amount_source = 1 THEN '订单实际支付金额'
                WHEN t1.commission_amount_source = 2 THEN '佣金金额'
                WHEN t1.commission_amount_source = 3 THEN '订单金额'
                ELSE ''
            END as commission_amount_source_name
        FROM
            t_reward_scheme_det t1
        LEFT JOIN
            t_reward_scheme t2 ON t2.id = t1.reward_id
        LEFT JOIN
            t_platform_account_info t3 ON t2.user_id = t3.id
        LEFT JOIN
            t_platform_account_info t4 ON t2.create_user_id = t4.id
        LEFT JOIN
            t_product t5 ON t5.id = t1.product_id
        LEFT JOIN
            t_sku_stock t6 on t6.id = t1.sku_id
        LEFT JOIN
            t_member_level t7 on t7.id = t1.member_level_id
        WHERE
            t1.is_deleted = 0 and t2.is_deleted = 0
        <if test="paramMap.billDateStartTime!=null and paramMap.billDateStartTime!='' and paramMap.billDateEndTime!=null and paramMap.billDateEndTime!=''">
            and t2.bill_Date >= #{paramMap.billDateStartTime} and t2.bill_Date &lt;= #{paramMap.billDateEndTime}
        </if>
        <if test="paramMap.userName!=null and paramMap.userName!=''">
            and t3.nike_name like CONCAT('%',#{paramMap.userName},'%')
        </if>
        <if test="paramMap.createUserName!=null and paramMap.createUserName!=''">
            and t4.nike_name like CONCAT('%',#{paramMap.createUserName},'%')
        </if>
        <if test="paramMap.name!=null and paramMap.name!=''">
            and t2.name like CONCAT('%',#{paramMap.name},'%')
        </if>
        <if test="paramMap.startTime!=null and paramMap.startTime!='' and paramMap.endTime!=null and paramMap.endTime!=''">
            and t2.start_time >= #{paramMap.startTime} and t2.start_time&lt;=#{paramMap.endTime}
        </if>
        <if test="paramMap.rewardType!=null ">
            and t1.reward_type = #{paramMap.rewardType}
        </if>
        <if test="paramMap.productName!=null and paramMap.productName!=''">
            and t5.name like CONCAT('%',#{paramMap.productName},'%')
        </if>
        <if test="paramMap.memberLevelName!=null and paramMap.memberLevelName!=''">
            and t7.member_level like CONCAT('%',#{paramMap.memberLevelName},'%')
        </if>
        <if test="paramMap.orderMemberTypeName != null and paramMap.orderMemberTypeName != ''">
            and EXISTS (
                SELECT 1 FROM t_member_type mt
                WHERE mt.name LIKE CONCAT('%',#{paramMap.orderMemberTypeName},'%')
                AND FIND_IN_SET(mt.id, t1.order_member_type_ids) > 0
                AND mt.is_deleted = 0
            )
        </if>
        <if test="paramMap.commissionTitle != null and paramMap.commissionTitle != ''">
            and t1.commission_title like CONCAT('%',#{paramMap.commissionTitle},'%')
        </if>
        <if test="paramMap.billNoSort != null and paramMap.billNoSort==1">
            order by t2.bill_no asc
        </if>
        <if test="paramMap.billNoSort != null and paramMap.billNoSort==2">
            order by t2.bill_no desc
        </if>
        <if test="paramMap.billDateSort != null and paramMap.billDateSort==1">
            order by t2.bill_date asc
        </if>
        <if test="paramMap.billDateSort != null and paramMap.billDateSort==2">
            order by t2.bill_date desc
        </if>
    </select>

</mapper>
                                                                                                                                                                                                         