package com.medusa.gruul.goods.web.controller.manager;

import java.text.SimpleDateFormat;
import java.util.*;


import com.medusa.gruul.common.core.annotation.EscapeLogin;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.goods.api.entity.ProductBuyIn;
import com.medusa.gruul.goods.api.model.dto.manager.ProductBuyInDto;
import com.medusa.gruul.goods.api.model.param.manager.ProductBuyInItemParam;
import com.medusa.gruul.goods.api.model.vo.manager.ProductBuyInItemAllVo;
import com.medusa.gruul.goods.api.model.vo.manager.ProductBuyInItemVo;
import com.medusa.gruul.goods.api.model.vo.manager.ProductBuyInVo;
import com.medusa.gruul.goods.api.model.vo.manager.SkuStockVo;
import com.medusa.gruul.goods.service.manager.IProductBuyInItemService;
import com.medusa.gruul.goods.service.manager.IProductBuyInService;
import com.medusa.gruul.goods.service.manager.ISkuStockService;
import com.medusa.gruul.shops.api.entity.DictItem;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import lombok.extern.slf4j.Slf4j;
import com.medusa.gruul.common.core.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

 /**
 * @Description: 购货入库
 * @Author: qsx
 * @Date:   2022-03-08
 * @Version: V1.0
 */
@Slf4j
@Api(tags="购货入库")
@RestController
@RequestMapping("/productBuyIn")
public class ProductBuyInController  {
	@Autowired
	private IProductBuyInService productBuyInService;
     @Autowired
     private IProductBuyInItemService productBuyInItemService;
     @Autowired
     private RemoteShopsService remoteShopsService;

     /**
      * 分页列表查询
      *
      * @param productBuyInDetailed
      * @param pageNo
      * @param pageSize
      * @param req
      * @return
      */
     /**@AutoLog(value = "购货入库明细-分页列表查询")*/
     @ApiOperation(value="购货入库明细-分页列表查询", notes="购货入库明细-分页列表查询")
     @PostMapping(value = "/list")
     public Result<?> queryPageList(@RequestBody ProductBuyInDto productBuyInDto) {
         PageUtils<ProductBuyInItemAllVo> pageUtils = new PageUtils(productBuyInService.selectList(productBuyInDto));
         return Result.ok(pageUtils);
     }
	
	/**
	 * 添加
	 *
	 * @param productBuyIn
	 * @return
	 */
	/**@AutoLog(value = "购货入库-添加")*/
	@ApiOperation(value="购货入库-添加", notes="购货入库-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody ProductBuyInDto productBuyInDto) {
        productBuyInService.add(productBuyInDto);
        return Result.ok("添加成功！");
	}
	
	/**
	 * 编辑
	 *
	 * @param productBuyIn
	 * @return
	 */
	/**@AutoLog(value = "购货入库-编辑")*/
	@ApiOperation(value="购货入库-编辑", notes="购货入库-编辑")
	@PostMapping(value = "/edit")
	public Result<?> edit(@RequestBody ProductBuyInDto productBuyInDto) {
		productBuyInService.update(productBuyInDto);
		return Result.ok("编辑成功！");
	}
     /**
      * 根据入库单Id获取入库明细
      *
      * @param productBuyIn
      * @return
      */
     /**@AutoLog(value = "购货入库-入库明细")*/
     @ApiOperation(value="购货入库-入库明细", notes="购货入库-入库明细")
     @PostMapping(value = "/queryById")
     public Result<?> queryById(@RequestBody ProductBuyInDto productBuyInDto) {
         //根据入库单id获取入库单
         ProductBuyInVo productBuyInVo=productBuyInService.getById(productBuyInDto.getId());
         //根据入库单id获取入库明细
         List<ProductBuyInItemVo> productBuyInItemVoList=productBuyInItemService.getByProductBuyInId(productBuyInDto.getId());
         productBuyInVo.setProductBuyInItemVoList(productBuyInItemVoList);
         return Result.ok(productBuyInVo);
     }
	
	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	/**@AutoLog(value = "购货入库-通过id删除")*/
	@ApiOperation(value="购货入库-通过id删除", notes="购货入库-通过id删除")
	@PostMapping(value = "/delete")
	public Result<?> delete(@RequestBody ProductBuyInDto productBuyInDto) {
		productBuyInService.delete(productBuyInDto);
		return Result.ok("", "删除成功");
	}
	
	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	/**@AutoLog(value = "购货入库-批量删除")*/
	@ApiOperation(value="购货入库-批量删除", notes="购货入库-批量删除")
	@PostMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.productBuyInService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功！");
	}
	

     /**
      * 获取入库类型
      *
      *
      * @return
      */
     /**@AutoLog(value = "购货入库-获取入库类型")*/
     @ApiOperation(value="购货入库-获取入库类型", notes="购货入库-获取入库类型")
     @PostMapping(value = "/getBuyType")
     public Result<?> getBuyType() {
         List<DictItem> dictItemList=remoteShopsService.getBuyType();
         return Result.ok(dictItemList);
     }

     /**
      * 获取入库单号和制单人
      *
      * @return
      */
     /**@AutoLog(value = "购货入库-获取入库单号和制单人")*/
     @ApiOperation(value="购货入库-获取入库单号和制单人", notes="购货入库-获取入库单号和制单人")
     @PostMapping(value = "/getReceiptNoAndUser")
     public Result<?> getReceiptNoAndUser() {
         Map<String,Object> map=productBuyInService.getReceiptNoAndUser();
         return Result.ok(map);
     }


     /**
      * 导出商品入库列表
      */
     @ApiOperation(value="导出商品入库列表", notes="导出商品入库列表")
     @PostMapping(value = "/export")
     public void exportProductBuyIn(@RequestBody ProductBuyInDto productBuyInDto) {
         productBuyInService.exportProductBuyIn(productBuyInDto);
     }
}
