package com.medusa.gruul.goods.service.manager.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.goods.api.constant.GoodsSkuStockRedisKey;
import com.medusa.gruul.goods.api.entity.*;
import com.medusa.gruul.goods.api.model.vo.manager.SkuStockMemberPriceVo;
import com.medusa.gruul.goods.api.param.OperateStockDto;
import com.medusa.gruul.goods.mapper.manager.ProductShowCategoryMapper;
import com.medusa.gruul.goods.mapper.manager.SkuStockMapper;
import com.medusa.gruul.goods.service.manager.IProductStockService;
import com.medusa.gruul.goods.service.manager.ISkuStockService;
import com.medusa.gruul.order.api.entity.OrderSetting;
import com.medusa.gruul.order.api.feign.RemoteOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * sku的库存 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-03
 */
@Service
public class SkuStockServiceImpl extends ServiceImpl<SkuStockMapper, SkuStock> implements ISkuStockService {
    @Autowired
    private IProductStockService productStockService;
    @Autowired
    private RemoteOrderService remoteOrderService;
    @Autowired
    private ProductShowCategoryMapper productShowCategoryMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean subtractStock(OperateStockDto operateStockDto) {
        GoodsSkuStockRedisKey goodsSkuStockRedisKey = new GoodsSkuStockRedisKey();
        int numAttempts = 0;
        do {
            SkuStock skuStock = baseMapper.selectById(operateStockDto.getSkuId());
            if (skuStock.getStock().compareTo(new BigDecimal(operateStockDto.getNumber())) >= 0) {
                skuStock.setStock(skuStock.getStock().subtract(new BigDecimal(operateStockDto.getNumber())));
                skuStock.setSale(skuStock.getSale() + operateStockDto.getNumber());
                if (this.updateById(skuStock)) {
                    goodsSkuStockRedisKey.set(String.valueOf(skuStock.getId()), String.valueOf(skuStock.getStock()));
                    return true;
                } else {
                    numAttempts++;
                }
            }
        } while (numAttempts < CommonConstants.DEFAULT_MAX_RETRIES);
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchSubtractStock(List<OperateStockDto> operateStockDtoList) {
        GoodsSkuStockRedisKey goodsSkuStockRedisKey = new GoodsSkuStockRedisKey();
        List<Long> skuIdList =
                operateStockDtoList.stream().map(OperateStockDto::getSkuId).collect(Collectors.toList());
        List<SkuStock> skuStockList =
                baseMapper.selectBatchIds(skuIdList);
        // 查询关联商品的skuId
        List<Long> linkSkuIdList =
                skuStockList.stream().map(SkuStock::getLinkSkuId).collect(Collectors.toList());
        // 查询关联商品的库存信息
        List<SkuStock> linkSkuStockList =
                baseMapper.selectBatchIds(linkSkuIdList);
        Map<Long, SkuStock> skuStockMap = skuStockList.stream().collect(Collectors.toMap(SkuStock::getId, v -> v));
        Map<Long, SkuStock> linkSkuStockMap = linkSkuStockList.stream().collect(Collectors.toMap(SkuStock::getId, v -> v));
        skuStockList.clear();
        boolean re = false;
        //获取订单设置，判断是否开启负库存下单
        OrderSetting orderSetting = remoteOrderService.getOrderSetting();
        boolean openNegative = orderSetting != null && orderSetting.getOpenNegativeOrder();
        for (OperateStockDto operateStockDto : operateStockDtoList) {
            SkuStock skuStock = skuStockMap.get(operateStockDto.getSkuId());
            // 实际出库
            if (skuStock.getReallyOutStock() > 1){
                Integer reallyOutStockBySkus = skuStock.getReallyOutStockNum(operateStockDto.getNumber());
                operateStockDto.setNumber(reallyOutStockBySkus);
            }
            if (openNegative || skuStock.getStock().compareTo(new BigDecimal(operateStockDto.getNumber())) >= 0) {
                skuStock.setStock(skuStock.getStock().subtract(new BigDecimal(operateStockDto.getNumber())));
                skuStock.setSale(skuStock.getSale() + operateStockDto.getNumber());
                //更新数据库库存与销量 同时更新缓存里面的库存
                if (this.updateById(skuStock)) {
                    goodsSkuStockRedisKey.set(String.valueOf(skuStock.getId()), String.valueOf(skuStock.getStock()));
                    re = true;
                }
                if(skuStock.getLinkSkuId() != null){
                    SkuStock linkSkuStock = linkSkuStockMap.get(skuStock.getLinkSkuId());
                    linkSkuStock.setStock(linkSkuStock.getStock().subtract(new BigDecimal(operateStockDto.getNumber())));
                    linkSkuStock.setSale(linkSkuStock.getSale() + operateStockDto.getNumber());
                    //更新数据库库存与销量 同时更新缓存里面的库存
                    if (this.updateById(linkSkuStock)) {
                        goodsSkuStockRedisKey.set(String.valueOf(linkSkuStock.getId()), String.valueOf(linkSkuStock.getStock()));
                    }
                }
            }
        }
        return re;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean VerifySubtractStock(List<OperateStockDto> operateStockDtoList) {
        GoodsSkuStockRedisKey goodsSkuStockRedisKey = new GoodsSkuStockRedisKey();
        List<Long> skuIdList =
                operateStockDtoList.stream().map(OperateStockDto::getSkuId).collect(Collectors.toList());
        List<SkuStock> skuStockList =
                baseMapper.selectBatchIds(skuIdList);
        Map<Long, SkuStock> skuStockMap = skuStockList.stream().collect(Collectors.toMap(SkuStock::getId, v -> v));
        skuStockList.clear();
        boolean re = false;
        for (OperateStockDto operateStockDto : operateStockDtoList) {
            SkuStock skuStock = skuStockMap.get(operateStockDto.getSkuId());
            if (skuStock.getStock().compareTo(new BigDecimal(operateStockDto.getNumber())) >= 0) {
                skuStock.setStock(skuStock.getStock().subtract(new BigDecimal(operateStockDto.getNumber())));
                skuStock.setSale(skuStock.getSale() + operateStockDto.getNumber());
                //更新数据库库存与销量 同时更新缓存里面的库存
                if (this.updateById(skuStock)) {
                    goodsSkuStockRedisKey.set(String.valueOf(skuStock.getId()), String.valueOf(skuStock.getStock()));
                    re = true;
                }
            }
        }
        return re;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchProductSubtractStock(List<OperateStockDto> operateStockDtoList) {
        GoodsSkuStockRedisKey goodsSkuStockRedisKey = new GoodsSkuStockRedisKey();
        List<Long> skuIdList =
                operateStockDtoList.stream().map(OperateStockDto::getSkuId).collect(Collectors.toList());
        List<SkuStock> skuStockList =
                baseMapper.selectBatchIds(skuIdList);
        Map<Long, SkuStock> skuStockMap = skuStockList.stream().collect(Collectors.toMap(SkuStock::getId, v -> v));
        skuStockList.clear();
        boolean re = false;
        for (OperateStockDto operateStockDto : operateStockDtoList) {
            SkuStock skuStock = skuStockMap.get(operateStockDto.getSkuId());
            if (skuStock.getStock().compareTo(new BigDecimal(operateStockDto.getNumber())) >= 0) {
                skuStock.setStock(skuStock.getStock().subtract(new BigDecimal(operateStockDto.getNumber())) );
                skuStock.setSale(skuStock.getSale() + operateStockDto.getNumber());
                //更新数据库库存与销量 同时更新缓存里面的库存
                if (this.updateById(skuStock)) {
                    goodsSkuStockRedisKey.set(String.valueOf(skuStock.getId()), String.valueOf(skuStock.getStock()));
                    re = true;
                }
            }
        }
        return re;
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchRevertStock(List<OperateStockDto> operateStockDtoList) {
        GoodsSkuStockRedisKey goodsSkuStockRedisKey = new GoodsSkuStockRedisKey();
        List<Long> skuIdList =
                operateStockDtoList.stream().map(OperateStockDto::getSkuId).collect(Collectors.toList());
        //获取仓库id
        List<Long> warehouseIdList =
                operateStockDtoList.stream().map(OperateStockDto::getWarehouseId).collect(Collectors.toList());
        List<SkuStock> skuStockList =
                baseMapper.selectBatchIds(skuIdList);
        Map<Long, SkuStock> skuStockMap = skuStockList.stream().collect(Collectors.toMap(SkuStock::getId, v -> v));
        skuStockList.clear();
        LambdaQueryWrapper<ProductStock> lambdaQueryWrapper=new LambdaQueryWrapper<ProductStock>();
        lambdaQueryWrapper.in(ProductStock::getSkuId,skuIdList);
        if(CollectionUtils.isNotEmpty(warehouseIdList)){
            lambdaQueryWrapper.in(ProductStock::getWarehouseId,warehouseIdList);
        }

        List<ProductStock> productStockList=productStockService.list(lambdaQueryWrapper);
        for (OperateStockDto operateStockDto : operateStockDtoList) {
            SkuStock skuStock = skuStockMap.get(operateStockDto.getSkuId());
            if (!BeanUtil.isEmpty(skuStock)) {
                if (skuStock.getReallyOutStock() > 1){
                    operateStockDto.setNumber(skuStock.getReallyOutStockNum(operateStockDto.getNumber()));
                }
                if(BeanUtil.isNotEmpty(operateStockDto.getWarehouseId())){
                    for(ProductStock prStock:productStockList){
                        if(prStock.getSkuId().equals(operateStockDto.getSkuId()) && prStock.getWarehouseId().equals(operateStockDto.getWarehouseId())){
                            BigDecimal number=new BigDecimal(operateStockDto.getNumber());
                            prStock.setStock(prStock.getStock().add(number));
                            productStockService.updateById(prStock);
                        }
                    }

                }
                skuStock.setStock(skuStock.getStock().add(new BigDecimal(operateStockDto.getNumber())));
                skuStock.setSale(skuStock.getSale() - operateStockDto.getNumber());
                //更新数据库库存与销量 同时更新缓存里面的库存
                if (this.updateById(skuStock)) {
                    goodsSkuStockRedisKey.set(String.valueOf(skuStock.getId()), String.valueOf(skuStock.getStock()));
                    return true;
                }
                goodsSkuStockRedisKey.set(String.valueOf(skuStock.getId()), String.valueOf(skuStock.getStock()));
            }
        }
        return false;
    }

    @Override
    public List<SkuStock> getBySkuStockListId(List<Long> skuStockIdList) {
        return this.getBaseMapper().selectList(new LambdaQueryWrapper<SkuStock>().in(SkuStock::getId, skuStockIdList));

    }

    @Override
    public List<SkuStockMemberPriceVo> getByProductId(Long productId) {
        return this.getBaseMapper().getByProductId(productId);
    }

    @Override
    public List<SkuStock> getByCategoryId(String categoryId) {
        LambdaQueryWrapper<ProductShowCategory>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductShowCategory::getDeleted,CommonConstants.NUMBER_ZERO);
        wrapper.eq(ProductShowCategory::getShowCategoryId,categoryId);
        List<ProductShowCategory> productShowCategoryList = productShowCategoryMapper.selectList(wrapper);
        List<Long>productIds = new ArrayList<>();
        List<SkuStock> dataList = new ArrayList<>();
        for (ProductShowCategory productShowCategory : productShowCategoryList) {
            Long productId = productShowCategory.getProductId();
            if(!productIds.contains(productId)){
                LambdaQueryWrapper<SkuStock>skuStockLambdaQueryWrapper = new LambdaQueryWrapper<>();
                skuStockLambdaQueryWrapper.eq(SkuStock::getDeleted,CommonConstants.NUMBER_ZERO);
                skuStockLambdaQueryWrapper.eq(SkuStock::getProductId,productId);
                List<SkuStock> list = this.list(skuStockLambdaQueryWrapper);
                if(list!=null&&list.size()>0){
                    dataList.addAll(list);
                }
                productIds.add(productId);
            }
        }
        return dataList;
    }
}
