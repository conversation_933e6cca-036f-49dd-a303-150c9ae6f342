package com.medusa.gruul.goods.service.manager.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MemberType;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.ApproveStatusEnum;
import com.medusa.gruul.common.core.constant.enums.RewardSchemeEnum;
import com.medusa.gruul.common.core.constant.enums.RewardTypeEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.HuToolExcelUtils;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.entity.*;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.dto.manager.*;
import com.medusa.gruul.goods.api.model.param.manager.RewardSchemeParam;
import com.medusa.gruul.goods.api.model.param.manager.RewardSchemeShowParam;
import com.medusa.gruul.goods.api.model.vo.manager.*;
import com.medusa.gruul.goods.mapper.manager.RewardSchemeDetMapper;
import com.medusa.gruul.goods.mapper.manager.RewardSchemeMapper;
import com.medusa.gruul.goods.service.manager.*;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:31 2025/3/10
 */
@Service
public class RewardSchemeServiceImpl extends ServiceImpl<RewardSchemeMapper, RewardScheme> implements IRewardSchemeService {
    @Autowired
    private IProductService productService;
    @Autowired
    private ISkuStockService skuStockService;

    @Autowired
    private RewardSchemeDetMapper rewardSchemeDetMapper;

    @Autowired
    private IRewardSchemeDetProductService rewardSchemeDetProductService;
    @Autowired
    private IRewardSchemeDetNonProductService rewardSchemeDetNonProductService;

    @Autowired
    private RemoteGoodsService remoteGoodsService;

    @Autowired
    private RemoteMiniAccountService remoteMiniAccountService;

    @Override
    @Transactional
    public void add(RewardSchemeDto rewardSchemeDto) {


        String billNo = rewardSchemeDto.getBillNo();
        LambdaQueryWrapper<RewardScheme>billNoWrapper = new LambdaQueryWrapper<>();
        billNoWrapper.eq(RewardScheme::getBillNo,billNo);
        Integer billNoNumber = baseMapper.selectCount(billNoWrapper);
        if(billNoNumber>0){
            throw new ServiceException("单据编号已存在！");
        }

        LambdaQueryWrapper<RewardScheme> nameWrapper = new LambdaQueryWrapper<>();
        nameWrapper.eq(RewardScheme::getName,rewardSchemeDto.getName());
        Integer nameNumber = baseMapper.selectCount(nameWrapper);
        if(nameNumber>0 && !rewardSchemeDto.getName().contains("-复制")){
            throw new ServiceException("活动名称已存在！");
        }

        //获取用户信息
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        List<RewardSchemeDetDto> list = rewardSchemeDto.getList();
        LocalDateTime startTime = rewardSchemeDto.getStartTime();
        LocalDateTime endTime = rewardSchemeDto.getEndTime();
        //判断是否有重复值
        if(rewardSchemeDto.getStatus()==RewardSchemeEnum.NO.getStatus()){
            List<RewardSchemeDetDto> rewardSchemeDetList = rewardSchemeDto.getList();
            if(rewardSchemeDetList!=null&&rewardSchemeDetList.size()>0){
                for (RewardSchemeDetDto rewardSchemeDetDto : rewardSchemeDetList) {
                    if(rewardSchemeDetDto.getMemberTypeId() == null){
                         throw new ServiceException("奖励方案明细会员类型不能为空！");
                    }
                    if(rewardSchemeDetDto.getRewardType() == null){
                        throw new ServiceException("奖励方案明细奖励类型不能为空！");
                    }
                    if(rewardSchemeDetDto.getOneCommissionAmount() == null
                            &&rewardSchemeDetDto.getOneCommissionRate() == null
                            &&rewardSchemeDetDto.getTwoCommissionAmount() == null
                            &&rewardSchemeDetDto.getTwoCommissionRate() == null){
                        throw new ServiceException("奖励方案明细一级下级比例,二级下级比例,一级下级固定金额,二级下级固定金额不能为都为空！");
                    }
                    if(rewardSchemeDetDto.getRewardType().equals(RewardTypeEnum.CYCLE_COMMISSION.getStatus())){
                        //循环分佣
                        if(StringUtils.isNotEmpty(rewardSchemeDetDto.getOneCommissionRate())){
                            for (String oneCommissionRate : rewardSchemeDetDto.getOneCommissionRate().split(",")) {
                                if(!isNumeric(oneCommissionRate)){
                                    throw new ServiceException("奖励方案明细一级下级比例必须为数字！");
                                }
                            }
                        }
                        if(StringUtils.isNotEmpty(rewardSchemeDetDto.getTwoCommissionRate())){
                            for (String twoCommissionRate : rewardSchemeDetDto.getTwoCommissionRate().split(",")) {
                                if(!isNumeric(twoCommissionRate)){
                                    throw new ServiceException("奖励方案明细二级下级比例必须为数字！");
                                }
                            }
                        }
                        if(StringUtils.isNotEmpty(rewardSchemeDetDto.getOneCommissionAmount())){
                            for (String oneCommissionAmount : rewardSchemeDetDto.getOneCommissionAmount().split(",")) {
                                if(!isNumeric(oneCommissionAmount)){
                                    throw new ServiceException("奖励方案明细一级下级固定金额必须为数字！");
                                }
                            }
                        }
                        if(StringUtils.isNotEmpty(rewardSchemeDetDto.getTwoCommissionAmount())){
                            for (String twoCommissionAmount : rewardSchemeDetDto.getTwoCommissionAmount().split(",")) {
                                if(!isNumeric(twoCommissionAmount)){
                                    throw new ServiceException("奖励方案明细二级下级固定金额必须为数字！");
                                }
                            }
                        }
                    }else{
                        if(StringUtils.isNotEmpty(rewardSchemeDetDto.getOneCommissionRate())){
                            String oneCommissionRate = rewardSchemeDetDto.getOneCommissionRate();
                            if(!isNumeric(oneCommissionRate)){
                                throw new ServiceException("奖励方案明细一级下级比例必须为数字！");
                            }
                        }
                        if(StringUtils.isNotEmpty(rewardSchemeDetDto.getTwoCommissionRate())){
                            String twoCommissionRate = rewardSchemeDetDto.getTwoCommissionRate();
                            if(!isNumeric(twoCommissionRate)){
                                throw new ServiceException("奖励方案明细二级下级比例必须为数字！");
                            }
                        }
                        if(StringUtils.isNotEmpty(rewardSchemeDetDto.getOneCommissionAmount())){
                            String oneCommissionAmount = rewardSchemeDetDto.getOneCommissionAmount();
                            if(!isNumeric(oneCommissionAmount)){
                                throw new ServiceException("奖励方案明细一级下级固定金额必须为数字！");
                            }
                        }
                        if(StringUtils.isNotEmpty(rewardSchemeDetDto.getTwoCommissionAmount())){
                            String twoCommissionAmount = rewardSchemeDetDto.getTwoCommissionAmount();
                            if(!isNumeric(twoCommissionAmount)){
                                throw new ServiceException("奖励方案明细二级下级固定金额必须为数字！");
                            }
                        }
                    }

                }
            }else{
                throw new ServiceException("奖励方案明细不能为空");
            }





            LambdaQueryWrapper<RewardScheme>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(RewardScheme::getDeleted, CommonConstants.NUMBER_ZERO);
            wrapper.ne(RewardScheme::getStatus,0);
            wrapper.ne(RewardScheme::getStatus,-2);
            wrapper.and(e->e.ge(RewardScheme::getStartTime,startTime).le(RewardScheme::getStartTime,endTime).
                    or(e2->e2.ge(RewardScheme::getEndTime,startTime).le(RewardScheme::getEndTime,endTime)).
                    or(e3->e3.le(RewardScheme::getStartTime,startTime).ge(RewardScheme::getEndTime,endTime)));
            List<RewardScheme> rewardSchemeList = baseMapper.selectList(wrapper);
//            if(rewardSchemeList.size()>0){
//                for (RewardScheme rewardScheme : rewardSchemeList) {
//
//                    for (RewardSchemeDetDto rewardSchemeDetDto : list) {
//                        LambdaQueryWrapper<RewardSchemeDet>queryWrapper = new LambdaQueryWrapper<>();
//                        queryWrapper.eq(RewardSchemeDet::getDeleted,CommonConstants.NUMBER_ZERO);
//                        queryWrapper.eq(RewardSchemeDet::getRewardId,rewardScheme.getId());
//
//                        if(rewardSchemeDetDto.getRewardType()!=null){
//                            queryWrapper.eq(RewardSchemeDet::getRewardType,rewardSchemeDetDto.getRewardType());
//                        }else{
//                            queryWrapper.isNull(RewardSchemeDet::getRewardType);
//                        }
//
//                        queryWrapper.eq(RewardSchemeDet::getProductId,rewardSchemeDetDto.getProductId());
//                        queryWrapper.eq(RewardSchemeDet::getSkuId,rewardSchemeDetDto.getSkuId());
//
//                        if(rewardSchemeDetDto.getPriceType()!=null){
//                            queryWrapper.eq(RewardSchemeDet::getPriceType,rewardSchemeDetDto.getPriceType());
//                        }else{
//                            queryWrapper.isNull(RewardSchemeDet::getPriceType);
//                        }
//
//                        queryWrapper.eq(RewardSchemeDet::getMemberLevelId,rewardSchemeDetDto.getMemberLevelId());
//                        Integer count = rewardSchemeDetMapper.selectCount(queryWrapper);
//                        if (count > 0) {
//                            throw new ServiceException("奖励类型，商品名称，商品规格，价格类型，会员等级相同的数据已存在！");
//                        }
//                    }
//                }
//            }
        }

        RewardScheme rewardScheme = new RewardScheme();
        BeanUtils.copyProperties(rewardSchemeDto,rewardScheme);
        rewardScheme.setCreateUserId(Long.valueOf(curUserDto.getUserId()));
        rewardScheme.setCreateUserName(curUserDto.getNikeName());
        baseMapper.insert(rewardScheme);
        if(list!=null&&list.size()>0){
            for (RewardSchemeDetDto rewardSchemeDetDto : list) {
                RewardSchemeDet rewardSchemeDet = new RewardSchemeDet();
                BeanUtils.copyProperties(rewardSchemeDetDto,rewardSchemeDet);
                rewardSchemeDet.setCreateUserId(Long.valueOf(curUserDto.getUserId()));
                rewardSchemeDet.setCreateUserName(curUserDto.getNikeName());
                rewardSchemeDet.setRewardId(String.valueOf(rewardScheme.getId()));

                //订单会员类型id
                if(rewardSchemeDetDto.getOrderMemberTypeList()!=null
                &&rewardSchemeDetDto.getOrderMemberTypeList().size()>0){
                    String orderMemberTypeIds = String.join(",", rewardSchemeDetDto.getOrderMemberTypeList());
                    rewardSchemeDet.setOrderMemberTypeIds(orderMemberTypeIds);
                }
                //订单收益等级
                if(rewardSchemeDetDto.getMemberLevelList()!=null
                        &&rewardSchemeDetDto.getMemberLevelList().size()>0){
                    String memberLevelId = String.join(",", rewardSchemeDetDto.getMemberLevelList());
                    rewardSchemeDet.setMemberLevelId(memberLevelId);
                }
                //条件升级等级
                if(rewardSchemeDetDto.getUpMemberLevelList()!=null
                        &&rewardSchemeDetDto.getUpMemberLevelList().size()>0){
                    String upMemberLevelId = String.join(",", rewardSchemeDetDto.getUpMemberLevelList());
                    rewardSchemeDet.setUpMemberLevelId(upMemberLevelId);
                }
                //条件直推等级
                if(rewardSchemeDetDto.getDirectMemberLevelList()!=null
                        &&rewardSchemeDetDto.getDirectMemberLevelList().size()>0){
                    String directMemberLevelIds = String.join(",", rewardSchemeDetDto.getDirectMemberLevelList());
                    rewardSchemeDet.setDirectMemberLevelIds(directMemberLevelIds);
                }

                rewardSchemeDetMapper.insert(rewardSchemeDet);
                List<RewardSchemeDetProductDto> rewardSchemeDetProductList = rewardSchemeDetDto.getRewardSchemeDetProductList();
                if(rewardSchemeDetProductList!=null&&rewardSchemeDetProductList.size()>0){
                    for (RewardSchemeDetProductDto rewardSchemeDetProductDto : rewardSchemeDetProductList) {
                        RewardSchemeDetProduct rewardSchemeDetProduct = new RewardSchemeDetProduct();
                        BeanUtils.copyProperties(rewardSchemeDetProductDto,rewardSchemeDetProduct);
                        rewardSchemeDetProduct.setRewardId(rewardScheme.getId());
                        rewardSchemeDetProduct.setCreateUserId(Long.valueOf(curUserDto.getUserId()));
                        rewardSchemeDetProduct.setCreateUserName(curUserDto.getNikeName());
                        rewardSchemeDetProduct.setRewardDetId(rewardSchemeDet.getId());
                        rewardSchemeDetProductService.save(rewardSchemeDetProduct);
                    }
                }
                List<RewardSchemeDetNonProductDto> rewardSchemeDetNonProductList = rewardSchemeDetDto.getRewardSchemeDetNonProductList();
                if(rewardSchemeDetNonProductList!=null&&rewardSchemeDetNonProductList.size()>0){
                    for (RewardSchemeDetNonProductDto rewardSchemeDetNonProductDto : rewardSchemeDetNonProductList) {
                        RewardSchemeDetNonProduct rewardSchemeDetNonProduct = new RewardSchemeDetNonProduct();
                        BeanUtils.copyProperties(rewardSchemeDetNonProductDto,rewardSchemeDetNonProduct);
                        rewardSchemeDetNonProduct.setRewardId(rewardScheme.getId());
                        rewardSchemeDetNonProduct.setCreateUserId(Long.valueOf(curUserDto.getUserId()));
                        rewardSchemeDetNonProduct.setCreateUserName(curUserDto.getNikeName());
                        rewardSchemeDetNonProduct.setRewardDetId(rewardSchemeDet.getId());
                        rewardSchemeDetNonProductService.save(rewardSchemeDetNonProduct);
                    }
                }


            }
        }

    }

    @Override
    @Transactional
    public void edit(RewardSchemeDto rewardSchemeDto) {

        Long id = rewardSchemeDto.getId();
        List<RewardSchemeDetDto> list = rewardSchemeDto.getList();
        if(id == null ){
            throw new ServiceException("奖励活动id不能为空");
        }
        RewardScheme rewardScheme = baseMapper.selectById(id);
        if(rewardScheme == null ){
            throw new ServiceException("奖励活动不存在");
        }
        LambdaQueryWrapper<RewardScheme> nameWrapper = new LambdaQueryWrapper<>();
        nameWrapper.eq(RewardScheme::getName,rewardSchemeDto.getName());
        nameWrapper.ne(RewardScheme::getId,id);// 排除自己
        Integer nameNumber = baseMapper.selectCount(nameWrapper);
        if(nameNumber>0){
            throw new ServiceException("活动名称已存在！");
        }

        String billNo = rewardSchemeDto.getBillNo();
        LambdaQueryWrapper<RewardScheme>billNoWrapper = new LambdaQueryWrapper<>();
        billNoWrapper.eq(RewardScheme::getBillNo,billNo);
        billNoWrapper.ne(RewardScheme::getId,id);
        Integer billNoNumber = baseMapper.selectCount(billNoWrapper);
        if(billNoNumber>0){
            throw new ServiceException("单据编号已存在！");
        }

        //获取用户信息
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        LocalDateTime startTime = rewardSchemeDto.getStartTime();
        LocalDateTime endTime = rewardSchemeDto.getEndTime();
        //判断是否有重复值
        if(rewardSchemeDto.getStatus()==RewardSchemeEnum.NO.getStatus()){

            List<RewardSchemeDetDto> rewardSchemeDetList = rewardSchemeDto.getList();
            if(rewardSchemeDetList!=null&&rewardSchemeDetList.size()>0){
                for (RewardSchemeDetDto rewardSchemeDetDto : rewardSchemeDetList) {
                    if(rewardSchemeDetDto.getMemberTypeId() == null){
                        throw new ServiceException("奖励方案明细会员类型不能为空！");
                    }
                    if(rewardSchemeDetDto.getRewardType() == null){
                        throw new ServiceException("奖励方案明细奖励类型不能为空！");
                    }
                    if(rewardSchemeDetDto.getOneCommissionAmount() == null
                            &&rewardSchemeDetDto.getOneCommissionRate() == null
                            &&rewardSchemeDetDto.getTwoCommissionAmount() == null
                            &&rewardSchemeDetDto.getTwoCommissionRate() == null){
                        throw new ServiceException("奖励方案明细一级下级比例,二级下级比例,一级下级固定金额,二级下级固定金额不能为都为空！");
                    }
                    if(rewardSchemeDetDto.getRewardType().equals(RewardTypeEnum.CYCLE_COMMISSION.getStatus())){
                        //循环分佣
                        if(StringUtils.isNotEmpty(rewardSchemeDetDto.getOneCommissionRate())){
                            for (String oneCommissionRate : rewardSchemeDetDto.getOneCommissionRate().split(",")) {
                                if(!isNumeric(oneCommissionRate)){
                                    throw new ServiceException("奖励方案明细一级下级比例必须为数字！");
                                }
                            }
                        }
                        if(StringUtils.isNotEmpty(rewardSchemeDetDto.getTwoCommissionRate())){
                            for (String twoCommissionRate : rewardSchemeDetDto.getTwoCommissionRate().split(",")) {
                                if(!isNumeric(twoCommissionRate)){
                                    throw new ServiceException("奖励方案明细二级下级比例必须为数字！");
                                }
                            }
                        }
                        if(StringUtils.isNotEmpty(rewardSchemeDetDto.getOneCommissionAmount())){
                            for (String oneCommissionAmount : rewardSchemeDetDto.getOneCommissionAmount().split(",")) {
                                if(!isNumeric(oneCommissionAmount)){
                                    throw new ServiceException("奖励方案明细一级下级固定金额必须为数字！");
                                }
                            }
                        }
                        if(StringUtils.isNotEmpty(rewardSchemeDetDto.getTwoCommissionAmount())){
                            for (String twoCommissionAmount : rewardSchemeDetDto.getTwoCommissionAmount().split(",")) {
                                if(!isNumeric(twoCommissionAmount)){
                                    throw new ServiceException("奖励方案明细二级下级固定金额必须为数字！");
                                }
                            }
                        }
                    }else{
                        if(StringUtils.isNotEmpty(rewardSchemeDetDto.getOneCommissionRate())){
                            String oneCommissionRate = rewardSchemeDetDto.getOneCommissionRate();
                            if(!isNumeric(oneCommissionRate)){
                                throw new ServiceException("奖励方案明细一级下级比例必须为数字！");
                            }
                        }
                        if(StringUtils.isNotEmpty(rewardSchemeDetDto.getTwoCommissionRate())){
                            String twoCommissionRate = rewardSchemeDetDto.getTwoCommissionRate();
                            if(!isNumeric(twoCommissionRate)){
                                throw new ServiceException("奖励方案明细二级下级比例必须为数字！");
                            }
                        }
                        if(StringUtils.isNotEmpty(rewardSchemeDetDto.getOneCommissionAmount())){
                            String oneCommissionAmount = rewardSchemeDetDto.getOneCommissionAmount();
                            if(!isNumeric(oneCommissionAmount)){
                                throw new ServiceException("奖励方案明细一级下级固定金额必须为数字！");
                            }
                        }
                        if(StringUtils.isNotEmpty(rewardSchemeDetDto.getTwoCommissionAmount())){
                            String twoCommissionAmount = rewardSchemeDetDto.getTwoCommissionAmount();
                            if(!isNumeric(twoCommissionAmount)){
                                throw new ServiceException("奖励方案明细二级下级固定金额必须为数字！");
                            }
                        }
                    }

                }
            }else{
                throw new ServiceException("奖励方案明细不能为空");
            }

            LambdaQueryWrapper<RewardScheme>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(RewardScheme::getDeleted, CommonConstants.NUMBER_ZERO);
            wrapper.ne(RewardScheme::getId,id);
            //过滤状态为0-草稿；-2-停止数据
            wrapper.ne(RewardScheme::getStatus,0);
            wrapper.ne(RewardScheme::getStatus,-2);
            wrapper.and(e->e.ge(RewardScheme::getStartTime,startTime).le(RewardScheme::getStartTime,endTime).
                    or(e2->e2.ge(RewardScheme::getEndTime,startTime).le(RewardScheme::getEndTime,endTime)).
                    or(e3->e3.le(RewardScheme::getStartTime,startTime).ge(RewardScheme::getEndTime,endTime)));
            List<RewardScheme> rewardSchemeList = baseMapper.selectList(wrapper);

//            if(rewardSchemeList.size()>0){
//
//                for (RewardScheme scheme : rewardSchemeList) {
//
//                    for (RewardSchemeDetDto rewardSchemeDetDto : list) {
//                        LambdaQueryWrapper<RewardSchemeDet>queryWrapper = new LambdaQueryWrapper<>();
//                        queryWrapper.ne(RewardSchemeDet::getId,rewardSchemeDetDto.getId());
//                        queryWrapper.eq(RewardSchemeDet::getDeleted,CommonConstants.NUMBER_ZERO);
//                        queryWrapper.eq(RewardSchemeDet::getRewardId,scheme.getId());
//                        if(rewardSchemeDetDto.getRewardType()!=null){
//                            queryWrapper.eq(RewardSchemeDet::getRewardType,rewardSchemeDetDto.getRewardType());
//                        }else{
//                            queryWrapper.isNull(RewardSchemeDet::getRewardType);
//                        }
//                        queryWrapper.eq(RewardSchemeDet::getProductId,rewardSchemeDetDto.getProductId());
//                        queryWrapper.eq(RewardSchemeDet::getSkuId,rewardSchemeDetDto.getSkuId());
//
//                        if(rewardSchemeDetDto.getPriceType()!=null){
//                            queryWrapper.eq(RewardSchemeDet::getPriceType,rewardSchemeDetDto.getPriceType());
//                        }else{
//                            queryWrapper.isNull(RewardSchemeDet::getPriceType);
//                        }
//                        queryWrapper.eq(RewardSchemeDet::getMemberLevelId,rewardSchemeDetDto.getMemberLevelId());
//                        Integer count = rewardSchemeDetMapper.selectCount(queryWrapper);
//                        if (count > 0) {
//                            throw new ServiceException("奖励类型，商品名称，商品规格，价格类型，会员等级相同的数据【"+scheme.getBillNo()+"】已存在！");
//                        }
//                    }
//                }
//            }
        }
        //删除奖励奖励方案明细分佣商品
        LambdaQueryWrapper<RewardSchemeDetProduct>wrapper3 = new LambdaQueryWrapper<>();
        wrapper3.eq(RewardSchemeDetProduct::getRewardId,id);
        List<RewardSchemeDetProduct> removeRewardSchemeDetProductList = rewardSchemeDetProductService.list(wrapper3);
        if(removeRewardSchemeDetProductList!=null&&removeRewardSchemeDetProductList.size()>0){
            for (RewardSchemeDetProduct rewardSchemeDetProduct : removeRewardSchemeDetProductList) {
                rewardSchemeDetProductService.removeById(rewardSchemeDetProduct.getId());
            }
        }
        //删除奖励方案明细非分佣商品
        LambdaQueryWrapper<RewardSchemeDetNonProduct>wrapper4 = new LambdaQueryWrapper<>();
        wrapper4.eq(RewardSchemeDetNonProduct::getRewardId,id);
        List<RewardSchemeDetNonProduct> removeRewardSchemeDetNonProductList = rewardSchemeDetNonProductService.list(wrapper4);
        if(removeRewardSchemeDetNonProductList!=null&&removeRewardSchemeDetNonProductList.size()>0){
            for (RewardSchemeDetNonProduct rewardSchemeDetNonProduct : removeRewardSchemeDetNonProductList) {
                rewardSchemeDetNonProductService.removeById(rewardSchemeDetNonProduct.getId());
            }
        }

        LambdaQueryWrapper<RewardSchemeDet>wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.eq(RewardSchemeDet::getRewardId,id);
        List<RewardSchemeDet> rewardSchemeDetList = rewardSchemeDetMapper.selectList(wrapper2);
        if(rewardSchemeDetList!=null&&rewardSchemeDetList.size()>0){
            for (RewardSchemeDet rewardSchemeDet : rewardSchemeDetList) {
                rewardSchemeDetMapper.deleteById(rewardSchemeDet.getId());
            }
        }




        BeanUtils.copyProperties(rewardSchemeDto,rewardScheme);

        rewardScheme.setLastModifyUserId(Long.valueOf(curUserDto.getUserId()));
        rewardScheme.setLastModifyUserName(curUserDto.getNikeName());

        baseMapper.updateById(rewardScheme);

        if(list!=null&&list.size()>0){
            for (RewardSchemeDetDto rewardSchemeDetDto : list) {
                RewardSchemeDet rewardSchemeDet = new RewardSchemeDet();
                BeanUtils.copyProperties(rewardSchemeDetDto,rewardSchemeDet);
                rewardSchemeDet.setId(null);
                rewardSchemeDet.setCreateUserId(Long.valueOf(curUserDto.getUserId()));
                rewardSchemeDet.setCreateUserName(curUserDto.getNikeName());
                rewardSchemeDet.setRewardId(String.valueOf(rewardScheme.getId()));

                //订单会员类型id
                if(rewardSchemeDetDto.getOrderMemberTypeList()!=null
                        &&rewardSchemeDetDto.getOrderMemberTypeList().size()>0){
                    String orderMemberTypeIds = String.join(",", rewardSchemeDetDto.getOrderMemberTypeList());
                    rewardSchemeDet.setOrderMemberTypeIds(orderMemberTypeIds);
                }
                //订单收益等级
                if(rewardSchemeDetDto.getMemberLevelList()!=null
                        &&rewardSchemeDetDto.getMemberLevelList().size()>0){
                    String memberLevelId = String.join(",", rewardSchemeDetDto.getMemberLevelList());
                    rewardSchemeDet.setMemberLevelId(memberLevelId);
                }
                //条件升级等级
                if(rewardSchemeDetDto.getUpMemberLevelList()!=null
                        &&rewardSchemeDetDto.getUpMemberLevelList().size()>0){
                    String upMemberLevelId = String.join(",", rewardSchemeDetDto.getUpMemberLevelList());
                    rewardSchemeDet.setUpMemberLevelId(upMemberLevelId);
                }
                //条件直推等级
                if(rewardSchemeDetDto.getDirectMemberLevelList()!=null
                        &&rewardSchemeDetDto.getDirectMemberLevelList().size()>0){
                    String directMemberLevelIds = String.join(",", rewardSchemeDetDto.getDirectMemberLevelList());
                    rewardSchemeDet.setDirectMemberLevelIds(directMemberLevelIds);
                }

                rewardSchemeDetMapper.insert(rewardSchemeDet);
                List<RewardSchemeDetProductDto> rewardSchemeDetProductList = rewardSchemeDetDto.getRewardSchemeDetProductList();
                if(rewardSchemeDetProductList!=null&&rewardSchemeDetProductList.size()>0){
                    for (RewardSchemeDetProductDto rewardSchemeDetProductDto : rewardSchemeDetProductList) {
                        RewardSchemeDetProduct rewardSchemeDetProduct = new RewardSchemeDetProduct();
                        BeanUtils.copyProperties(rewardSchemeDetProductDto,rewardSchemeDetProduct);
                        rewardSchemeDetProduct.setRewardId(rewardScheme.getId());
                        rewardSchemeDetProduct.setCreateUserId(Long.valueOf(curUserDto.getUserId()));
                        rewardSchemeDetProduct.setCreateUserName(curUserDto.getNikeName());
                        rewardSchemeDetProduct.setRewardDetId(rewardSchemeDet.getId());
                        rewardSchemeDetProductService.save(rewardSchemeDetProduct);
                    }
                }
                List<RewardSchemeDetNonProductDto> rewardSchemeDetNonProductList = rewardSchemeDetDto.getRewardSchemeDetNonProductList();
                if(rewardSchemeDetNonProductList!=null&&rewardSchemeDetNonProductList.size()>0){
                    for (RewardSchemeDetNonProductDto rewardSchemeDetNonProductDto : rewardSchemeDetNonProductList) {
                        RewardSchemeDetNonProduct rewardSchemeDetNonProduct = new RewardSchemeDetNonProduct();
                        BeanUtils.copyProperties(rewardSchemeDetNonProductDto,rewardSchemeDetNonProduct);
                        rewardSchemeDetNonProduct.setRewardId(rewardScheme.getId());
                        rewardSchemeDetNonProduct.setCreateUserId(Long.valueOf(curUserDto.getUserId()));
                        rewardSchemeDetNonProduct.setCreateUserName(curUserDto.getNikeName());
                        rewardSchemeDetNonProduct.setRewardDetId(rewardSchemeDet.getId());
                        rewardSchemeDetNonProductService.save(rewardSchemeDetNonProduct);
                    }
                }
            }
        }

    }

    @Override
    public Map<String, Object> getReceiptNoAndUser() {
        //获取用户信息
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        Map<String,Object> map=new HashMap<String,Object>(4);
        SimpleDateFormat bartDateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat bartDateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String time="JLFA-"+bartDateFormat.format(date);
        String maxBuyNo = baseMapper.getReceiptNo(time);
        String billNo="";
        if(StrUtil.isNotEmpty(maxBuyNo)){
            String code=maxBuyNo.substring(14);
            Long number=Long.parseLong(code);
            number=number+1;
            String str=number.toString();
            int num=str.length();
            for (int i=0;i<(5-num);i++){
                str="0"+str;
            }
            billNo=time+"-"+(str);
        }else{
            billNo=time+"-"+"00001";
        }
        map.put("billNo",billNo);
        map.put("billDate",bartDateFormat2.format(date));
        map.put("userId",curUserDto.getUserId());
        map.put("userName",curUserDto.getNikeName());
        return map;
    }

    @Override
    @Transactional
    public PageUtils<RewardSchemeVo> searchRewardScheme(RewardSchemeParam rewardSchemeParam) {
        updateStatus();
        IPage<RewardSchemeVo> page = this.baseMapper.searchRewardScheme(new Page<>(rewardSchemeParam.getCurrent(), rewardSchemeParam.getSize()), rewardSchemeParam);
        if(page!=null&&page.getRecords()!=null&&page.getRecords().size()>0){
            for (RewardSchemeVo rewardSchemeVo : page.getRecords()) {
                if(rewardSchemeVo.getList()!=null&&rewardSchemeVo.getList().size()>0){
                    for (RewardSchemeDetVo rewardSchemeDetVo : rewardSchemeVo.getList()) {
                        if(StringUtils.isNotEmpty(rewardSchemeDetVo.getOrderMemberTypeIds())){
                            List<String> orderMemberTypeList = Arrays.asList(rewardSchemeDetVo.getOrderMemberTypeIds().split(","));
                            rewardSchemeDetVo.setOrderMemberTypeList(orderMemberTypeList);
                        }
                        if(StringUtils.isNotEmpty(rewardSchemeDetVo.getMemberLevelId())){
                            List<String> memberLevelList = Arrays.asList(rewardSchemeDetVo.getMemberLevelId().split(","));
                            rewardSchemeDetVo.setMemberLevelList(memberLevelList);
                        }
                        if(StringUtils.isNotEmpty(rewardSchemeDetVo.getUpMemberLevelId())){
                            List<String> upMemberLevelList = Arrays.asList(rewardSchemeDetVo.getUpMemberLevelId().split(","));
                            rewardSchemeDetVo.setUpMemberLevelList(upMemberLevelList);
                        }
                        if(StringUtils.isNotEmpty(rewardSchemeDetVo.getDirectMemberLevelIds())){
                            List<String> directMemberLevelList = Arrays.asList(rewardSchemeDetVo.getDirectMemberLevelIds().split(","));
                            rewardSchemeDetVo.setDirectMemberLevelList(directMemberLevelList);
                        }
                    }
                }
            }
        }

        return new PageUtils<>(page);
    }

    @Override
    public RewardSchemeVo getDetail(Long id) {

        RewardSchemeVo rewardSchemeVo = this.baseMapper.getRewardScheme(id);
        List<RewardSchemeDetVo>list = rewardSchemeDetMapper.getRewardSchemeDet(id);
        if(list!=null&&list.size()>0){
            for (RewardSchemeDetVo rewardSchemeDetVo : list) {

                if(StringUtils.isNotEmpty(rewardSchemeDetVo.getOrderMemberTypeIds())){
                    List<String> orderMemberTypeList = Arrays.asList(rewardSchemeDetVo.getOrderMemberTypeIds().split(","));
                    rewardSchemeDetVo.setOrderMemberTypeList(orderMemberTypeList);
                }
                if(StringUtils.isNotEmpty(rewardSchemeDetVo.getMemberLevelId())){
                    List<String> memberLevelList = Arrays.asList(rewardSchemeDetVo.getMemberLevelId().split(","));
                    rewardSchemeDetVo.setMemberLevelList(memberLevelList);
                }
                if(StringUtils.isNotEmpty(rewardSchemeDetVo.getUpMemberLevelId())){
                    List<String> upMemberLevelList = Arrays.asList(rewardSchemeDetVo.getUpMemberLevelId().split(","));
                    rewardSchemeDetVo.setUpMemberLevelList(upMemberLevelList);
                }
                if(StringUtils.isNotEmpty(rewardSchemeDetVo.getDirectMemberLevelIds())){
                    List<String> directMemberLevelList = Arrays.asList(rewardSchemeDetVo.getDirectMemberLevelIds().split(","));
                    rewardSchemeDetVo.setDirectMemberLevelList(directMemberLevelList);
                }

                String rewardSchemeDetProductName = "";
                String rewardSchemeDetNonProductName = "";
                List<RewardSchemeDetProductVo> rewardSchemeDetProductList = rewardSchemeDetVo.getRewardSchemeDetProductList();
                List<RewardSchemeDetNonProductVo> rewardSchemeDetNonProductList = rewardSchemeDetVo.getRewardSchemeDetNonProductList();
                if(rewardSchemeDetProductList!=null&&rewardSchemeDetProductList.size()>0){
                    for (RewardSchemeDetProductVo rewardSchemeDetProductVo : rewardSchemeDetProductList) {
                        Long skuId = rewardSchemeDetProductVo.getSkuId();
                        Long productId = rewardSchemeDetProductVo.getProductId();
                        String shopId = ShopContextHolder.getShopId();
                        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);

                        ProductVo product = productService.findProductById(productId);
                        SkuStock skuStock = skuStockService.getById(skuId);
                        ShopContextHolder.setShopId(shopId);
                        String name = product.getName();
                        String specs = skuStock.getSpecs();
                        if(StringUtils.isNotEmpty(specs)){
                            specs = "-"+specs;
                        }else{
                            specs = "";
                        }
                        if(rewardSchemeDetProductName.length() > 0){
                            rewardSchemeDetProductName += "、";
                        }
                        rewardSchemeDetProductName += name+specs;
                    }
                    rewardSchemeDetVo.setRewardSchemeDetProductName(rewardSchemeDetProductName);
                }
                if(rewardSchemeDetNonProductList!=null&&rewardSchemeDetNonProductList.size()>0){
                    for (RewardSchemeDetNonProductVo rewardSchemeDetNonProductVo : rewardSchemeDetNonProductList) {
                        Long skuId = rewardSchemeDetNonProductVo.getSkuId();
                        Long productId = rewardSchemeDetNonProductVo.getProductId();
                        ProductVo product = remoteGoodsService.findProductById(productId);
                        SkuStock skuStock = remoteGoodsService.findSkuStockById(skuId);
                        String name = product.getName();
                        String specs = skuStock.getSpecs();
                        if(StringUtils.isNotEmpty(specs)){
                            specs = "-"+specs;
                        }else{
                            specs = "";
                        }
                        if(rewardSchemeDetNonProductName.length() > 0){
                            rewardSchemeDetNonProductName += "、";
                        }
                        rewardSchemeDetNonProductName += name+specs;
                    }
                    rewardSchemeDetVo.setRewardSchemeDetNonProductName(rewardSchemeDetNonProductName);
                }
            }
        }
        rewardSchemeVo.setList(list);
        return rewardSchemeVo;
    }

    @Override
    @Transactional
    public void approval(RewardSchemeApprovalDto rewardSchemeApprovalDto) {

        Integer approvalStatus = rewardSchemeApprovalDto.getApprovalStatus();
        String approvalReason = rewardSchemeApprovalDto.getApprovalReason();
        String ids = rewardSchemeApprovalDto.getIds();
        if(StringUtils.isEmpty(ids)){
            throw new ServiceException("奖励方案id不能为空");
        }

        if(approvalStatus== ApproveStatusEnum.REJECT.getStatus()&&StringUtils.isEmpty(approvalReason)){
            throw new ServiceException("拒绝审核，审核原因不能为空");
        }

        for (String id : ids.split(",")) {
            RewardScheme rewardScheme = baseMapper.selectById(id);
            if(rewardScheme == null){
                throw new ServiceException("奖励方案不存在");
            }
            //状态为失效，并且审核状态为待审核才能审核
            if(rewardScheme.getApprovalStatus() == ApproveStatusEnum.AUDIT.getStatus()&&rewardScheme.getStatus() == RewardSchemeEnum.NO.getStatus()){
                rewardScheme.setApprovalStatus(approvalStatus);
                rewardScheme.setApprovalReason(approvalReason);
                rewardScheme.setApprovalTime(LocalDateTime.now());
                baseMapper.updateById(rewardScheme);
            }
        }

    }

    @Override
    @Transactional
    public void stop(RewardSchemeDto rewardSchemeDto) {
        Long id = rewardSchemeDto.getId();
        Integer status = rewardSchemeDto.getStatus();
        if(id == null){
            throw new ServiceException("奖励方案id不能为空");
        }

        if(status == null){
            throw new ServiceException("奖励方案状态不能为空");
        }
        RewardScheme rewardScheme = baseMapper.selectById(id);

        if(rewardScheme == null){
            throw new ServiceException("奖励方案不存在");
        }
        rewardScheme.setStatus(status);
        baseMapper.updateById(rewardScheme);
    }

    @Override
    public void delete(RewardSchemeDto rewardSchemeDto) {
        Long id = rewardSchemeDto.getId();
        if(id == null){
            throw new ServiceException("奖励方案id不能为空");
        }
        RewardScheme rewardScheme = baseMapper.selectById(id);
        if (rewardScheme != null){
            if(rewardScheme.getStatus() != RewardSchemeEnum.DRAFT.getStatus()){
                throw new ServiceException("只能删除草稿状态的奖励方案");
            }
            baseMapper.deleteById( id);
        }else {
            throw new ServiceException("奖励方案不存在");
        }

    }

    @Override
    @Transactional
    public void copy(RewardSchemeCopyDto rewardSchemeCopyDto) {
        String ids = rewardSchemeCopyDto.getIds();
        if(StringUtils.isEmpty(ids)){
            throw new ServiceException("奖励方案id不能为空");
        }
        String billNo = "";
        SimpleDateFormat bartDateFormat = new SimpleDateFormat("yyyyMMdd");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        String time="JLFA-"+bartDateFormat.format(date);
        for (String id : ids.split(",")) {
            RewardSchemeVo rewardSchemeVo = getDetail(Long.valueOf(id));
            if(rewardSchemeVo == null){
                throw new ServiceException("奖励方案不存在");
            }
            if(StringUtils.isEmpty(billNo)){
                Map<String, Object> receiptNoAndUser = getReceiptNoAndUser();
                billNo = String.valueOf(receiptNoAndUser.get("billNo"));
            }else {
                String code=billNo.substring(14);
                Long number=Long.parseLong(code);
                number=number+1;
                String str=number.toString();
                int num=str.length();
                for (int i=0;i<(5-num);i++){
                    str="0"+str;
                }
                billNo=time+"-"+(str);
            }
            RewardSchemeDto rewardSchemeDto = new RewardSchemeDto();
            BeanUtils.copyProperties(rewardSchemeVo,rewardSchemeDto);
            rewardSchemeDto.setBillNo(billNo);
            rewardSchemeDto.setId(null);
            rewardSchemeDto.setApprovalStatus(ApproveStatusEnum.AUDIT.getStatus());
            rewardSchemeDto.setStatus(RewardSchemeEnum.DRAFT.getStatus());
            if(StringUtils.isNotEmpty(rewardSchemeVo.getBillDate())){
                String str = rewardSchemeVo.getBillDate()+" 00:00:00";
                LocalDateTime dateTime = LocalDateTime.parse(str, formatter);
                rewardSchemeDto.setBillDate(dateTime);
            }
            if(StringUtils.isNotEmpty(rewardSchemeVo.getStartTime())){
                String str = rewardSchemeVo.getStartTime()+" 00:00:00";
                LocalDateTime dateTime = LocalDateTime.parse(str, formatter);
                rewardSchemeDto.setStartTime(dateTime);
            }
            if(StringUtils.isNotEmpty(rewardSchemeVo.getEndTime())){
                String str = rewardSchemeVo.getEndTime()+" 23:59:59";
                LocalDateTime dateTime = LocalDateTime.parse(str, formatter);
                rewardSchemeDto.setEndTime(dateTime);
            }
            List<RewardSchemeDetDto>dataList = new ArrayList<>();
            List<RewardSchemeDetVo> list = rewardSchemeDetMapper.getRewardSchemeDet(rewardSchemeVo.getId());
            for (RewardSchemeDetVo rewardSchemeDetVo : list) {
                RewardSchemeDetDto rewardSchemeDetDto = new RewardSchemeDetDto();
                BeanUtils.copyProperties(rewardSchemeDetVo,rewardSchemeDetDto);
                List<RewardSchemeDetProductVo> rewardSchemeDetProductList = rewardSchemeDetVo.getRewardSchemeDetProductList();
                List<RewardSchemeDetNonProductVo> rewardSchemeDetNonProductList = rewardSchemeDetVo.getRewardSchemeDetNonProductList();
                List<RewardSchemeDetProductDto>rewardSchemeDetProductDtoList = new ArrayList<>();
                if(rewardSchemeDetProductList!=null&&rewardSchemeDetProductList.size()>0){
                    for (RewardSchemeDetProductVo rewardSchemeDetProductVo : rewardSchemeDetProductList) {
                        RewardSchemeDetProductDto rewardSchemeDetProductDto = new RewardSchemeDetProductDto();
                        BeanUtils.copyProperties(rewardSchemeDetProductVo,rewardSchemeDetProductDto);
                        rewardSchemeDetProductDtoList.add(rewardSchemeDetProductDto);
                    }
                }
                List<RewardSchemeDetNonProductDto>rewardSchemeDetNonProductDtoList = new ArrayList<>();
                if(rewardSchemeDetNonProductList!=null&&rewardSchemeDetNonProductList.size()>0){
                    for (RewardSchemeDetNonProductVo rewardSchemeDetNonProductVo : rewardSchemeDetNonProductList) {
                        RewardSchemeDetNonProductDto rewardSchemeDetNonProductDto = new RewardSchemeDetNonProductDto();
                        BeanUtils.copyProperties(rewardSchemeDetNonProductVo,rewardSchemeDetNonProductDto);
                        rewardSchemeDetNonProductDtoList.add(rewardSchemeDetNonProductDto);
                    }
                }
                rewardSchemeDetDto.setRewardSchemeDetProductList(rewardSchemeDetProductDtoList);
                rewardSchemeDetDto.setRewardSchemeDetNonProductList(rewardSchemeDetNonProductDtoList);
                rewardSchemeDetDto.setId(null);
                dataList.add(rewardSchemeDetDto);
            }
            rewardSchemeDto.setName(rewardSchemeDto.getName()+"-复制");
            rewardSchemeDto.setList(dataList);
            add(rewardSchemeDto);
        }
    }

    @Override
    @Transactional
    public void updateStatus() {
        LambdaQueryWrapper<RewardScheme>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RewardScheme::getApprovalStatus,ApproveStatusEnum.APPROVED.getStatus());
        wrapper.eq(RewardScheme::getDeleted,CommonConstants.NUMBER_ZERO);
        wrapper.ne(RewardScheme::getStatus,RewardSchemeEnum.STOP.getStatus());
        List<RewardScheme> list = baseMapper.selectList(wrapper);
        if(list!=null&&list.size()>0){
            for (RewardScheme rewardScheme : list) {
                LocalDateTime startTime = rewardScheme.getStartTime();
                LocalDateTime endTime = rewardScheme.getEndTime();
                LocalDateTime now = LocalDateTime.now();

                int result1 = startTime.compareTo(now);
                int result2 = endTime.compareTo(now);

                if(result1<0&&result2>0){
                    rewardScheme.setStatus(RewardSchemeEnum.YES.getStatus());
                    baseMapper.updateById(rewardScheme);
                }else{
                    rewardScheme.setStatus(RewardSchemeEnum.NO.getStatus());
                    baseMapper.updateById(rewardScheme);
                }
            }
        }
    }

    @Override
    @Transactional
    public PageUtils<RewardSchemeShowVo> searchRewardSchemeDet(RewardSchemeShowParam rewardSchemeShowParam) {
        updateStatus();
        IPage<RewardSchemeShowVo> page = this.baseMapper.searchRewardSchemeDet(new Page<>(rewardSchemeShowParam.getCurrent(), rewardSchemeShowParam.getSize()), rewardSchemeShowParam);
        List<RewardSchemeShowVo> records = page.getRecords();
        // 获取订单会员类型名
        if (CollUtil.isNotEmpty(records)) {
            for (RewardSchemeShowVo record : records) {
                String orderMemberTypeIds = record.getOrderMemberTypeIds();
                if (StringUtils.isNotEmpty(orderMemberTypeIds)) {
                    String[] orderMemberTypeArr = orderMemberTypeIds.split(",");
                    StringBuilder orderMemberTypeNames = new StringBuilder();
                    for (String typeId : orderMemberTypeArr) {
                        MemberType memberType = remoteMiniAccountService.getMemberTypeById(Long.valueOf(typeId));

                        orderMemberTypeNames.append(
                                memberType == null || memberType.getName() == null ?
                                "-" : memberType.getName() + ",");
                    }
                    record.setOrderMemberTypeName(orderMemberTypeNames.substring(0, orderMemberTypeNames.length() - 1));
                }

                if (record.getMemberTypeId()!=null){
                    MemberType memberType = remoteMiniAccountService.getMemberTypeById(Long.valueOf(record.getMemberTypeId()));
                    String MemberTypeName = memberType.getName()==null?"":memberType.getName();
                    record.setMemberTypeName(MemberTypeName);
                }
            }
        }
        return new PageUtils<>(page);
    }

    private static boolean isNumeric(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        // 正则表达式规则，匹配整数和小数
        // ^表示字符串的开始，$表示字符串的结束，中间的+表示匹配前面的字符或组一次或多次
        // \D* 表示非数字字符可以出现0次或多次（小数点前后都可能有空白字符）
        // \d+ 表示至少有一位数字
        // (\.\d+)? 表示小数点和至少一位数字可以有0次或1次出现（小数点前后的数字）
        String regex = "^(\\d+(\\.\\d+)?)$";
        return str.matches(regex);
    }

    @Override
    public void exportRewardSchemeDet(RewardSchemeShowParam param) {
        // 设置导出最大限制
        HuToolExcelUtils.exportParamToMax(param);

        // 获取查询数据
        PageUtils<RewardSchemeShowVo> pageUtils = searchRewardSchemeDet(param);
        List<RewardSchemeShowVo> dataList = pageUtils.getList();

        // 使用Lambda转换方式导出Excel
        HuToolExcelUtils.exportData(dataList, "奖励方案明细", item -> {
            RewardSchemeDetExcelVo vo = new RewardSchemeDetExcelVo();
            // 有效期格式化：startTime~endTime
            if (StrUtil.isNotBlank(item.getStartTime()) && StrUtil.isNotBlank(item.getEndTime())) {
                vo.setExpirationTime(item.getStartTime() + "~" + item.getEndTime());
            }

            // 奖励类型转换：1->佣金，2->提成
            if (item.getRewardType() != null) {
                vo.setRewardType(item.getRewardType() == 1 ? "佣金" : "提成");
            }

            // 价格类型转换：1->会员价，2->复购价，3->实售价
            if (item.getPriceType() != null) {
                String priceTypeStr = "";
                switch (item.getPriceType()) {
                    case 1:
                        priceTypeStr = "会员价";
                        break;
                    case 2:
                        priceTypeStr = "复购价";
                        break;
                    case 3:
                        priceTypeStr = "实售价";
                        break;
                    default:
                        priceTypeStr = "";
                }
                vo.setPriceType(priceTypeStr);
            }
            return vo;
        });
    }
}
