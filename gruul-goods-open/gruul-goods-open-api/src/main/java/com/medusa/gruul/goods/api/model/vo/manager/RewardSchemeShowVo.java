package com.medusa.gruul.goods.api.model.vo.manager;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:12 2025/3/14
 */
@Data
@ApiModel(value = "RewardSchemeShowVo", description = "奖励方案明细列表查询Vo")
public class RewardSchemeShowVo {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "奖励方案明细id")
    private Long id;

    @ApiModelProperty(value = "奖励方案id")
    private String rewardId;

    @ApiModelProperty(value = "单据编号")
    private String billNo;

    @ApiModelProperty(value = "单据日期")
    private String billDate;

    @ApiModelProperty(value = "有效期开始时间")
    private String startTime;

    @ApiModelProperty(value = "有效期结束时间")
    private String endTime;

    @ApiModelProperty(value = "方案名称")
    private String name;

    @ApiModelProperty(value = "经手人id")
    private String userId;

    @ApiModelProperty(value = "经手人名称")
    private String userName;

    @ApiModelProperty(value = "制单人id")
    private String createUserId;

    @ApiModelProperty(value = "制单人名称")
    private String createUserName;

    @ApiModelProperty(value = "主表备注")
    private String remark;

    @ApiModelProperty(value = "奖励类型：1->佣金；2->提成")
    private Integer rewardType;

    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品规格id")
    private String skuId;

    @ApiModelProperty(value = "商品规格名称")
    private String skuName;

    @ApiModelProperty(value = "价格类型：1->会员价；2->复购价；3->实售价")
    private Integer priceType;

    @ApiModelProperty(value = "会员等级id")
    private String memberLevelId;

    @ApiModelProperty(value = "会员等级名称")
    private String memberLevelName;

    @ApiModelProperty(value = "明细备注")
    private String detRemark;

    @ApiModelProperty(value = "会员类型Id")
    private String memberTypeId;

    @ApiModelProperty(value = "会员类型名称")
    private String memberTypeName;

    @ApiModelProperty(value = "订单会员类型ID")
    private String orderMemberTypeIds;

    @ApiModelProperty(value = "订单会员类型名称")
    private String orderMemberTypeName;

    @ApiModelProperty(value = "分佣标题")
    private String commissionTitle;

    @ApiModelProperty(value = "一级下级比例")
    private String oneCommissionRate;

    @ApiModelProperty(value = "二级下级比例")
    private String twoCommissionRate;

    @ApiModelProperty(value = "一级下级固定金额")
    private String oneCommissionAmount;

    @ApiModelProperty(value = "二级下级固定金额")
    private String twoCommissionAmount;

    @ApiModelProperty(value = "分佣金额来源")
    private Integer commissionAmountSource;

    @ApiModelProperty(value = "分佣金额来源名称")
    private String commissionAmountSourceName;
}