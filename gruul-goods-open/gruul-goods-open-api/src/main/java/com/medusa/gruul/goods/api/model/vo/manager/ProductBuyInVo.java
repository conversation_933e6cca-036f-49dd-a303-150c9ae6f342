package com.medusa.gruul.goods.api.model.vo.manager;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * @Description: 购货入库
 * @Author: qsx
 * @Date:   2022-03-08
 * @Version: V1.0
 */
@Data
@ApiModel(value="ProductBuyInVo", description = "商品入库查询返回信息")
public class ProductBuyInVo  implements Serializable {
    
	/**id*/
    @ApiModelProperty(value = "id")
	private Long id;
	/**创建用户id*/
    @ApiModelProperty(value = "创建用户id")
	private Long createUserId;
	/**修改用户id*/
    @ApiModelProperty(value = "修改用户id")
	private Long updateUserId;
	/**删除状态：0->未删除；1->已删除*/
    @ApiModelProperty(value = "删除状态：0->未删除；1->已删除")
	private Integer isDeleted;
	/**仓库id*/
    @ApiModelProperty(value = "仓库id")
	private Long warehouseId;
	/**入库日期*/
    @ApiModelProperty(value = "入库日期")
	private Date buyDate;
	/**入库单号*/
    @ApiModelProperty(value = "入库单号")
	private String buyNo;
	/**入库类型*/
    @ApiModelProperty(value = "入库类型")
	private String buyType;
	/**入库数量*/
    @ApiModelProperty(value = "入库数量")
	private java.math.BigDecimal buyQuantity;
	/**入库金额*/
    @ApiModelProperty(value = "入库金额")
	private java.math.BigDecimal buyAmount;
	/**经办人id*/
    @ApiModelProperty(value = "经办人id")
	private Long handlerId;
    /**经办人名字*/
    @ApiModelProperty(value = "经办人名字")
    private String handlerName;
	/**制单人id*/
    @ApiModelProperty(value = "制单人id")
	private Long preparerId;
    @ApiModelProperty(value = "制单人")
    private String preparerName;
	/**供应商id*/
    @ApiModelProperty(value = "供应商id")
	private Long supplierId;
    @ApiModelProperty(value = "供应商名")
    private String supplierName;
	/**备注*/
    @ApiModelProperty(value = "备注")
	private String remarks;
    /**购货入库明细*/
    @ApiModelProperty(value = "购货入库明细")
    private List<ProductBuyInItemVo> productBuyInItemVoList;
    /**仓库*/
    @ApiModelProperty(value = "仓库")
    private String warehouse;
    /**入库类型名*/
    @ApiModelProperty(value = "入库类型名")
    private String buyTypeName;

    /**实际出库数量--比例*/
    @ApiModelProperty(value = "实际出库数量")
    private Integer reallyOutStock;

    /**状态 0-草稿 100-已提交*/
    private Integer status;
}
