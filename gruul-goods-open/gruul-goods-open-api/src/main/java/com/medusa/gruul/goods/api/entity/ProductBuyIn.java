package com.medusa.gruul.goods.api.entity;


import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * @Description: 购货入库
 * @Author: qsx
 * @Date:   2022-03-08
 * @Version: V1.0
 */
@Data
@TableName("t_product_buy_in")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="t_product_buy_in对象", description="购货入库")
public class ProductBuyIn extends BaseEntity {
    
	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
	private Long id;
	/**创建用户id*/
    @ApiModelProperty(value = "创建用户id")
	private Long createUserId;
	/**修改用户id*/
    @ApiModelProperty(value = "修改用户id")
	private Long updateUserId;
	/**删除状态：0->未删除；1->已删除*/
    @ApiModelProperty(value = "删除状态：0->未删除；1->已删除")
	private Integer isDeleted;
	/**仓库id*/
    @ApiModelProperty(value = "仓库id")
	private Long warehouseId;
	/**入库日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "入库日期")
	private Date buyDate;
	/**入库单号*/
    @ApiModelProperty(value = "入库单号")
	private String buyNo;
	/**入库类型*/
    @ApiModelProperty(value = "入库类型")
	private String buyType;
	/**入库数量*/
    @ApiModelProperty(value = "入库数量")
	private java.math.BigDecimal buyQuantity;
	/**入库金额*/
    @ApiModelProperty(value = "入库金额")
	private java.math.BigDecimal buyAmount;
	/**经办人id*/
    @ApiModelProperty(value = "经办人id")
	private Long handlerId;
	/**制单人id*/
    @ApiModelProperty(value = "制单人id")
	private Long preparerId;
	/**供应商id*/
    @ApiModelProperty(value = "供应商id")
	private Long supplierId;
	/**备注*/
    @ApiModelProperty(value = "备注")
	private String remarks;

	/**状态 0-草稿 100-已提交*/
	private Integer status;
}
