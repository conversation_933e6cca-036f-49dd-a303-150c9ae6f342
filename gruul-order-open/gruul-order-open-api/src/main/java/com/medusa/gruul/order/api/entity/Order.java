package com.medusa.gruul.order.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.medusa.gruul.common.data.base.BaseEntity;
import com.medusa.gruul.order.api.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_order")
@ApiModel(value = "Order对象", description = "订单表")
public class Order extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    @ApiModelProperty(value = "订单id")
    @TableId(value = "id", type = IdType.ID_WORKER)
    private Long id;

    /**
     * 商铺ID
     */
    @ApiModelProperty(value = "商铺ID")
    @TableField("shop_id")
    private String shopId;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @TableField("user_id")
    private String userId;

    /**
     * 用户帐号
     */
    @ApiModelProperty(value = "用户帐号")
    @TableField("user_name")
    private String userName;

    /**
     * 用户头像
     */
    @ApiModelProperty(value = "用户头像")
    @TableField("user_avatar_url")
    private String userAvatarUrl;

    /**
     * 用户备注
     */
    @ApiModelProperty(value = "用户备注")
    @TableField("user_note")
    private String userNote;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    @TableField("type")
    private OrderTypeEnum type;

    /**
     * 订单总金额=商品指导价*商品数量
     */
    @ApiModelProperty(value = "订单总金额")
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 实际金额=应付金额-退款金额
     */
    @ApiModelProperty(value = "实际金额")
    @TableField("discounts_amount")
    private BigDecimal discountsAmount;

    /**
     * 应付金额（实际支付金额）=订单总金额+运费-促销优化金额
     */
    @ApiModelProperty(value = "应付金额（实际支付金额）")
    @TableField("pay_amount")
    private BigDecimal payAmount;

    /**
     * 运费金额=快递运费或送货上门的费用
     */
    @ApiModelProperty(value = "运费金额")
    @TableField("freight_amount")
    private BigDecimal freightAmount;

    /**
     * 促销优化金额=积分抵扣+优惠券+满减+会员价+会员免运费
     */
    @ApiModelProperty(value = "促销优化金额（促销价、满减、会员价）")
    @TableField("promotion_amount")
    private BigDecimal promotionAmount;


    /**
     * 用户优惠券id
     */
    @ApiModelProperty(value = "用户优惠券id")
    @TableField("coupon_id")
    private Long couponId;


    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式")
    @TableField("pay_type")
    private PayTypeEnum payType;

    /**
     * 支付流水号
     */
    @ApiModelProperty(value = "支付流水号")
    @TableField("transaction_id")
    private String transactionId;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    @TableField("pay_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime payTime;

    /**
     * 订单来源
     */
    @ApiModelProperty(value = "订单来源")
    @TableField("source_type")
    private SourceTypeEnum sourceType;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    @TableField("status")
    private OrderStatusEnum status;

    /**
     * 关闭时间
     */
    @ApiModelProperty(value = "关闭时间")
    @TableField("close_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime closeTime;


    /**
     * 订单备注
     */
    @ApiModelProperty(value = "订单备注")
    @TableField("note")
    private String note;

    /**
     * 评价时间
     */
    @ApiModelProperty(value = "评价时间")
    @TableField("comment_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime commentTime;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    @TableField("complete_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime completeTime;

    /**
     * 预计到货时间
     */
    @ApiModelProperty(value = "预计到货时间")
    @TableField("estimated_delivery_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 过期时间
     */
    @ApiModelProperty(value = "过期时间")
    @TableField("expire_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime expireTime;


    /**
     * 自定义字段:[{"key":"IDCard","value":"332022199001010011"},{"key":"Phone","value":"13956852259"}]
     */
    @ApiModelProperty(value = "自定义字段:[{\"key\":\"IDCard\",\"value\":\"332022199001010011\"},{\"key\":\"Phone\"," +
            "\"value\":\"13956852259\"}]")
    @TableField("custom_form")
    private String customForm;


    /**
     * 退款金额
     */
    @ApiModelProperty(value = "退款金额")
    @TableField("refund_amount")
    private BigDecimal refundAmount;

    /**
     * 退款支付流水号
     */
    @ApiModelProperty(value = "退款支付流水号")
    @TableField("refund_transaction_id")
    private String refundTransactionId;

    /**
     * 仓库id
     */
    @ApiModelProperty(value = "仓库id")
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    @TableField("approved_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime approvedTime;

    /**
     * 应付积分
     */
    @ApiModelProperty(value = "应付积分")
    @TableField("all_integral")
    private BigDecimal allIntegral;

    /**
     * 订单支付id
     */
    @ApiModelProperty(value = "订单支付id")
    @TableField("pay_id")
    private Long payId;

    /**
     * 微信发货管理状态
     */
    @ApiModelProperty(value = "微信发货管理状态->0.发货成功；1.收货成功")
    @TableField("wx_deliver_status")
    private WxDeliverStatusEnum wxDeliverStatusEnum;

    /**
     * 打印次数
     */
    @ApiModelProperty(value = "打印次数")
    @TableField("print_time")
    private Integer printTime;

    /**
     * 优惠金额
     */
    @ApiModelProperty(value = "优惠金额")
    @TableField("youhui_price")
    private BigDecimal youhuiPrice;




    /**
     * 商超订单类型->1.普通订单；2->权益包订单；3->组合商品订单；5->升级订单
     */
    @ApiModelProperty(value = "商超订单类型->1.普通订单；2->权益包订单；3->组合商品订单；5->升级订单")
    @TableField("mall_order_type")
    private Integer mallOrderType;


    /**发送状态*/
    @ApiModelProperty(value = "发送状态")
    @TableField("send_status")
    private Integer sendStatus;

    /**
     * 部门id
     */
    @ApiModelProperty("部门id")
    @TableField("department_id")
    private String departmentId;
    /**
     * 部门编码
     */
    @ApiModelProperty("部门编码")
    @TableField("department_code")
    private String departmentCode;

    /**
     * 部门名称
     */
    @ApiModelProperty("部门名称")
    @TableField("department_name")
    private String departmentName;

    /**
     * 门店标识
     */
    @ApiModelProperty("门店标识")
    @TableField("store_front_id")
    private String storeFrontId;

    /**
     * 门店编码
     */
    @ApiModelProperty("门店编码")
    @TableField("store_front_code")
    private String storeFrontCode;

    /**
     * 门店名称
     */
    @ApiModelProperty("门店名称")
    @TableField("store_front_name")
    private String storeFrontName;

    /**
     * 职员id
     */
    @ApiModelProperty("职员id")
    @TableField("employee_id")
    private String employeeId;
    /**
     * 职员标识
     */
    @ApiModelProperty("职员标识")
    @TableField("employee_out_id")
    private String employeeOutId;

    /**
     * 经手人姓名
     */
    @ApiModelProperty("经手人姓名")
    @TableField("employee_name")
    private String employeeName;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    @TableField("account_id")
    private String accountId;

    /**
     * 用户名称
     */
    @ApiModelProperty("用户名称")
    @TableField("account_name")
    private String accountName;

    /**
     * 收款同步状态->0.未同步；1.已同步
     */
    @ApiModelProperty(value = "收款同步状态->0.未同步；1.已同步")
    @TableField("receive_sync_status")
    private Integer receiveSyncStatus;

    /**
     * 邀请用户id
     */
    @ApiModelProperty(value = "邀请用户id")
    @TableField("invite_user_id")
    private String inviteUserId;

    /**
     * 扣减金额
     */
    @ApiModelProperty(value = "扣减金额")
    @TableField("deduction_price")
    private BigDecimal deductionPrice;

    /**
     * 扣减人
     */
    @ApiModelProperty(value = "扣减人")
    @TableField("deduction_user")
    private String deductionUser;

    /**
     * 扣减时间
     */
    @ApiModelProperty(value = "扣减时间")
    @TableField("deduction_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime deductionTime;

    /**
     * 会员等级id
     */
    @ApiModelProperty(value = "会员等级id")
    @TableField("member_id")
    private String memberId;
    /**
     * 省级编码
     */
    @ApiModelProperty(value = "省级编码")
    @TableField("province_code")
    private String provinceCode;
    /**
     * 市级编码
     */
    @ApiModelProperty(value = "市级编码")
    @TableField("city_code")
    private String cityCode;
    /**
     * 区/县编码
     */
    @ApiModelProperty(value = "区/县编码")
    @TableField("county_code")
    private String countyCode;
    /**
     * 下单人用户ID
     */
    @ApiModelProperty(value = "下单人用户ID")
    @TableField("order_user_id")
    private String orderUserId;

    /**
     * 现金金额
     */
    @ApiModelProperty(value = "现金金额")
    @TableField("cash_amount")
    private BigDecimal cashAmount;
    /**
     * 佣金支付金额
     */
    @ApiModelProperty(value = "佣金支付金额")
    @TableField("commission_amount")
    private BigDecimal commissionAmount;
    /**
     * 金豆支付金额
     */
    @ApiModelProperty(value = "金豆支付金额")
    @TableField("golden_amount")
    private BigDecimal goldenAmount;

    /**
     * 上传效果图状态：0-否；1-是
     */
    @ApiModelProperty(value = "上传效果图状态：0-否；1-是")
    @TableField("upload_effect_status")
    private Integer uploadEffectStatus;
    /**
     * 升级会员类型ID
     */
    @ApiModelProperty(value = "升级会员类型ID")
    @TableField("upgrade_member_type_id")
    private Long upgradeMemberTypeId;
    /**
     * 升级会员等级ID
     */
    @ApiModelProperty(value = "升级会员等级ID")
    @TableField("upgrade_member_lever_id")
    private Long upgradeMemberLeverId;

    @ApiModelProperty(value = "订单组ID")
    @TableField("order_group_id")
    private Long orderGroupId;

}
