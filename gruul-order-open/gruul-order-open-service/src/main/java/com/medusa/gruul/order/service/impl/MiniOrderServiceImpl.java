package com.medusa.gruul.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.*;
import com.medusa.gruul.account.api.enums.BlacklistEnum;
import com.medusa.gruul.account.api.enums.MiniAccountExtendsStatusEnum;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.api.model.*;
import com.medusa.gruul.account.api.model.message.AccountReturnBalanceMessage;
import com.medusa.gruul.account.api.model.vo.ApiMemberLevelVo;
import com.medusa.gruul.account.api.model.vo.MiniAccountCouponByOrderVo;
import com.medusa.gruul.afs.api.entity.AfsOrder;
import com.medusa.gruul.afs.api.enums.AfsOrderCloseTypeEnum;
import com.medusa.gruul.afs.api.enums.AfsOrderStatusEnum;
import com.medusa.gruul.afs.api.feign.RemoteAfsService;
import com.medusa.gruul.afs.api.model.AfsSimpleVo;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.TimeConstants;
import com.medusa.gruul.common.core.constant.enums.MemberLevelRuleTypeEnum;
import com.medusa.gruul.common.core.constant.enums.MemberTypeStatusEnum;
import com.medusa.gruul.common.core.encrypt.AESUtil;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.*;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.constant.GoodsSkuStockRedisKey;
import com.medusa.gruul.goods.api.constant.IntegralGoodsExchangeNumRedisKey;
import com.medusa.gruul.goods.api.entity.*;
import com.medusa.gruul.goods.api.enums.ProductStatusEnum;
import com.medusa.gruul.goods.api.enums.ProductTypeEnum;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.dto.api.MiniOrderCouponDto;
import com.medusa.gruul.goods.api.model.vo.manager.ItemVo;
import com.medusa.gruul.goods.api.model.vo.manager.ProductVo;
import com.medusa.gruul.goods.api.model.vo.manager.ShopsItemVo;
import com.medusa.gruul.goods.api.param.OperateExchangeNumParam;
import com.medusa.gruul.goods.api.param.OperateStockDto;
import com.medusa.gruul.logistics.api.feign.RemoteLogisticsFeginService;
import com.medusa.gruul.logistics.model.dto.manager.CountCostDto;
import com.medusa.gruul.logistics.model.dto.manager.LogisticsFreightDto;
import com.medusa.gruul.order.api.constant.OrderCode;
import com.medusa.gruul.order.api.constant.OrderConstant;
import com.medusa.gruul.order.api.constant.OrderFailedRedisKey;
import com.medusa.gruul.order.api.constant.OrderQueueEnum;
import com.medusa.gruul.order.api.entity.*;
import com.medusa.gruul.order.api.enums.*;
import com.medusa.gruul.order.api.model.*;
import com.medusa.gruul.order.api.enums.MemberPriceTypeEnum;
import com.medusa.gruul.order.mapper.*;
import com.medusa.gruul.order.model.*;
import com.medusa.gruul.order.mq.Sender;
import com.medusa.gruul.order.service.IMiniOrderService;
import com.medusa.gruul.order.service.IOrderDeliveryProxyItemService;
import com.medusa.gruul.order.service.IOrderDeliveryProxyService;
import com.medusa.gruul.order.service.IOrderShareSettingService;
import com.medusa.gruul.payment.api.feign.RemotePaymentService;
import com.medusa.gruul.payment.api.model.dto.PayRequestDto;
import com.medusa.gruul.payment.api.model.dto.PayResultDto;
import com.medusa.gruul.payment.api.model.dto.RefundNotifyResultDto;
import com.medusa.gruul.platform.api.entity.AccountInfo;
import com.medusa.gruul.platform.api.entity.SpecialSetting;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.platform.api.model.dto.ShopConfigDto;
import com.medusa.gruul.platform.api.model.dto.WxSendMessageDto;
import com.medusa.gruul.platform.api.model.vo.*;
import com.medusa.gruul.shops.api.entity.ShopCoupon;
import com.medusa.gruul.shops.api.entity.ShopsPartner;
import com.medusa.gruul.shops.api.enums.*;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import com.medusa.gruul.shops.api.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019 -09-02
 */
@Slf4j
@Service
public class MiniOrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements IMiniOrderService {
    @Resource
    private RemoteGoodsService remoteGoodsService;
    @Resource
    private RemotePaymentService remotePaymentService;
    @Resource
    private RemoteMiniAccountService remoteMiniAccountService;
    @Resource
    private RemoteLogisticsFeginService remoteLogisticsFeginService;
    @Resource
    private IOrderShareSettingService orderShareSettingService;
    @Resource
    private OrderDeliveryMapper orderDeliveryMapper;
    @Resource
    private OrderSettingMapper orderSettingMapper;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private OrderEvaluateMapper orderEvaluateMapper;
    @Resource
    private OrderProductEvaluateMapper productEvaluateMapper;
    @Resource
    private Sender sender;
    @Resource
    private RemoteMiniInfoService remoteMiniInfoService;
    @Resource
    private AfsOrderMapper afsOrderMapper;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private RemoteShopsService remoteShopsService;
    @Resource
    private SalesItemLydHistoryMapper salesItemLydHistoryMapper;
    @Autowired
    private IOrderDeliveryProxyService orderDeliveryProxyService;
    @Autowired
    private IOrderDeliveryProxyItemService orderDeliveryProxyItemService;
    @Autowired
    private RemoteAfsService remoteAfsService;

    /**
     * 根据用户选择的商品返回结算页需要的数据
     *
     * @param dto
     * @return com.medusa.gruul.order.model.ConfirmOrderVo
     * <AUTHOR>
     * @date 2020/7/26 14:19
     */
    @Override
    public ConfirmOrderVo getConfirmOrder(ConfirmOrderDto dto) {
        TimeInterval timer = DateUtil.timer();
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        if (ObjectUtil.isNull(curUserDto)) {
            throw new ServiceException(SystemCode.UNAUTHORIZED);
        }
        log.info("当前用户信息:" + curUserDto.toString());
        //查询用户持有的积分、收货地址
        AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(curUserDto.getUserId(), Arrays.asList(1,2,
                3, 5));

        // 判断当前用户是否注册，如果未注册则提示
        if(accountInfoDto.getMiniAccountunt() != null && !accountInfoDto.getMiniAccountunt().getWhetherAuthorization()){
            throw new ServiceException("您尚未注册无法下单，请先注册登录！");
        }

        ConfirmOrderVo result = new ConfirmOrderVo();

        //会员id
        String memberId = accountInfoDto.getMiniAccountunt().getMemberLevelId();
        MemberLevel memberLevel = remoteMiniAccountService.getMemberLevel(memberId);

        log.info("当前用户会员id:" + memberId);
        GoodsSkuStockRedisKey redisStock = new GoodsSkuStockRedisKey();
        IntegralGoodsExchangeNumRedisKey exchangeNumRedisKey = new IntegralGoodsExchangeNumRedisKey();
        //查询商品库存情况
        List<ItemVo> itemVoList = remoteGoodsService.findItemVoByIds(dto.getItemSkuIds());
        List<MemberLevelGoodsPrice> memberLevelGoodsPriceList = remoteGoodsService.selectMemberGoodsPrice(memberId);
        if (CollUtil.isEmpty(itemVoList)) {
            throw new ServiceException(OrderCode.DATA_HAS_BEEN_UPDATED);
        }
        Map<Long, ItemVo> itemVoMap = itemVoList.stream().collect(Collectors.toMap(ItemVo::getProductSkuId, v -> v));

        //是否为积分商品
        Boolean isIntegralProduct = false;;
        if(dto.getType()!=null&&dto.getType().getCode()==OrderTypeEnum.INTEGRAL.getCode()){
            isIntegralProduct = true;
        }
        String shopId = "";
        for (ItemDto item : dto.getItems()) {
            ItemVo itemVo = itemVoMap.get(item.getSkuId());
            shopId = itemVo.getShopId();
            itemVo.setProductQuantity(item.getNumber());//productPrice
            String stock = redisStock.get(item.getSkuId().toString());
            List<MemberLevelGoodsPrice> memberLevelGoodsPrices = memberLevelGoodsPriceList.stream().filter(i -> i.getProductId().equals(itemVo.getProductId())).collect(Collectors.toList());
            //获取商品规格会员价
            memberLevelGoodsPrices = memberLevelGoodsPrices.stream().filter(memberLevelGoodsPrice->  memberLevelGoodsPrice.getSkuId().equals(item.getSkuId())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(memberLevelGoodsPrices)) {
                if (memberLevelGoodsPrices.get(0).getMemberLevelPrice()!=null){
                    if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==itemVo.getMemberPriceType()){
                        itemVo.setProductPrice(memberLevelGoodsPrices.get(0).getMemberLevelPrice());
                    }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==itemVo.getMemberPriceType()){
                        itemVo.setProductPrice(itemVo.getProductPrice().multiply(memberLevelGoodsPrices.get(0).getMemberLevelPrice()).divide(new BigDecimal(100),4, BigDecimal.ROUND_HALF_UP));
                    }
                }
            }
            if (ObjectUtil.isNull(stock)) {
                throw new ServiceException(OrderCode.THIRD_PARTY_SERVICE_EXCEPTION);
            }
            if (new BigDecimal(stock).compareTo(new BigDecimal(item.getNumber())) > 0) {
                //库存状态：0->有库存
                itemVo.setStatus(0);
            } else {
                //库存状态：1->无库存
                itemVo.setStatus(1);
            }

            if(isIntegralProduct){
                //可兑换数
                String exchangeNum = exchangeNumRedisKey.get(item.getIntegralProductId());
                if (new BigDecimal(exchangeNum).compareTo(new BigDecimal(item.getNumber())) > 0) {
                    //兑换状态：：0->可兑换数足够
                    itemVo.setExchangeStatus(0);
                } else {
                    //兑换状态：1->可兑换数不足
                    itemVo.setExchangeStatus(1);
                }
                String integralProductId = item.getIntegralProductId();
                IntegralProduct integralProduct = remoteGoodsService.getIntegralProduct(integralProductId);
                if(integralProduct==null){
                    throw new ServiceException("积分商品不存在！");
                }
                BigDecimal userAllExchangeNum = integralProduct.getUserExchangeNum();
                String skuId = integralProduct.getSkuId();
                Long productId = integralProduct.getProductId();

                BigDecimal userExchangeNum = this.baseMapper.getUserExchangeNumByUserId(curUserDto.getUserId(),skuId,productId);
                if(userExchangeNum==null){
                    userExchangeNum = new BigDecimal(0);
                }
                if((userExchangeNum.add(new BigDecimal(item.getNumber()))).compareTo(userAllExchangeNum)>0){
                    throw new ServiceException("兑换数量已经超过用户限兑数量！");
                }
                itemVo.setAmount(integralProduct.getAmount());
                itemVo.setIntegral(integralProduct.getIntegral());
                itemVo.setIntegralProductId(integralProductId);
            }
        }
        result.setShopId(shopId);
        if(memberLevel!=null&&!memberLevel.getDeleted()&&memberLevel.getDisable()==CommonConstants.NUMBER_ZERO){
            result.setStockFlag(memberLevel.getStockFlag());
            if(memberLevel.getStockFlag()==CommonConstants.NUMBER_ONE){
                Warehouse warehouse = remoteGoodsService.getWarehouseStock(shopId);
                result.setWarehouse(warehouse);
            }
        }
        result.setItemVoList(itemVoList);
        log.info(ObjectUtil.isNotNull(itemVoList) ? itemVoList.toString() : "");
        log.info("查询商品耗时{}ms", timer.intervalRestart());
        result.setMiniAccountAddress(accountInfoDto.getMiniAccountAddress());
        log.info(ObjectUtil.isNotNull(accountInfoDto) ? accountInfoDto.toString() : "");
        log.info("查询用户耗时{}ms", timer.intervalRestart());
        result.setUserIntegral(accountInfoDto.getMiniAccountunt().getCurrentIntegral());
        return result;
    }

    @Override
    public ShopsConfirmOrderVo getShopsConfirmOrderVo(ConfirmOrderDto dto) {
        TimeInterval timer = DateUtil.timer();

        String shopUserId = "";
        if(dto.getReplaceCreateOrderFlag()!=null&& dto.getReplaceCreateOrderFlag().equals(CommonConstants.NUMBER_ONE)){

            if(StringUtils.isEmpty(dto.getUserId())){
                throw new ServiceException("代下单需要认证用户信息！");
            }

            shopUserId = remoteMiniAccountService.getShopUserIdByUserId(dto.getUserId());

            if(StringUtils.isEmpty(shopUserId)){
                throw new ServiceException("代下单用户信息不存在！");
            }

        }else{
            CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
            if (ObjectUtil.isNull(curUserDto)) {
                throw new ServiceException(SystemCode.UNAUTHORIZED);
            }
            log.info("当前用户信息:" + curUserDto.toString());
            shopUserId = curUserDto.getUserId();
        }


        //查询用户持有的积分、收货地址
        AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(shopUserId, Arrays.asList(1,2,
                3, 5));

        // 判断当前用户是否注册，如果未注册则提示
        if(accountInfoDto.getMiniAccountunt() != null && !accountInfoDto.getMiniAccountunt().getWhetherAuthorization()){
            throw new ServiceException("您尚未注册无法下单，请先注册登录！");
        }

        ShopsConfirmOrderVo result = new ShopsConfirmOrderVo();
        GoodsSkuStockRedisKey redisStock = new GoodsSkuStockRedisKey();
        IntegralGoodsExchangeNumRedisKey exchangeNumRedisKey = new IntegralGoodsExchangeNumRedisKey();

        //查询商品库存情况
        //List<ItemVo> itemVoList = remoteGoodsService.findItemVoByIds(dto.getItemSkuIds());

        List<ItemDto> itemList = dto.getItems();
        List<Map<String,String>>dataList = new ArrayList<>();
        //会员等级列表
        List<MemberLevel>memberLevelList = new ArrayList<>();
        for (ItemDto itemDto : itemList) {
            //会员类型id
            Long memberTypeId = itemDto.getMemberTypeId();
            //若商品传入的会员类型为空，则将默认会员等级所属的会员类型作为传入的会员类型
            if(memberTypeId == null){
                //获取默认会员等级
                MemberLevel memberLevel = remoteMiniAccountService.getDefaultMemberLevel();
                if(memberLevel == null){
                    throw new ServiceException("默认会员等级为空！");
                }
                memberTypeId = memberLevel.getMemberTypeId();
                // 设置默认会员类型
                itemDto.setMemberTypeId(memberTypeId);
            }


            String memberLevelId = null;
            //升级订单
            if(dto.getMallOrderType()!=null&&dto.getMallOrderType() == ProductTypeEnum.UPGRADE_PRODUCT.getStatus()){
                memberLevelId = "";
                Long upgradeMemberTypeId = dto.getUpgradeMemberTypeId();
                Long upgradeMemberLevelId = dto.getUpgradeMemberLevelId();
                //根据升级升级会员类型id，升级会员等级id验证是否满足最低升级
                Boolean upgradeFlag = remoteMiniAccountService.checkUpgradePreLow(upgradeMemberTypeId,upgradeMemberLevelId,shopUserId);
                if(!upgradeFlag){
                   throw new ServiceException("当前用户未满足升级等级需要前置最低会员等级！");
                }
            }else{
                memberLevelId = remoteMiniAccountService.getMemberLevelId(accountInfoDto.getMiniAccountunt().getUserId(), memberTypeId);
                if(StringUtils.isEmpty(memberLevelId)){
                    throw new ServiceException("您没有权限购买，请先升级！");
                }
                //判断商品会员类型列表A每个会员类型该会员所处的会员等级是否是会员体系会员
                MemberLevel memberLevel = remoteMiniAccountService.getMemberLevel(memberLevelId);
                if(memberLevel == null){
                    throw new ServiceException("会员等级不存在！");
                }
//                if(memberLevel.getMemberFlag() == MemberFlagEnum.NO.getStatus()){
//                    throw new ServiceException("还未是会员，请先升级！");
//                }
                memberLevelList.add(memberLevel);
            }
            itemDto.setMemberLevelId(memberLevelId);
            Map<String,String>dataMap = new HashMap<>();
            dataMap.put("skuId",itemDto.getSkuId()+"");
            dataMap.put("memberTypeId",memberTypeId+"");
            dataList.add(dataMap);
        }

        String jsonString = JSON.toJSONString(dataList);
        List<ItemVo> itemVoList = remoteGoodsService.findItemVoByItemVo(jsonString);


        if (CollUtil.isEmpty(itemVoList)) {
            throw new ServiceException(OrderCode.DATA_HAS_BEEN_UPDATED);
        }
        Map<String, ItemVo> itemVoMap = itemVoList.stream().collect(Collectors.toMap(t->t.getProductSkuId()+":"+t.getMemberTypeId(),ItemVo->ItemVo));
        Long upgradeMemberLevelId = dto.getUpgradeMemberLevelId();
        Long upgradeMemberTypeId = dto.getUpgradeMemberTypeId();
        //是否为积分商品
        Boolean isIntegralProduct = false;
        if(dto.getType()!=null&&dto.getType().getCode()==OrderTypeEnum.INTEGRAL.getCode()){
            isIntegralProduct = true;
        }
        List<MiniAccountCouponOrderItemDto> miniAccountCouponOrderItemList = new ArrayList<>();

        for (ItemDto item : dto.getItems()) {

            ItemVo itemVo = itemVoMap.get(item.getSkuId()+":"+item.getMemberTypeId());
            itemVo.setProductQuantity(item.getNumber());//productPrice
            String stock = redisStock.get(item.getSkuId().toString());
            BigDecimal productPrice = itemVo.getProductPrice();

            // 默认首单
            itemVo.setBuyType(BuyTypeEnum.ONE.getCode());
            //升级订单
            if(dto.getMallOrderType()!=null&&dto.getMallOrderType() == ProductTypeEnum.UPGRADE_PRODUCT.getStatus()){
                //升级价格
                if(item.getUpgradeProductPrice()!=null&&item.getUpgradeProductQuantity()!=null&&
                        upgradeMemberLevelId!=null&&upgradeMemberTypeId!=null){
                    itemVo.setProductPrice(item.getUpgradeProductPrice());
                    itemVo.setProductQuantity(item.getUpgradeProductQuantity());
                }

            }else{
                //获取会员价
                List<MemberLevelGoodsPrice> memberLevelGoodsPriceList = remoteGoodsService.selectMemberGoodsPrice(item.getMemberLevelId());
                List<MemberLevelGoodsPrice> memberLevelGoodsPrices = memberLevelGoodsPriceList.stream().filter(i -> i.getProductId().equals(itemVo.getProductId())).collect(Collectors.toList());
                //获取商品规格会员价
                memberLevelGoodsPrices = memberLevelGoodsPrices.stream().filter(memberLevelGoodsPrice->  memberLevelGoodsPrice.getSkuId().equals(item.getSkuId())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(memberLevelGoodsPrices)) {
                    if (memberLevelGoodsPrices.get(0).getMemberLevelPrice()!=null){
                        if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==itemVo.getMemberPriceType()){
                            itemVo.setProductPrice(memberLevelGoodsPrices.get(0).getMemberLevelPrice());
                        }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==itemVo.getMemberPriceType()){
                            itemVo.setProductPrice(productPrice.multiply(memberLevelGoodsPrices.get(0).getMemberLevelPrice()).divide(new BigDecimal(100),4, RoundingMode.HALF_UP));
                        }
                    }
                }

                //判断复购价
                Integer count = orderItemMapper.getCountByMemberType(shopUserId,itemVo.getMemberTypeId(), OrderStatusEnum.getPaidStatus());
                if(count>0){
                    // 复购单
                    itemVo.setBuyType(BuyTypeEnum.AGAIN.getCode());
                    //获取复购价
                    List<MemberLevelGoodsAgainPrice> memberLevelGoodsAgainPriceList = remoteGoodsService.selectMemberGoodsAgainPrice(item.getMemberLevelId());
                    List<MemberLevelGoodsAgainPrice> memberLevelGoodsAgainPrices = memberLevelGoodsAgainPriceList.stream().filter(i -> i.getProductId().equals(itemVo.getProductId())).collect(Collectors.toList());
                    //获取商品规格会员价
                    memberLevelGoodsAgainPrices = memberLevelGoodsAgainPrices.stream().filter(memberLevelGoodsPrice->  memberLevelGoodsPrice.getSkuId().equals(item.getSkuId())).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(memberLevelGoodsAgainPrices)) {
                        if (memberLevelGoodsAgainPrices.get(0).getMemberLevelAgainPrice()!=null){
                            if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==itemVo.getMemberAgainPriceType()){
                                itemVo.setProductPrice(memberLevelGoodsAgainPrices.get(0).getMemberLevelAgainPrice());
                            }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==itemVo.getMemberAgainPriceType()){
                                itemVo.setProductPrice(productPrice.multiply(memberLevelGoodsAgainPrices.get(0).getMemberLevelAgainPrice()).divide(new BigDecimal(100),4, RoundingMode.HALF_UP));
                            }
                        }
                    }
                }
            }
            itemVo.setAmount((itemVo.getProductPrice() == null ? BigDecimal.ZERO : itemVo.getProductPrice())
                    .multiply(new BigDecimal((itemVo.getProductQuantity() == null ? 0 : itemVo.getProductQuantity()))));
            if(StringUtils.isEmpty(stock) || "null".equalsIgnoreCase(stock)){
                throw new ServiceException(OrderCode.THIRD_PARTY_SERVICE_EXCEPTION);
            }
            if (new BigDecimal(stock).compareTo(new BigDecimal(item.getNumber())) > 0) {
                //库存状态：0->有库存
                itemVo.setStatus(0);
            } else {
                //库存状态：1->无库存
                itemVo.setStatus(1);
            }

            if(isIntegralProduct){
                //可兑换数
                String exchangeNum = exchangeNumRedisKey.get(item.getIntegralProductId());
                if (new BigDecimal(exchangeNum).compareTo(new BigDecimal(item.getNumber())) > 0) {
                    //兑换状态：：0->可兑换数足够
                    itemVo.setExchangeStatus(0);
                } else {
                    //兑换状态：1->可兑换数不足
                    itemVo.setExchangeStatus(1);
                }
                String integralProductId = item.getIntegralProductId();
                IntegralProduct integralProduct = remoteGoodsService.getIntegralProduct(integralProductId);
                if(integralProduct==null){
                    throw new ServiceException("积分商品不存在！");
                }
                BigDecimal userAllExchangeNum = integralProduct.getUserExchangeNum();
                String skuId = integralProduct.getSkuId();
                Long productId = integralProduct.getProductId();

                BigDecimal userExchangeNum = this.baseMapper.getUserExchangeNumByUserId(shopUserId,skuId,productId);
                if(userExchangeNum==null){
                    userExchangeNum = new BigDecimal(0);
                }
                if((userExchangeNum.add(new BigDecimal(item.getNumber()))).compareTo(userAllExchangeNum)>0){
                    throw new ServiceException("兑换数量已经超过用户限兑数量！");
                }
                itemVo.setAmount(integralProduct.getAmount());
                itemVo.setIntegral(integralProduct.getIntegral());
                itemVo.setIntegralProductId(integralProductId);

            }

            MiniAccountCouponOrderItemDto miniAccountCouponOrderItemDto = new MiniAccountCouponOrderItemDto();
            miniAccountCouponOrderItemDto.setNumber(new BigDecimal(item.getNumber()));
            miniAccountCouponOrderItemDto.setMemberTypeId(item.getMemberTypeId());
            miniAccountCouponOrderItemDto.setSkuId(item.getSkuId()+"");
            miniAccountCouponOrderItemDto.setProductPrice(itemVo.getProductPrice());
            miniAccountCouponOrderItemDto.setShopId(itemVo.getShopId());
            miniAccountCouponOrderItemDto.setAmount(itemVo.getAmount());
            miniAccountCouponOrderItemList.add(miniAccountCouponOrderItemDto);
        }
        result.setYouhuiPrice(BigDecimal.ZERO);
        result.setCouponId("");
        //查看用户可用优惠券：只查最大满额的一张或者用户手动选择的优惠券
        MiniAccountCouponByOrderDto miniAccountCouponByOrderDto = new MiniAccountCouponByOrderDto();
        miniAccountCouponByOrderDto.setUserId(accountInfoDto.getMiniAccountunt().getUserId());
        miniAccountCouponByOrderDto.setReplaceCreateOrderFlag(dto.getReplaceCreateOrderFlag());
        miniAccountCouponByOrderDto.setData(miniAccountCouponOrderItemList);
        miniAccountCouponByOrderDto.setMiniAccountCouponId(dto.getMiniAccountCouponId());
        miniAccountCouponByOrderDto.setAllFlag(false);

        List<MiniAccountCouponByOrderVo> miniAccountCouponByOrderList = remoteMiniAccountService.getCouponByUser(miniAccountCouponByOrderDto);
        //将返回的优惠券数据转换且分解到订单商品明细的优惠金额字段


        //符合使用优惠券的订单明细总金额
        BigDecimal allRealAmount = BigDecimal.ZERO;
        if(miniAccountCouponByOrderList!=null && !miniAccountCouponByOrderList.isEmpty()){
            if(dto.getMiniAccountCouponId()!=null){
                miniAccountCouponByOrderList = miniAccountCouponByOrderList.stream().filter(e -> e.getId().equals(dto.getMiniAccountCouponId())).collect(Collectors.toList());
            }
            MiniAccountCouponByOrderVo miniAccountCouponByOrderVo = miniAccountCouponByOrderList.get(0);

            result.setCouponId(miniAccountCouponByOrderVo.getId()+"");

            //优惠金额--目前只有满减额
            BigDecimal promotion = miniAccountCouponByOrderVo.getPromotion();
            result.setYouhuiPrice(promotion);
            // 对得到优惠券条件的商品的金额进行求和
            BigDecimal couponTotalAmount = miniAccountCouponByOrderDto.getData().stream().filter(e -> e.getMiniAccountCouponId() != null).map(MiniAccountCouponOrderItemDto::getAmount)
                    .filter(Objects::nonNull) // 过滤 null 值（可选）
                    .reduce(BigDecimal.ZERO, BigDecimal::add); // 初始值为 0，逐个相加

            miniAccountCouponByOrderDto.getData().forEach(e -> {
                if(e.getMiniAccountCouponId() != null){
                    //计算优惠券分解金额
                    BigDecimal youhuiPrice = e.getAmount().divide(couponTotalAmount,2, RoundingMode.HALF_UP).multiply(promotion);
                    String key = e.getSkuId() + ":" + e.getMemberTypeId();
                    if(null != itemVoMap.get(key)){
                        ItemVo itemVo = itemVoMap.get(key);
                        itemVo.setYouhuiPrice(youhuiPrice);
                    }
                }
            });



            /*AccountCouponVo accountCouponVo = remoteShopsService.getCouponById(miniAccountCouponByOrderVo.getCouponId());
            //可用优惠券的店铺，这里没有判断商品，会导致数据不准确
            Boolean shopFlag = accountCouponVo.getShopFlag();
            List<String> shopIds = accountCouponVo.getShopIds();

            for (ItemVo itemVo : itemVoList) {
                boolean b = false;
                if(shopFlag){
                    b = shopIds != null && shopIds.contains(itemVo.getShopId());
                }else{
                    b = true;
                }
                if(b){
                    itemVo.setCouponFlag(Boolean.TRUE);
                    allRealAmount = allRealAmount.add(itemVo.getProductPrice().multiply(new BigDecimal(itemVo.getProductQuantity())));
                }else{
                    itemVo.setCouponFlag(Boolean.FALSE);
                }
            }
            for (ItemVo itemVo : itemVoList) {
                if(itemVo.getCouponFlag()){
                    BigDecimal amount = itemVo.getProductPrice().multiply(new BigDecimal(itemVo.getProductQuantity()));
                    BigDecimal youhuiPrice = amount.divide(allRealAmount,2, RoundingMode.HALF_UP).multiply(accountCouponVo.getPromotion());
                    itemVo.setYouhuiPrice(youhuiPrice);
                }
            }*/
        }
        // 计算满减满赠逻辑
        this.checkFullDonation(itemVoList, accountInfoDto);
        // 已被处理过的满减满赠活动id
        List<Long> fullDonationIdList = new ArrayList<>();
        // 满减满赠活动标识
        AtomicBoolean fullDonationFlag = new AtomicBoolean(false);
        // 活动满减总额
        AtomicReference<BigDecimal> fullDonationAmount = new AtomicReference<>(BigDecimal.ZERO);
        // 活动赠送的优惠券
        List<ShopCoupon> giftCoupons = new ArrayList<>();
        // 活动赠送的商品
        List<ItemVo> giftGoodsItemVoList = new ArrayList<>();
        itemVoList.forEach(itemVo -> {

            if(itemVo.getShopFullDonationRuleVo() != null){
                fullDonationFlag.set(true);
                if(!fullDonationIdList.contains(itemVo.getShopFullDonationRuleVo().getId())){
                    // 计算满减总额
                    if(itemVo.getShopFullDonationRuleVo().getReductionAmount() != null && itemVo.getShopFullDonationRuleVo().getReductionAmount().compareTo(BigDecimal.ZERO) > 0){
                        fullDonationAmount.set(fullDonationAmount.get().add(itemVo.getShopFullDonationRuleVo().getReductionAmount()));
                    }
                    // 计算赠送优惠券
                    if(StrUtil.isNotBlank(itemVo.getShopFullDonationRuleVo().getCouponIds())){
                        // 根据优惠券id查询优惠券
                        // 将逗号分隔的id组装成list，且将string转换成Long类型
                        List<String> couponIdStrList = Arrays.asList(itemVo.getShopFullDonationRuleVo().getCouponIds().split(","));
                        List<Long> couponIdList = couponIdStrList.stream().map(Long::valueOf).collect(Collectors.toList());
                        List<ShopCoupon> shopCouponList = this.remoteShopsService.getCouponByIds(couponIdList);
                        giftCoupons.addAll(shopCouponList);
                    }
                    // 计算赠送的商品
                    if(StrUtil.isNotBlank(itemVo.getShopFullDonationRuleVo().getSkuIds())){
                        // 将逗号分隔的id组装成list，且将string转换成Long类型
                        List<String> skuIdStrList = Arrays.asList(itemVo.getShopFullDonationRuleVo().getSkuIds().split(","));
                        List<Long> skuIdList = skuIdStrList.stream().map(Long::valueOf).collect(Collectors.toList());
                        List<ItemVo> itemVoByIds = this.remoteGoodsService.findItemVoByIds(skuIdList);
                        // 设置itemVoByIds 产品数量为1
                        itemVoByIds.forEach(itemVo2 -> {
                            itemVo2.setProductQuantity(1);
                            itemVo2.setProductPrice(BigDecimal.ZERO);
                            itemVo2.setMemberTypeId(itemVo.getMemberTypeId());
                            itemVo2.setGiftFlag(GiftFlagEnum.YES.getCode());
                            itemVo2.setAmount(itemVo2.getProductPrice().multiply(new BigDecimal(itemVo2.getProductQuantity())));
                        });
                        giftGoodsItemVoList.addAll(itemVoByIds);
                    }
                    fullDonationIdList.add(itemVo.getShopFullDonationRuleVo().getId());
                }
                // 计算赠送自身件数
                if(itemVo.getShopFullDonationRuleVo().getSelfNum() != null && itemVo.getShopFullDonationRuleVo().getSelfNum() > 0){
                    ItemVo giftItemVo = new ItemVo();
                    BeanUtil.copyProperties(itemVo, giftItemVo);
                    giftItemVo.setShopFullDonationRuleVo(null);
                    giftItemVo.setFullDonationId(null);
                    giftItemVo.setProductQuantity(itemVo.getProductQuantity() * itemVo.getShopFullDonationRuleVo().getSelfNum());
                    giftItemVo.setProductPrice(BigDecimal.ZERO);
                    giftItemVo.setGiftFlag(GiftFlagEnum.YES.getCode());
                    giftItemVo.setAmount(giftItemVo.getProductPrice().multiply(new BigDecimal(giftItemVo.getProductQuantity())));
                    giftGoodsItemVoList.add(giftItemVo);
                }

            }
        });

        if(fullDonationFlag.get()){
            // 对商品明细的满减满赠活动进行处理
            ConfirmOrderFullDonationVo orderFullDonationVo = new ConfirmOrderFullDonationVo();
            orderFullDonationVo.setReductionAmount(fullDonationAmount.get());
            orderFullDonationVo.setShopCouponVos(giftCoupons);
            orderFullDonationVo.setGoodsItemVoList(giftGoodsItemVoList);
            result.setConfirmOrderFullDonationVo(orderFullDonationVo);
        }

        //以店铺作为分组组装订单结算数据
        Map<String, List<ItemVo>> listMap = itemVoList.stream().collect(Collectors.groupingBy(ItemVo::getShopId));
        List<ShopsItemVo>list = new ArrayList<>(listMap.size());
        for (Map.Entry<String, List<ItemVo>> entry : listMap.entrySet()) {
            ShopsItemVo shopsItemVo = new ShopsItemVo();
            String shopId = entry.getKey();
            List<ItemVo> itemVos = entry.getValue();
            ShopsPartner shopsPartner = remoteShopsService.getByShopId(Long.valueOf(shopId));
            shopsItemVo.setShopId(Long.valueOf(shopsPartner.getShopId()));
            shopsItemVo.setShopName(shopsPartner.getName());
            shopsItemVo.setItemVos(itemVos);
            //取第一个会员等级做为会员默认仓库
            if(memberLevelList!=null && !memberLevelList.isEmpty()){
                MemberLevel memberLevel = memberLevelList.get(0);
                if(memberLevel!=null&&!memberLevel.getDeleted()&& Objects.equals(memberLevel.getDisable(), CommonConstants.NUMBER_ZERO)){
                    result.setStockFlag(memberLevel.getStockFlag());
                    if(Objects.equals(memberLevel.getStockFlag(), CommonConstants.NUMBER_ONE)){
                        Warehouse warehouse = remoteGoodsService.getWarehouseStock(shopsPartner.getShopId());
                        shopsItemVo.setWarehouse(warehouse);
                    }
                }
            }
            list.add(shopsItemVo);
        }
        result.setShopItemVoList(list);
        log.info(ObjectUtil.isNotNull(itemVoList) ? itemVoList.toString() : "");
        log.info("查询商品耗时{}ms", timer.intervalRestart());
        result.setMiniAccountAddress(accountInfoDto.getMiniAccountAddress());
        log.info(ObjectUtil.isNotNull(accountInfoDto) ? accountInfoDto.toString() : "");
        log.info("查询用户耗时{}ms", timer.intervalRestart());
        result.setUserIntegral(accountInfoDto.getMiniAccountunt().getCurrentIntegral());
        return result;
    }

    private void checkFullDonation(List<ItemVo> itemVoList, AccountInfoDto accountInfoDto){
        // 查询审核通过在有效期内且允许会员所属会员等级能参加的满减满赠活动
        // 1、查询符合会员等级的有效的满减满赠活动
        List<MemberLevelRelation> memberLevelRelations = this.remoteMiniAccountService.getMemberLevelsByUserId(accountInfoDto.getMiniAccountunt().getUserId());
        // 获取会员等级id
        List<String> memberLevelIds = memberLevelRelations.stream().map(MemberLevelRelation::getMemberLevelId).collect(Collectors.toList());

        ShopFullDonationParam shopFullDonationParam = new ShopFullDonationParam();
        shopFullDonationParam.setStartTime(LocalDateTime.from(LocalDate.now().atStartOfDay()));
        shopFullDonationParam.setEndTime(LocalDateTime.from(LocalDate.now().atStartOfDay()));
        shopFullDonationParam.setStatus(ShopFullDonationStatusFlagEnum.PASS.getType());
        shopFullDonationParam.setMemberLevelIdList(memberLevelIds);
        shopFullDonationParam.setUserId(accountInfoDto.getMiniAccountExtends().getShopUserId());
        shopFullDonationParam.setOrderStatusList(OrderStatusEnum.getPaidStatus());
        // 查询满减满赠活动
        List<ShopFullDonationVo> shopFullDonationVos = this.remoteShopsService.getValidShopFullDonation(shopFullDonationParam);
        log.info("查询符合条件满减满赠活动，活动信息：{}", JSONObject.toJSONString(shopFullDonationVos));
        if(shopFullDonationVos!=null && !shopFullDonationVos.isEmpty()){

            // 判断订单明细满足哪个活动
            shopFullDonationVos.parallelStream().forEach(shopFullDonationVo -> {
                // 符合活动规则的商品明细
                List<ItemVo> validItemVoList = new ArrayList<>();
                // 商品明细以规格作为分组
                Map<Long, List<ItemVo>> orderItemDataMap = new HashMap<>();
                List<ItemVo> orderItemGroupList = new ArrayList<>();

                // 是否指定参与商家
                if(Objects.equals(shopFullDonationVo.getShopsFlag(), ShopsFlagEnum.DEFAULT.getType())){
                    // 不指定商家，所有商家都参与
                    validItemVoList = itemVoList;
                }else if(Objects.equals(shopFullDonationVo.getShopsFlag(), ShopsFlagEnum.YES.getType())){
                    // 指定参与商家，获取商家列表
                    List<ShopFullDonationPartnerVo> shopFullDonationPartnerList = shopFullDonationVo.getShopFullDonationPartnerList();
                    // 根据商家id获取商家列表
                    List<Long> shopIdList = shopFullDonationPartnerList.stream().map(ShopFullDonationPartnerVo::getShopsPartnerId).collect(Collectors.toList());
                    List<ShopsPartner> shopPartnerListByIds = this.remoteShopsService.getShopPartnerListByIds(shopIdList);
                    List<String> shopIds = shopPartnerListByIds.stream().map(ShopsPartner::getShopId).collect(Collectors.toList());
                    // 将不在商家shopIds的商品过滤
                    validItemVoList = itemVoList.stream().filter(itemVo -> shopIds.contains(itemVo.getShopId())).collect(Collectors.toList());
                }else if(Objects.equals(shopFullDonationVo.getShopsFlag(), ShopsFlagEnum.NO.getType())){
                    // 指定不参与商家，获取不参与商家列表
                    List<ShopFullDonationPartnerVo> shopFullDonationPartnerList = shopFullDonationVo.getShopFullDonationPartnerList();
                    List<Long> shopIdList = shopFullDonationPartnerList.stream().map(ShopFullDonationPartnerVo::getShopsPartnerId).collect(Collectors.toList());
                    List<ShopsPartner> shopPartnerListByIds = this.remoteShopsService.getShopPartnerListByIds(shopIdList);
                    List<String> shopIds = shopPartnerListByIds.stream().map(ShopsPartner::getShopId).collect(Collectors.toList());
                    // 将在商家shopIds的商品过滤
                    validItemVoList = itemVoList.stream().filter(itemVo -> !shopIds.contains(itemVo.getShopId())).collect(Collectors.toList());
                }
                // 是否指定商品
                if(Objects.equals(shopFullDonationVo.getProductFlag(), ProductFlagEnum.DEFAULT.getType())){
                    // 不指定商品
                }else if(Objects.equals(shopFullDonationVo.getProductFlag(), ProductFlagEnum.YES.getType())){
                    // 指定商品
                    List<ShopFullDonationProductVo> shopFullDonationProductList = shopFullDonationVo.getShopFullDonationProductList();
                    // 获取指定商品的skuId列表
                    List<Long> productSkuIdList = shopFullDonationProductList.stream().map(ShopFullDonationProductVo::getSkuId).collect(Collectors.toList());
                    // 将不在商家商品列表中的商品过滤
                    validItemVoList = validItemVoList.stream().filter(itemVo -> productSkuIdList.contains(itemVo.getProductSkuId())).collect(Collectors.toList());
                }else if(Objects.equals(shopFullDonationVo.getProductFlag(), ProductFlagEnum.NO.getType())){
                    // 指定不参与商品
                    List<ShopFullDonationProductVo> shopFullDonationProductList = shopFullDonationVo.getShopFullDonationProductList();
                    List<Long> productSkuIdList = shopFullDonationProductList.stream().map(ShopFullDonationProductVo::getSkuId).collect(Collectors.toList());
                    validItemVoList = validItemVoList.stream().filter(itemVo -> !productSkuIdList.contains(itemVo.getProductSkuId())).collect(Collectors.toList());
                }

                log.info("满减满赠活动，活动信息：{}，符合商家商品过滤的商品信息：{}", JSONObject.toJSONString(shopFullDonationVo), JSONObject.toJSONString(validItemVoList));
                orderItemDataMap = this.preCheckFullDonationRule(orderItemGroupList, orderItemDataMap, validItemVoList);

                //判断按商品照规格分组后的商品详情是否满足满减满赠活动规则
                List<ShopFullDonationRuleVo> shopFullDonationRuleList = shopFullDonationVo.getShopFullDonationRuleList();
                this.checkFullDonationRule(orderItemGroupList, orderItemDataMap, shopFullDonationRuleList);
            });

        }
    }

    /**
     * 满减满赠组装数据
     * @param goodsItemGroupList 商品按规格合并后的商品详情
     * @param orderItemDataMap 商品按规格分组
     * @param itemVoList 商品详情
     */
    private Map<Long, List<ItemVo>> preCheckFullDonationRule(List<ItemVo> goodsItemGroupList, Map<Long, List<ItemVo>> orderItemDataMap, List<ItemVo> itemVoList){
        if(null == itemVoList || itemVoList.isEmpty()){
            return orderItemDataMap;
        }
        // 以规格作为分组
        orderItemDataMap = itemVoList.stream().collect(Collectors.groupingBy(ItemVo::getProductSkuId));
        for (Map.Entry<Long, List<ItemVo>> entry : orderItemDataMap.entrySet()) {
            Long key = entry.getKey();
            List<ItemVo> list = entry.getValue();
            // 同规格数量相加
            Integer number = 0;
            // 同规格金额相加
            BigDecimal amount = BigDecimal.ZERO;
            BigDecimal youhuiAmount = BigDecimal.ZERO;
            // 将同规格产品的数量相加
            for (ItemVo itemDto : list) {
                number = number + itemDto.getProductQuantity();
                BigDecimal realAmount = itemDto.getProductPrice() != null ? itemDto.getProductPrice().multiply(new BigDecimal(itemDto.getProductQuantity()))
                        .setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
                BigDecimal singleYouhuiAmount = itemDto.getYouhuiPrice() != null ? itemDto.getYouhuiPrice() : BigDecimal.ZERO;
                realAmount = realAmount.subtract(singleYouhuiAmount);
                amount = amount.add(realAmount);
                youhuiAmount = youhuiAmount.add(singleYouhuiAmount);
            }
            ItemVo newItemVo = new ItemVo();
            BeanUtil.copyProperties(list.get(0), newItemVo);
            newItemVo.setProductQuantity(number);
            newItemVo.setAmount(amount);
            newItemVo.setYouhuiPrice(youhuiAmount);
            goodsItemGroupList.add(newItemVo);
        }
        return orderItemDataMap;
    }

    /**
     * 对活动规则按照满额类型分别进行判断选择符合条件的活动规则
     * @param itemVoList 按商品规格分组，已对数量、金额求和
     * @param orderItemDataMap 按商品规格分组的商品详情
     * @param shopFullDonationRuleList
     */
    private void checkFullDonationRule(List<ItemVo> itemVoList, Map<Long, List<ItemVo>> orderItemDataMap, List<ShopFullDonationRuleVo> shopFullDonationRuleList){
        if(null != shopFullDonationRuleList && !shopFullDonationRuleList.isEmpty() && null != itemVoList && !itemVoList.isEmpty()){
            // 将商品明细按照购买订单类型分组
            Map<Integer, List<ItemVo>> orderItemBuyTypeMap = itemVoList.stream().collect(Collectors.groupingBy(ItemVo::getBuyType));
            // 将满减满赠规则按照满额类型分组
            Map<Integer, List<ShopFullDonationRuleVo>> shopFullDonationRuleMap = shopFullDonationRuleList.stream().collect(Collectors.groupingBy(ShopFullDonationRuleVo::getFullType));

            // 1. 满额类型
            List<ShopFullDonationRuleVo> shopFullDonationRuleVosAmount = shopFullDonationRuleMap.get(ShopFullDonationFullTypeEnum.AMOUNT.getType());
            if(null != shopFullDonationRuleVosAmount && !shopFullDonationRuleVosAmount.isEmpty() && null != orderItemDataMap && !orderItemDataMap.isEmpty()){
                // 将满减满赠规则按照订单满额由大到小进行排序
                shopFullDonationRuleVosAmount.sort(Comparator.comparing(ShopFullDonationRuleVo::getOrderAmount).reversed());
                for (ShopFullDonationRuleVo shopFullDonationRuleVo : shopFullDonationRuleVosAmount) {
                    this.chooseItemFullDonationRule(orderItemBuyTypeMap, itemVoList, orderItemDataMap, shopFullDonationRuleVo);
                }
            }
            // 2. 满件类型
            List<ShopFullDonationRuleVo> shopFullDonationRuleVosNumber = shopFullDonationRuleMap.get(ShopFullDonationFullTypeEnum.NUMBER.getType());
            if(null != shopFullDonationRuleVosNumber && !shopFullDonationRuleVosNumber.isEmpty() && null != orderItemDataMap && !orderItemDataMap.isEmpty()){
                // 将满减满赠规则按照订单满件由大到小进行排序
                shopFullDonationRuleVosNumber.sort(Comparator.comparing(ShopFullDonationRuleVo::getPieceNum).reversed());
                for (ShopFullDonationRuleVo shopFullDonationRuleVo : shopFullDonationRuleVosNumber) {
                    this.chooseItemFullDonationRule(orderItemBuyTypeMap, itemVoList, orderItemDataMap, shopFullDonationRuleVo);
                }
            }
            // 3. 满件满额
            List<ShopFullDonationRuleVo> shopFullDonationRuleVosNumberAmount = shopFullDonationRuleMap.get(ShopFullDonationFullTypeEnum.NUMBER_AMOUNT.getType());
            if(null != shopFullDonationRuleVosNumberAmount && !shopFullDonationRuleVosNumberAmount.isEmpty() && null != orderItemDataMap && !orderItemDataMap.isEmpty()){
                // 先按金额降序，再按数量降序排列
                shopFullDonationRuleVosNumberAmount.sort(
                        Comparator.comparing(ShopFullDonationRuleVo::getOrderAmount, Comparator.reverseOrder()) // 按金额降序
                                .thenComparing(ShopFullDonationRuleVo::getPieceNum, Comparator.reverseOrder()) // 按数量降序
                );
                for (ShopFullDonationRuleVo shopFullDonationRuleVo : shopFullDonationRuleVosNumberAmount) {
                    this.chooseItemFullDonationRule(orderItemBuyTypeMap, itemVoList, orderItemDataMap, shopFullDonationRuleVo);
                }
            }

        }
    }

    /**
     * 订单明细是否满足满减满赠活动规则
     * @param goodsItemBuyTypeMap 以购买类型分组的商品明细
     * @param itemVoList
     * @param orderItemDataMap
     * @param shopFullDonationRuleVo
     */
    private void chooseItemFullDonationRule(Map<Integer, List<ItemVo>> goodsItemBuyTypeMap, List<ItemVo> itemVoList, Map<Long, List<ItemVo>> orderItemDataMap, ShopFullDonationRuleVo shopFullDonationRuleVo){
        if(null == itemVoList || itemVoList.isEmpty()){
            return;
        }
        log.info("判断订单明细是否满足满减满赠活动规则，活动规则信息：{}，商品购买类型分组信息：{}，商品规格分组信息：{}", JSONObject.toJSONString(shopFullDonationRuleVo),
                JSONObject.toJSONString(goodsItemBuyTypeMap), JSONObject.toJSONString(orderItemDataMap));
        List<ItemVo> itemVosSetting = null;
        if(Objects.equals(shopFullDonationRuleVo.getBuyType(), BuyTypeEnum.ONE.getCode()) ){
            // 1.如果是首单的规则
            //查出首单的商品明细
            itemVosSetting = goodsItemBuyTypeMap.get(BuyTypeEnum.ONE.getCode());
        }else if(Objects.equals(shopFullDonationRuleVo.getBuyType(), BuyTypeEnum.AGAIN.getCode())){
            // 2.复购的规则
            itemVosSetting = goodsItemBuyTypeMap.get(BuyTypeEnum.AGAIN.getCode());
        }else{
            // 3. 不区分首单、复购
            itemVosSetting = itemVoList;
        }
        if(null != itemVosSetting && !itemVosSetting.isEmpty() && null != orderItemDataMap && !orderItemDataMap.isEmpty()){
            if(shopFullDonationRuleVo.getFullType() == ShopFullDonationFullTypeEnum.AMOUNT.getType()){
                // 算出商品明细金额之和，就判断是否满足满额条件
                BigDecimal totalAmount = itemVosSetting.stream().map(ItemVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                if(totalAmount.compareTo(shopFullDonationRuleVo.getOrderAmount()) >= 0){
                    // 满足条件，进行优惠
                    log.info("满足满减满赠活动规则，活动规则信息：{}，商品信息：{}，商品规格分组信息：{}", JSONObject.toJSONString(shopFullDonationRuleVo),
                            JSONObject.toJSONString(itemVosSetting), JSONObject.toJSONString(orderItemDataMap));
                    this.setItemFullDonationRule(itemVosSetting, orderItemDataMap, shopFullDonationRuleVo, totalAmount, null);
                }
            }else if(shopFullDonationRuleVo.getFullType() == ShopFullDonationFullTypeEnum.NUMBER.getType()){
                // 算出商品明细件数之和，就判断是否满足满件条件
                Integer totalNumber = itemVosSetting.stream().map(ItemVo::getProductQuantity).reduce(0, Integer::sum);
                if(totalNumber.compareTo(shopFullDonationRuleVo.getPieceNum()) >= 0) {
                    // 满足条件，进行优惠
                    log.info("满足满减满赠活动规则，活动规则信息：{}，商品信息：{}，商品规格分组信息：{}", JSONObject.toJSONString(shopFullDonationRuleVo),
                            JSONObject.toJSONString(itemVosSetting), JSONObject.toJSONString(orderItemDataMap));
                    this.setItemFullDonationRule(itemVosSetting, orderItemDataMap, shopFullDonationRuleVo, null, totalNumber);
                }
            }else if(shopFullDonationRuleVo.getFullType() == ShopFullDonationFullTypeEnum.NUMBER_AMOUNT.getType()){
                // 算出商品明细件数之和、金额之和，就判断是否满足满件条件
                BigDecimal totalAmount = itemVosSetting.stream().map(ItemVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                Integer totalNumber = itemVosSetting.stream().map(ItemVo::getProductQuantity).reduce(0, Integer::sum);
                if(totalAmount.compareTo(shopFullDonationRuleVo.getOrderAmount()) >= 0 &&
                        totalNumber.compareTo(shopFullDonationRuleVo.getPieceNum()) >= 0) {
                    log.info("满足满减满赠活动规则，活动规则信息：{}，商品信息：{}，商品规格分组信息：{}", JSONObject.toJSONString(shopFullDonationRuleVo),
                            JSONObject.toJSONString(itemVosSetting), JSONObject.toJSONString(orderItemDataMap));
                    // 满足条件，进行优惠
                    this.setItemFullDonationRule(itemVosSetting, orderItemDataMap, shopFullDonationRuleVo, totalAmount, null);
                }
            }

        }
    }

    /**
     * 将满足活动规则的商品明细赋值活动规则
     * @param itemVosSetting
     * @param orderItemDataMap 以规格作为分组的商品明细
     * @param shopFullDonationRuleVo 可以使用的满减满赠活动规则
     * @param totalAmount 满足满减满赠活动规则的商品明细的订单明细总金额
     * @param totalNumber 满足满减满赠活动规则的商品明细的订单明细商品总数量
     */
    private void setItemFullDonationRule(List<ItemVo> itemVosSetting, Map<Long, List<ItemVo>> orderItemDataMap, ShopFullDonationRuleVo shopFullDonationRuleVo,
                                         BigDecimal totalAmount, Integer totalNumber){
        // 满足条件，进行优惠
        itemVosSetting.parallelStream().forEach(itemVo -> {
            // 满减满赠活动id
            itemVo.setFullDonationId(shopFullDonationRuleVo.getId());
            // 满减满赠活动规则
            itemVo.setShopFullDonationRuleVo(shopFullDonationRuleVo);
            List<ItemVo> itemVos = orderItemDataMap.get(itemVo.getProductSkuId());
            if(itemVos != null && !itemVos.isEmpty()){
                itemVos.parallelStream().forEach(itemVo2 -> {
                    itemVo2.setFullDonationId(shopFullDonationRuleVo.getId());
                    itemVo2.setShopFullDonationRuleVo(shopFullDonationRuleVo);
                    // 计算满减满赠活动优惠的金额:满件且满额
                    if(null != totalAmount && null != totalNumber && null != shopFullDonationRuleVo.getReductionAmount()
                            && shopFullDonationRuleVo.getReductionAmount().compareTo(BigDecimal.ZERO) > 0){
                        BigDecimal amountRate = itemVo2.getAmount().divide(totalAmount, 2, RoundingMode.HALF_UP);
                        BigDecimal numberRate = new BigDecimal(itemVo2.getProductQuantity()).divide(new BigDecimal(totalNumber), 2, RoundingMode.HALF_UP);
                        BigDecimal rate = amountRate.add(numberRate).divide(new BigDecimal(("2")), 2, RoundingMode.HALF_UP);
                        BigDecimal promotion = shopFullDonationRuleVo.getReductionAmount().multiply(rate).setScale(2, RoundingMode.HALF_UP);
                        itemVo2.setPromotionAmount(promotion);
                    }else if(null != totalAmount && null != shopFullDonationRuleVo.getReductionAmount()
                            && shopFullDonationRuleVo.getReductionAmount().compareTo(BigDecimal.ZERO) > 0){
                        // 满额
                        BigDecimal amountRate = itemVo2.getAmount().divide(totalAmount, 2, RoundingMode.HALF_UP);
                        BigDecimal promotion = shopFullDonationRuleVo.getReductionAmount().multiply(amountRate).setScale(2, RoundingMode.HALF_UP);
                        itemVo2.setPromotionAmount(promotion);
                    }else if(null != totalNumber && null != shopFullDonationRuleVo.getReductionAmount()
                            && shopFullDonationRuleVo.getReductionAmount().compareTo(BigDecimal.ZERO) > 0){
                        // 满件
                        BigDecimal numberRate = new BigDecimal(itemVo2.getProductQuantity()).divide(new BigDecimal(totalNumber), 2, RoundingMode.HALF_UP);
                        BigDecimal promotion = shopFullDonationRuleVo.getReductionAmount().multiply(numberRate).setScale(2, RoundingMode.HALF_UP);
                        itemVo2.setPromotionAmount(promotion);
                    }
                });
            }
            // 将已经赋值过满减满赠活动的商品详情移除，以便后面不再循环，节省时间
            orderItemDataMap.remove(itemVo.getProductSkuId());
        });
    }

    /**
     * 获取运费的参数包装类，将参数包装成需要的格式
     *
     * @param miniAccountAddress
     * @param deliverType
     * @param itemDtoList
     * @return com.medusa.gruul.shipping.model.dto.CountCostDto
     * <AUTHOR>
     * @date 2020/7/26 14:32
     */
    private CountCostDto getFreightAmount(MiniAccountAddress miniAccountAddress,
                                          DeliverTypeEnum deliverType,
                                          List<ItemDto> itemDtoList) {
        //查询运费
        log.info("miniAccountAddress is {}", JSONUtil.toJsonStr(miniAccountAddress));
        log.info("deliverType is {}", deliverType.getDesc());
        CountCostDto countCostDto;
        GetCostDto getCostDto = new GetCostDto();
        getCostDto.setType(1);
        getCostDto.setRegion(miniAccountAddress.getPostCode());
        getCostDto.setItems(itemDtoList);
        log.info("getCostDto is {}", JSONUtil.toJsonStr(getCostDto));
        countCostDto = getFreightAmount(getCostDto);
        log.info("CountCostDto is {}", JSONUtil.toJsonStr(countCostDto));
        return countCostDto;
    }

    /**
     * 提前订单时候的前置检查
     *
     * @param createOrderDto
     * @return java.lang.String
     * <AUTHOR>
     * @date 2020/7/26 14:33
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String preCheckOrder(CreateOrderDto createOrderDto) {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        //查询会员持有的积分、收货地址
        AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(curUserDto.getUserId(), Arrays.asList(2,
                3, 5));
        log.info("当前用户信息:" + curUserDto.toString());
        //检查账号
        checkAccount(accountInfoDto);
        //检查缓存中商品库存
        checkStock(createOrderDto);
        //检查商品
        checkProduct(createOrderDto);
        //判断是否为积分商品
        Boolean isIntegralProduct = false;
        if(createOrderDto.getOrderType().getCode()==OrderTypeEnum.INTEGRAL.getCode()){
            isIntegralProduct = true;
        }

        if(isIntegralProduct){
            //检查缓存中的可兑换商品
            checkExchange(createOrderDto);
            //检查用户最大可兑换数
            checkUserExchange(createOrderDto,curUserDto);
        }


        List<ItemVo> itemVoList = remoteGoodsService.findItemVoByIds(createOrderDto.getItemSkuIds());

        if (CollUtil.isEmpty(itemVoList)) {
            throw new ServiceException(OrderCode.DATA_HAS_BEEN_UPDATED);
        }
        //计算订单总金额 同时购买数量赋值

        //积分商品订单不考虑检查限购
        if(!isIntegralProduct){
            checkLimit(itemVoList, curUserDto.getUserId());
        }
        ItemVo itemVo = itemVoList.get(0);



        //物流配送才需要计算运费
        if (createOrderDto.getDeliverType().equals(DeliverTypeEnum.LOGISTICS)) {
            //检查收货地址
            MiniAccountAddress accountAddress = checkAddress(accountInfoDto.getMiniAccountAddress(), createOrderDto);
            //计算运费
            BigDecimal freightAmount =
                    getFreightAmount(accountAddress, createOrderDto.getDeliverType(),
                            createOrderDto.getItemDtoList()).getCost();
            if (ObjectUtil.isNull(freightAmount) || freightAmount.equals(BigDecimal.valueOf(-1))) {
                throw new ServiceException(OrderCode.NOT_IN_THE_SCOPE_OF_DISTRIBUTION);
            }
        }
        //检查通过发送创建订单的消息
        CreateOrderMessage message = new CreateOrderMessage();
        message.setOrderVo(createOrderDto);
        //预生成的订单ID
        Long orderId = IdWorker.getId();
        message.setOrderId(orderId);
        message.setCurUser(curUserDto);
        message.setTenantId(TenantContextHolder.getTenantId());
        message.setShopId(itemVo.getShopId());
        sender.sendCreateOrderMessage(message);
        return orderId.toString();
    }

    @Override
    public String preShopsCheckOrder(CreateOrderDto createOrderDto) {
        List<WarehouseShopDto> warehouseShopDtoList = createOrderDto.getWarehouseShopDtoList();

        Integer replaceCreateOrderFlag = createOrderDto.getReplaceCreateOrderFlag();

        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();

        //下单人用户存当前登录小程序用户
        createOrderDto.setOrderUserId(curUserDto.getUserId());


        //查询会员持有的积分、收货地址
        AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(curUserDto.getUserId(), Arrays.asList(1,2,
                3,4, 5));
        log.info("当前用户信息:" + curUserDto.toString());

        if(replaceCreateOrderFlag!=null&&replaceCreateOrderFlag == 1){//代下单
            String phone = createOrderDto.getPhone();
            String smsCode = createOrderDto.getSmsCode();
            Integer type = createOrderDto.getType();
            if(type == null){
                throw new ServiceException("短信类型不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
            }
            if(StringUtils.isEmpty(phone)){
                throw new ServiceException("手机号不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
            }
            if(StringUtils.isEmpty(smsCode)){
                throw new ServiceException("手机验证码不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
            }

            Map<String, String> dataMap = remoteMiniAccountService.vailSmsCode(phone, smsCode, type);
            if(dataMap.get("code").equals("-1")){
                throw new ServiceException(dataMap.get("message"));
            }
            String userId = dataMap.get("userId");

            String shopUserId = remoteMiniAccountService.getShopUserIdByUserId(userId);
            accountInfoDto = remoteMiniAccountService.accountInfo(shopUserId, Arrays.asList(1,2,
                    3,4,5));
            //curUserDto.setUserId(accountInfoDto.getMiniAccountExtends().getShopUserId());
            if (accountInfoDto.getMiniAccountOauths().getOauthType().equals(CommonConstants.NUMBER_ONE)) {
                curUserDto.setUserType(CommonConstants.NUMBER_ZERO);
            }
            if (accountInfoDto.getMiniAccountOauths().getOauthType().equals(CommonConstants.NUMBER_TWO)) {
                curUserDto.setUserType(CommonConstants.NUMBER_TWO);
            }
            curUserDto.setNikeName(accountInfoDto.getMiniAccountunt().getNikeName());
            curUserDto.setAvatarUrl(accountInfoDto.getMiniAccountunt().getAvatarUrl());
            curUserDto.setGender(accountInfoDto.getMiniAccountunt().getGender());
            curUserDto.setUserId(accountInfoDto.getMiniAccountExtends().getShopUserId());
            curUserDto.setShopType(0);
            curUserDto.setVersion("");
            curUserDto.setOpenId(accountInfoDto.getMiniAccountOauths().getOpenId());
            curUserDto.setPhone(accountInfoDto.getMiniAccountunt().getPhone());
            curUserDto.setSelfInviteCode(accountInfoDto.getMiniAccountunt().getSelfInviteCode());
        }

        Integer mallOrderType = createOrderDto.getMallOrderType();


        //会员类型id-升级订单
        Long upgradeMemberTypeId = createOrderDto.getUpgradeMemberTypeId();
        //会员等级id-升级订单
        Long upgradeMemberLevelId = createOrderDto.getUpgradeMemberLevelId();

        //检查账号
        checkAccount(accountInfoDto);


        String avatarUrl = accountInfoDto.getMiniAccountunt().getAvatarUrl();
        curUserDto.setAvatarUrl(avatarUrl);
        //List<ItemShopDto> itemShopDtoList = new ArrayList<>();
        List<ItemDto> itemDtoList = createOrderDto.getItemDtoList();

        for (ItemDto itemDto : itemDtoList) {
            Long skuId = itemDto.getSkuId();
            //会员类型id
            Long memberTypeId = itemDto.getMemberTypeId();
            //若商品传入的会员类型为空，则将默认会员等级所属的会员类型作为传入的会员类型
            if(memberTypeId == null){
                //获取默认会员等级
                MemberLevel memberLevel = remoteMiniAccountService.getDefaultMemberLevel();
                if(memberLevel == null){
                    throw new ServiceException("默认会员等级为空！");
                }
                memberTypeId = memberLevel.getMemberTypeId();
            }
            itemDto.setMemberTypeId(memberTypeId);
            String memberLevelId = null;
            //升级订单
            if(createOrderDto.getMallOrderType()!=null&&createOrderDto.getMallOrderType() == ProductTypeEnum.UPGRADE_PRODUCT.getStatus()){
                memberLevelId = "";
                //根据升级升级会员类型id，升级会员等级id验证是否满足最低升级
                Boolean upgradeFlag = remoteMiniAccountService.checkUpgradePreLow(upgradeMemberTypeId,upgradeMemberLevelId,curUserDto.getUserId());
                if(!upgradeFlag){
                    throw new ServiceException("当前用户未满足升级等级需要前置最低会员等级！");
                }
            }else{
                 memberLevelId = remoteMiniAccountService.getMemberLevelId(accountInfoDto.getMiniAccountunt().getUserId(), memberTypeId);
                if(StringUtils.isEmpty(memberLevelId)){
                    throw new ServiceException("您没有权限购买，请先升级！");
                }
            }
            itemDto.setMemberLevelId(memberLevelId);
            SkuStock skuStock = remoteGoodsService.findSkuStockById(skuId);
            itemDto.setShopId(skuStock.getShopId());
            Long productId = skuStock.getProductId();
            ProductVo product = remoteGoodsService.findProductById(productId);
            itemDto.setProductType(product.getProductType());

        }
        // 按照店铺、产品类型分组创建订单：如果先按照店铺分组，再生成订单，则跨店铺使用的优惠券、满减满赠活动无法判断能否使用以及各个订单明细分解的优惠金额也不好处理，
        // 改为在生成订单的时候再分组
        /*Map<String, Map<Integer, List<ItemDto>>> listMap2 = itemDtoList.stream()
                .collect(Collectors.groupingBy(
                        ItemDto::getShopId,
                        Collectors.groupingBy(ItemDto::getProductType)
                ));
        for (Map.Entry<String, Map<Integer, List<ItemDto>>> entry : listMap2.entrySet()) {
            String shopId = entry.getKey();
            Map<Integer, List<ItemDto>> listMap3 = entry.getValue();
            for (Map.Entry<Integer, List<ItemDto>> entry2 : listMap3.entrySet()) {
                ItemShopDto itemShopDto = new ItemShopDto();
                Integer productType = entry2.getKey();
                List<ItemDto> list = entry2.getValue();
                itemShopDto.setShopId(shopId);
                itemShopDto.setItemDtos(list);
                itemShopDto.setProductType(productType);
                itemShopDtoList.add(itemShopDto);
            }
        }*/

        String orderIds= "";

        int i = 0;

        //for (ItemShopDto itemShopDto : itemShopDtoList) {

            //List<ItemDto> itemDtos = itemShopDto.getItemDtos();
            //createOrderDto.setItemDtoList(itemDtos);
            //检查缓存中商品库存
            checkStock(createOrderDto);
            checkProduct(createOrderDto);
            //上述两项检查是否合在一起？
            //判断是否为积分商品
            Boolean isIntegralProduct = false;

            /*if(createOrderDto.getOrderType().getCode()==OrderTypeEnum.MALL.getCode()&&createOrderDto.getMallOrderType() == null){
                Integer productType = itemShopDto.getProductType();
                createOrderDto.setMallOrderType(productType);
            }*/

            if(createOrderDto.getOrderType().getCode()==OrderTypeEnum.INTEGRAL.getCode()){
                isIntegralProduct = true;
            }

            if(isIntegralProduct){
                //检查缓存中的可兑换商品
                checkExchange(createOrderDto);
                //检查用户最大可兑换数
                checkUserExchange(createOrderDto,curUserDto);
            }
            List<ItemDto> itemList = createOrderDto.getItemDtoList();
            List<Map<String,String>>dataList = new ArrayList<>();
            for (ItemDto itemDto : itemList) {
                Map<String,String>dataMap = new HashMap<>();
                dataMap.put("skuId",itemDto.getSkuId()+"");
                dataMap.put("memberTypeId",itemDto.getMemberTypeId()+"");
                dataMap.put("productQuantity",itemDto.getNumber()+"");
                dataList.add(dataMap);
            }

            String jsonString = JSON.toJSONString(dataList);
            List<ItemVo> itemVoList = remoteGoodsService.findItemVoByItemVo(jsonString);
            if (CollUtil.isEmpty(itemVoList)) {
                throw new ServiceException(OrderCode.DATA_HAS_BEEN_UPDATED);
            }

            //判断用户是否为冻结状态
            MiniAccountExtends miniAccountExtends = accountInfoDto.getMiniAccountExtends();
            if(Objects.equals(miniAccountExtends.getStatus(), MiniAccountExtendsStatusEnum.NO.getStatus())){
                //查询激活会员商品表是否有记录
                Integer memberActiveProductCount = remoteMiniAccountService.getMemberActiveProductCount();
                if(memberActiveProductCount>0){
                    if(mallOrderType ==null || mallOrderType != ProductTypeEnum.ACTIVE_PRODUCT.getStatus()){
                        throw new ServiceException("您的账户已被冻结，请先购买指定商品激活");
                    }else{
                        createOrderDto.setMallOrderType(mallOrderType);
                    }
                }else{
                    createOrderDto.setMallOrderType(ProductTypeEnum.ACTIVE_PRODUCT.getStatus());
                }
            }

            //判断是否为升级订单
            if(mallOrderType!=null&&mallOrderType == ProductTypeEnum.UPGRADE_PRODUCT.getStatus()){

                if(upgradeMemberTypeId == null){
                    throw new ServiceException("订单提交参数异常【会员类型为空】，请重新提交");
                }
                MemberType memberType= remoteMiniAccountService.getMemberTypeById(upgradeMemberTypeId);
                if(memberType == null){
                    throw new ServiceException("订单提交参数异常【会员升级类型不存在】，请重新提交");
                }
                if(memberType.getStatus() == MemberTypeStatusEnum.NO.getStatus()){
                    throw new ServiceException("该会员升级已停用，无法升级");
                }


                MemberLevelRuleMessage memberLevelRuleMessage = remoteMiniAccountService.getMemberLevelRuleMessageByMemberTypeId(upgradeMemberTypeId);
                if(memberLevelRuleMessage == null){
                    log.error("会员升级类型对应会员规则信息不存在");
                    throw new ServiceException("订单提交参数异常【升级会员等级异常】，请重新提交");
                }
                String type = memberLevelRuleMessage.getType();
                if(StringUtils.isEmpty(type)){
                    log.error("会员升级类型对应会员规则信息升级方式不存在");
                    throw new ServiceException("订单提交参数异常【升级会员等级异常】，请重新提交");
                }

                if(upgradeMemberLevelId != null){

                    if(!memberLevelRuleMessage.getType().contains(MemberLevelRuleTypeEnum.APPOINT_GOODS.getStatus())){
                        throw new ServiceException("订单提交参数异常【升级规则异常】，请重新提交");
                    }
                    List<MemberLevelRuleProduct>memberLevelRuleProductList =
                            remoteMiniAccountService.getMemberLevelRuleProductByMainId(memberLevelRuleMessage.getId());

                     List<MemberLevelRule>memberLevelRuleList =
                            remoteMiniAccountService.getMemberLevelRuleByMainId(memberLevelRuleMessage.getId());
                     MemberLevelRule upgradeMemberLevelRule = null;
                     if(memberLevelRuleList!=null&& !memberLevelRuleList.isEmpty()){
                         for (MemberLevelRule memberLevelRule : memberLevelRuleList) {
                             if(Long.valueOf(memberLevelRule.getMemberLevelId()).equals(upgradeMemberLevelId)){
                                 upgradeMemberLevelRule = memberLevelRule;
                             }
                         }
                     }else{
                         log.error("会员升级类型对应会员规则记录不存在");
                         throw new ServiceException("订单提交参数异常【升级商品异常】，请重新提交");
                     }
                     if(upgradeMemberLevelRule == null){
                         log.error("会员升级等级对应的规则记录不存在！");
                         throw new ServiceException("订单提交参数异常【升级商品异常】，请重新提交");
                     }
                    if(memberLevelRuleProductList!=null&& !memberLevelRuleProductList.isEmpty()){
                        boolean b = false;
                        for (ItemVo itemVo : itemVoList) {
                            for (MemberLevelRuleProduct memberLevelRuleProduct : memberLevelRuleProductList) {
                                if(itemVo.getProductSkuId().equals(Long.valueOf(memberLevelRuleProduct.getSkuId()))){
                                    b = true;
                                    itemVo.setUpgradeProductQuantity(upgradeMemberLevelRule.getProductQty());
                                    itemVo.setUpgradeProductPrice(upgradeMemberLevelRule.getProductAmount().divide(new BigDecimal(upgradeMemberLevelRule.getProductQty()),2, RoundingMode.HALF_UP));
                                    //设置升级订单金额
                                    itemVo.setAmount(upgradeMemberLevelRule.getProductAmount());
                                }
                            }
                        }
                        if(!b){
                            throw new ServiceException("订单提交参数异常【升级商品异常】，请重新提交");
                        }
                    }else{
                        log.error("会员升级类型对应会员规则商品不存在");
                        throw new ServiceException("订单提交参数异常【升级商品异常】，请重新提交");
                    }
                }else{
                    if(memberLevelRuleMessage.getType().contains(MemberLevelRuleTypeEnum.APPOINT_GOODS.getStatus())){
                        throw new ServiceException("订单提交参数异常【升级会员等级异常】，请重新提交");
                    }
                    List<MemberLevelRuleProduct>memberLevelRuleProductList =
                            remoteMiniAccountService.getMemberLevelRuleProductByMainId(memberLevelRuleMessage.getId());
                    if(memberLevelRuleProductList!=null&& !memberLevelRuleProductList.isEmpty()){
                        boolean b = false;
                        for (ItemVo itemVo : itemVoList) {
                            for (MemberLevelRuleProduct memberLevelRuleProduct : memberLevelRuleProductList) {
                                if(itemVo.getProductSkuId().equals(Long.valueOf(memberLevelRuleProduct.getSkuId()))){
                                    b = true;
                                    break;
                                }
                            }
                        }
                        if(!b){
                            throw new ServiceException("订单提交参数异常【升级商品异常】，请重新提交");
                        }
                    }else{
                        log.error("会员升级类型对应会员规则商品不存在");
                        throw new ServiceException("订单提交参数异常【升级商品异常】，请重新提交");
                    }
                }
            }

            AccountInfoDto finalAccountInfoDto = accountInfoDto;
            final BigDecimal[] totalAmount = {BigDecimal.ZERO};
            List<MiniAccountCouponOrderItemDto> miniAccountCouponOrderItemList = new ArrayList<>();
            itemVoList.forEach(itemVo -> {
                // 默认首单
                itemVo.setBuyType(BuyTypeEnum.ONE.getCode());
                //判断是否为升级订单
                if(mallOrderType!=null&&mallOrderType == ProductTypeEnum.UPGRADE_PRODUCT.getStatus()){
                    //升级价格
                    if(itemVo.getUpgradeProductPrice()!=null&&itemVo.getUpgradeProductQuantity()!=null){
                        itemVo.setProductPrice(itemVo.getUpgradeProductPrice());
                        itemVo.setProductQuantity(itemVo.getUpgradeProductQuantity());
                    }
                }else{
                    //会员类型id
                    Long memberTypeId = itemVo.getMemberTypeId();
                    //若商品传入的会员类型为空，则将默认会员等级所属的会员类型作为传入的会员类型
                    if(memberTypeId == null){
                        //获取默认会员等级
                        MemberLevel memberLevel = remoteMiniAccountService.getDefaultMemberLevel();
                        if(memberLevel == null){
                            throw new ServiceException("默认会员等级为空！");
                        }
                        memberTypeId = memberLevel.getMemberTypeId();
                    }
                    String memberLevelId = remoteMiniAccountService.getMemberLevelId(finalAccountInfoDto.getMiniAccountunt().getUserId(), memberTypeId);
                    //根据会员的id来获取对应的全部会员商品价格
                    List<MemberLevelGoodsPrice> memberLevelGoodsPriceList = remoteGoodsService.selectMemberGoodsPrice(memberLevelId);
                    BigDecimal productPrice = itemVo.getProductPrice();
                    //会员价
                    if(CollUtil.isNotEmpty(memberLevelGoodsPriceList)){
                        //获取商品规格会员价
                        List<MemberLevelGoodsPrice> memberLevelGoodsPrices = memberLevelGoodsPriceList.stream().filter(memberLevelGoodsPrice->  memberLevelGoodsPrice.getSkuId().equals(itemVo.getProductSkuId())).collect(Collectors.toList());
                        //将商品的价格替换成会员价
                        if(CollectionUtil.isNotEmpty(memberLevelGoodsPrices)){
                            if (memberLevelGoodsPrices.get(0).getMemberLevelPrice()!=null){
                                if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==itemVo.getMemberPriceType()){
                                    itemVo.setProductPrice(memberLevelGoodsPrices.get(0).getMemberLevelPrice());
                                }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==itemVo.getMemberPriceType()){
                                    itemVo.setProductPrice(productPrice.multiply(memberLevelGoodsPrices.get(0).getMemberLevelPrice()).divide(new BigDecimal(100),4, BigDecimal.ROUND_HALF_UP));
                                }
                            }
                        }
                    }
                    //复购价
                    Integer count = orderItemMapper.getCountByMemberType(curUserDto.getUserId(),itemVo.getMemberTypeId(), OrderStatusEnum.getPaidStatus());
                    if(count > 0 ){
                        // 复购单
                        itemVo.setBuyType(BuyTypeEnum.AGAIN.getCode());
                        List<MemberLevelGoodsAgainPrice> memberLevelGoodsAgainPriceList = remoteGoodsService.selectMemberGoodsAgainPrice(memberLevelId);
                        if(CollUtil.isNotEmpty(memberLevelGoodsAgainPriceList)){
                            List<MemberLevelGoodsAgainPrice> memberLevelGoodsAgainPrices = memberLevelGoodsAgainPriceList.stream().filter(memberLevelGoodsAgainPrice->  memberLevelGoodsAgainPrice.getSkuId().equals(itemVo.getProductSkuId())).collect(Collectors.toList());
                            //将商品的价格替换成复购价
                            if(CollectionUtil.isNotEmpty(memberLevelGoodsAgainPrices)){
                                if (memberLevelGoodsAgainPrices.get(0).getMemberLevelAgainPrice()!=null){
                                    if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==itemVo.getMemberAgainPriceType()){
                                        itemVo.setProductPrice(memberLevelGoodsAgainPrices.get(0).getMemberLevelAgainPrice());
                                    }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==itemVo.getMemberAgainPriceType()){
                                        itemVo.setProductPrice(productPrice.multiply(memberLevelGoodsAgainPrices.get(0).getMemberLevelAgainPrice()).divide(new BigDecimal(100),4, BigDecimal.ROUND_HALF_UP));
                                    }
                                }
                            }
                        }
                    }
                    itemVo.setAmount(itemVo.getProductPrice().multiply(new BigDecimal(itemVo.getProductQuantity())));
                }
                totalAmount[0] = totalAmount[0].add(itemVo.getAmount());
                MiniAccountCouponOrderItemDto miniAccountCouponOrderItemDto = new MiniAccountCouponOrderItemDto();
                miniAccountCouponOrderItemDto.setNumber(new BigDecimal(itemVo.getProductQuantity()));
                miniAccountCouponOrderItemDto.setMemberTypeId(itemVo.getMemberTypeId());
                miniAccountCouponOrderItemDto.setSkuId(itemVo.getProductSkuId() + "");
                miniAccountCouponOrderItemDto.setProductPrice(itemVo.getProductPrice());
                miniAccountCouponOrderItemDto.setShopId(itemVo.getShopId());
                miniAccountCouponOrderItemDto.setAmount(itemVo.getAmount());
                miniAccountCouponOrderItemList.add(miniAccountCouponOrderItemDto);
            });
            //积分商品订单不考虑检查限购
            if(!isIntegralProduct){
                checkLimit(itemVoList, curUserDto.getUserId());
            }
            //计算订单总金额 同时购买数量赋值
            //BigDecimal totalAmount = getTotalAmount(itemVoList, createOrderDto.getItemDtoList());

            //根据订单总额计算每个订单的优惠金额
            // 改为：1.先校验优惠券是否可用，2.优惠券可用，则将优惠金额分解到每条明细中
            BigDecimal couponAmount;
            Map<String, ItemVo> itemVoMap = itemVoList.stream().collect(Collectors.toMap(t->t.getProductSkuId()+":"+t.getMemberTypeId(),ItemVo->ItemVo));
            Long accountCouponId = createOrderDto.getAccountCouponId();
            //BigDecimal couponPrice = BigDecimal.ZERO;
            if(accountCouponId!=null){
                // shop_user_id
                //String userId = curUserDto.getUserId();
                //查看用户可用优惠券：只查最大满额的一张或者用户手动选择的优惠券
                MiniAccountCouponByOrderDto miniAccountCouponByOrderDto = new MiniAccountCouponByOrderDto();
                miniAccountCouponByOrderDto.setUserId(accountInfoDto.getMiniAccountunt().getUserId());
                miniAccountCouponByOrderDto.setReplaceCreateOrderFlag(replaceCreateOrderFlag);
                miniAccountCouponByOrderDto.setData(miniAccountCouponOrderItemList);
                miniAccountCouponByOrderDto.setMiniAccountCouponId(accountCouponId);
                miniAccountCouponByOrderDto.setAllFlag(false);

                List<MiniAccountCouponByOrderVo> miniAccountCouponByOrderList = remoteMiniAccountService.getCouponByUser(miniAccountCouponByOrderDto);
                //将返回的优惠券数据转换且分解到订单商品明细的优惠金额字段

                //符合使用优惠券的订单明细总金额
                if(miniAccountCouponByOrderList!=null && !miniAccountCouponByOrderList.isEmpty()) {
                    miniAccountCouponByOrderList = miniAccountCouponByOrderList.stream().filter(e -> e.getId().equals(accountCouponId)).collect(Collectors.toList());
                    MiniAccountCouponByOrderVo miniAccountCouponByOrderVo = miniAccountCouponByOrderList.get(0);

                    //优惠金额--目前只有满减额
                    couponAmount = miniAccountCouponByOrderVo.getPromotion();
                    // 对得到优惠券条件的商品的金额进行求和
                    BigDecimal couponTotalAmount = miniAccountCouponByOrderDto.getData().stream().filter(e -> e.getMiniAccountCouponId() != null).map(MiniAccountCouponOrderItemDto::getAmount)
                            .filter(Objects::nonNull) // 过滤 null 值（可选）
                            .reduce(BigDecimal.ZERO, BigDecimal::add); // 初始值为 0，逐个相加

                    miniAccountCouponByOrderDto.getData().forEach(e -> {
                        if (e.getMiniAccountCouponId() != null) {
                            //计算优惠券分解金额
                            BigDecimal itemPromotionAmount = e.getAmount().divide(couponTotalAmount, 2, RoundingMode.HALF_UP).multiply(couponAmount);
                            String key = e.getSkuId() + ":" + e.getMemberTypeId();
                            if (null != itemVoMap.get(key)) {
                                ItemVo itemVo = itemVoMap.get(key);
                                itemVo.setYouhuiPrice(itemPromotionAmount);
                            }
                        }
                    });
                }else{
                    throw new ServiceException("优惠券不可使用：订单商品可优惠总价小于满减金额或者优惠券已无法使用");
                }

                totalAmount[0] = totalAmount[0].subtract(couponAmount);
            }else {
                couponAmount = BigDecimal.ZERO;
            }
            createOrderDto.setCouponPrice(couponAmount);

            //优惠券计算完成，计算满减满赠
            // 计算满减满赠逻辑
            this.checkFullDonation(itemVoList, accountInfoDto);
            // 已被处理过的满减满赠活动id
            List<Long> fullDonationIdList = new ArrayList<>();
            // 满减满赠活动标识
            AtomicBoolean fullDonationFlag = new AtomicBoolean(false);
            // 活动满减总额
            AtomicReference<BigDecimal> fullDonationAmount = new AtomicReference<>(BigDecimal.ZERO);
            // 活动赠送的优惠券
            List<ShopCoupon> giftCoupons = new ArrayList<>();
            // 活动赠送的商品
            List<ItemVo> giftGoodsItemVoList = new ArrayList<>();
            itemVoList.forEach(itemVo -> {
                if(itemVo.getShopFullDonationRuleVo() != null){
                    fullDonationFlag.set(true);
                    if(!fullDonationIdList.contains(itemVo.getShopFullDonationRuleVo().getId())){
                        // 计算满减总额
                        if(itemVo.getShopFullDonationRuleVo().getReductionAmount() != null && itemVo.getShopFullDonationRuleVo().getReductionAmount().compareTo(BigDecimal.ZERO) > 0){
                            fullDonationAmount.set(fullDonationAmount.get().add(itemVo.getShopFullDonationRuleVo().getReductionAmount()));
                        }
                        // 计算赠送优惠券
                        if(StrUtil.isNotBlank(itemVo.getShopFullDonationRuleVo().getCouponIds())){
                            // 根据优惠券id查询优惠券
                            // 将逗号分隔的id组装成list，且将string转换成Long类型
                            List<String> couponIdStrList = Arrays.asList(itemVo.getShopFullDonationRuleVo().getCouponIds().split(","));
                            List<Long> couponIdList = couponIdStrList.stream().map(Long::valueOf).collect(Collectors.toList());
                            List<ShopCoupon> shopCouponList = this.remoteShopsService.getCouponByIds(couponIdList);
                            giftCoupons.addAll(shopCouponList);
                        }
                        // 计算赠送的商品
                        if(StrUtil.isNotBlank(itemVo.getShopFullDonationRuleVo().getSkuIds())){
                            // 将逗号分隔的id组装成list，且将string转换成Long类型
                            List<String> skuIdStrList = Arrays.asList(itemVo.getShopFullDonationRuleVo().getSkuIds().split(","));
                            List<Long> skuIdList = skuIdStrList.stream().map(Long::valueOf).collect(Collectors.toList());
                            List<ItemVo> itemVoByIds = this.remoteGoodsService.findItemVoByIds(skuIdList);
                            // 设置itemVoByIds 产品数量为1
                            itemVoByIds.forEach(itemVo2 -> {
                                itemVo2.setProductQuantity(1);
                                itemVo2.setProductPrice(BigDecimal.ZERO);
                                itemVo2.setMemberTypeId(itemVo.getMemberTypeId());
                                itemVo2.setGiftFlag(GiftFlagEnum.YES.getCode());
                                itemVo2.setAmount(itemVo2.getProductPrice().multiply(new BigDecimal(itemVo2.getProductQuantity())));
                            });
                            giftGoodsItemVoList.addAll(itemVoByIds);
                        }
                        fullDonationIdList.add(itemVo.getShopFullDonationRuleVo().getId());
                    }
                    // 计算赠送自身件数
                    if(itemVo.getShopFullDonationRuleVo().getSelfNum() != null && itemVo.getShopFullDonationRuleVo().getSelfNum() > 0){
                        ItemVo giftItemVo = new ItemVo();
                        BeanUtil.copyProperties(itemVo, giftItemVo);
                        giftItemVo.setShopFullDonationRuleVo(null);
                        giftItemVo.setFullDonationId(null);
                        giftItemVo.setProductQuantity(itemVo.getProductQuantity() * itemVo.getShopFullDonationRuleVo().getSelfNum());
                        giftItemVo.setProductPrice(BigDecimal.ZERO);
                        giftItemVo.setGiftFlag(GiftFlagEnum.YES.getCode());
                        giftItemVo.setAmount(giftItemVo.getProductPrice().multiply(new BigDecimal(giftItemVo.getProductQuantity())));
                        giftGoodsItemVoList.add(giftItemVo);
                    }

                }
            });
            //促销总金额
            BigDecimal promotionAmount = fullDonationAmount.get();
            // 总金额减去促销金额
            totalAmount[0] = totalAmount[0].subtract(promotionAmount);

            //物流配送才需要计算运费，需要加上满减满赠商品去计算运费，放到创建订单队列的时候要把赠品重新删掉，因为创建订单方法里会重新计算赠品
            if(!giftGoodsItemVoList.isEmpty()){
                giftGoodsItemVoList.forEach(e -> {
                    ItemDto itemDto = new ItemDto();
                    BeanUtil.copyProperties(e, itemDto);
                    itemDto.setSkuId(e.getProductSkuId());
                    itemDto.setNumber(e.getProductQuantity());
                    createOrderDto.getItemDtoList().add(itemDto);
                });
            }

            if (createOrderDto.getDeliverType().equals(DeliverTypeEnum.LOGISTICS)) {
                //检查收货地址
                MiniAccountAddress accountAddress = checkAddress(accountInfoDto.getMiniAccountAddress(), createOrderDto);
                //计算运费
                BigDecimal freightAmount =
                        getFreightAmount(accountAddress, createOrderDto.getDeliverType(),
                                createOrderDto.getItemDtoList()).getCost();
                if (ObjectUtil.isNull(freightAmount) || freightAmount.equals(BigDecimal.valueOf(-1))) {
                    throw new ServiceException(OrderCode.NOT_IN_THE_SCOPE_OF_DISTRIBUTION);
                }
                totalAmount[0] = totalAmount[0].add(freightAmount);
            }

            if(createOrderDto.getPayCommission() == null){
                createOrderDto.setPayCommission(BigDecimal.ZERO);
            }
            if(createOrderDto.getPayGolden() == null){
                createOrderDto.setPayGolden(BigDecimal.ZERO);
            }
            //判断支付密码
            BigDecimal payCommission = createOrderDto.getPayCommission();
            BigDecimal payGolden = createOrderDto.getPayGolden();
            PayTypeEnum payType = createOrderDto.getPayType();
            //使用佣金，金豆时候需要验证支付密码
            if(payCommission.compareTo(BigDecimal.ZERO)>0
                    || payGolden.compareTo(BigDecimal.ZERO)>0){

                if(StringUtils.isEmpty(miniAccountExtends.getPayPwd())){
                    throw new ServiceException("当前用户密码为空，请先设置用户密码！");
                }

                String payPwd = createOrderDto.getPayPwd();
                if(StringUtils.isEmpty(payPwd)){
                    throw new ServiceException("支付密码不能为空！");
                }

                //验证密码
                String md5Pw = SecureUtil.md5(payPwd.concat(miniAccountExtends.getPaySalt()));
                if (!md5Pw.equals(miniAccountExtends.getPayPwd())) {
                    throw new ServiceException("账号或密码错误！");
                }
            }
            //验证金豆支付+佣金支付金额不能大于订单应付金额
            if(payCommission.add(payGolden).compareTo(totalAmount[0])>0){
                throw new ServiceException("实际支付金额大于订单应付金额，请重新提交！");
            }
            //如果金豆支付金额A加佣金支付金额B之和小于订单应付金额，前端传入的支付方式必须有值
            if(payCommission.add(payGolden).compareTo(totalAmount[0])<0){
                if(payType == null){
                    throw new ServiceException("请选择支付方式！");
                }
            }else{
                if(payCommission.compareTo(BigDecimal.ZERO)>0){
                    createOrderDto.setPayType(PayTypeEnum.COMMISSION);
                }
                if(payCommission.compareTo(BigDecimal.ZERO)>0){
                    createOrderDto.setPayType(PayTypeEnum.COMMISSION);
                }
                if(payGolden.compareTo(BigDecimal.ZERO)>0){
                    createOrderDto.setPayType(PayTypeEnum.GOLDEN_BEAN);
                }
                if(payGolden.compareTo(BigDecimal.ZERO)>0&&payCommission.compareTo(BigDecimal.ZERO)>0){
                    createOrderDto.setPayType(PayTypeEnum.GOLDEN_BEAN_AND_COMMISSION);
                }
            }

            //检查通过发送创建订单的消息
            //预生成的订单ID
            Long orderId = IdWorker.getId();
            if(orderIds.length()!=0){
                orderIds+=",";
            }
            orderIds+=orderId;
            CreateOrderMessage message = new CreateOrderMessage();
            /*if(warehouseShopDtoList!=null && !warehouseShopDtoList.isEmpty()){
                for (WarehouseShopDto warehouseShopDto : warehouseShopDtoList) {
                    if(StringUtil.isNotEmpty(warehouseShopDto.getShopId())&&StringUtil.isNotEmpty(itemShopDto.getShopId())&&warehouseShopDto.getShopId().equals(itemShopDto.getShopId())){
                        createOrderDto.setWarehouseId(warehouseShopDto.getWarehouseId());
                    }
                }
            }*/
            // 将赠品标识为1的数据清除
        List<ItemDto> mqItemDtoList = createOrderDto.getItemDtoList().stream().filter(e -> !Objects.equals(e.getGiftFlag(), GiftFlagEnum.YES.getCode())).collect(Collectors.toList());
        createOrderDto.setItemDtoList(mqItemDtoList);
        message.setOrderVo(createOrderDto);
            message.setOrderId(orderId);
            //订单用户
            message.setCurUser(curUserDto);
            message.setTenantId(TenantContextHolder.getTenantId());
            //message.setShopId(itemShopDto.getShopId());
            sender.sendCreateOrderMessage(message);
        //}
        return orderIds;
    }


    /**
     * 检查是否符合限购要求
     *
     * @param itemVoList
     * @param userId
     * @return void
     * <AUTHOR>
     * @date 2020/7/26 14:35
     */
    private void checkLimit(List<ItemVo> itemVoList, String userId) {
        for (ItemVo itemVo : itemVoList) {
            if (ObjectUtil.isNotNull(itemVo.getPerLimit()) && itemVo.getPerLimit() != 0) {
                if (itemVo.getLimitType() != 1) {
                    Integer total = orderItemMapper.countSkuPurchased(itemVo.getProductSkuId(), userId);

                    total = ObjectUtil.isNull(total) ? 0 : total;
                    if (total > 0) {
                        Integer returnNum = afsOrderMapper.countSkuReturn(itemVo.getProductSkuId(), userId);
                        returnNum = ObjectUtil.isNull(returnNum) ? 0 : returnNum;
                        total = total - returnNum;
                    }

                    if (total + itemVo.getProductQuantity() > itemVo.getPerLimit()) {
                        throw new ServiceException(StrUtil.format("商品{}已经超过限购数量", itemVo.getProductName()));
                    }
                } else {
                    List<ItemVo> productItemList =
                            itemVoList.stream().filter(vo -> vo.getProductId().equals(itemVo.getProductId())).collect(Collectors.toList());
                    Integer total = 0;
                    for (ItemVo vo : productItemList) {
                        Integer skuPurchase = orderItemMapper.countSkuPurchased(vo.getProductSkuId(), userId);
                        skuPurchase = ObjectUtil.isNull(skuPurchase) ? 0 : skuPurchase;
                        if (skuPurchase > 0) {
                            Integer returnNum = afsOrderMapper.countSkuReturn(itemVo.getProductSkuId(), userId);
                            returnNum = ObjectUtil.isNull(returnNum) ? 0 : returnNum;
                            skuPurchase = skuPurchase - returnNum;
                        }
                        total = total + skuPurchase;
                    }
                    if (total + itemVo.getProductQuantity() > itemVo.getPerLimit()) {
                        throw new ServiceException(StrUtil.format("商品{}已经超过限购数量", itemVo.getProductName()));
                    }
                }

            }
        }
    }

    /**
     * 检查账户是否在黑名单中
     *
     * @param accountInfoDto
     * @return void
     * <AUTHOR>
     * @date 2020/7/26 14:35
     */
    private void checkAccount(AccountInfoDto accountInfoDto) {
        if (ObjectUtil.isNull(accountInfoDto)) {
            throw new ServiceException(SystemCode.DATA_NOT_EXIST);
        }
        if (CollUtil.isNotEmpty(accountInfoDto.getRestrictTypes()) && accountInfoDto.getRestrictTypes().contains(BlacklistEnum.REJECT_ORDER.getType())) {
            throw new ServiceException(OrderCode.ACCOUNT_NUMBER_EXCEPTION);
        }
    }


    private void checkUserExchange(CreateOrderDto createOrderDto,CurUserDto curUserDto){
        List<ItemDto> itemDtoList = createOrderDto.getItemDtoList();
        for (ItemDto itemDto : itemDtoList) {
            IntegralProduct integralProduct = remoteGoodsService.getIntegralProduct(itemDto.getIntegralProductId());
            String skuId = integralProduct.getSkuId();
            Long productId = integralProduct.getProductId();
            BigDecimal userAllExchangeNum = integralProduct.getUserExchangeNum();
            BigDecimal userExchangeNum = this.baseMapper.getUserExchangeNumByUserId(curUserDto.getUserId(),skuId,productId);
            if(userExchangeNum!=null&&(userExchangeNum.add(new BigDecimal(itemDto.getNumber()))).compareTo(userAllExchangeNum)>0){
                throw new ServiceException("兑换数量已经超过用户限兑数量！");
            }
        }
    }


    /**
     * 检查积分可兑换数
     * @param createOrderDto
     */
    private void checkExchange(CreateOrderDto createOrderDto){

        IntegralGoodsExchangeNumRedisKey exchangeNumRedisKey = new IntegralGoodsExchangeNumRedisKey();
        List<ItemDto> itemDtoList = createOrderDto.getItemDtoList();
        for (ItemDto itemDto : itemDtoList) {
            /**
             * 可兑换数量
             */
            BigDecimal exchangeNum = new BigDecimal(exchangeNumRedisKey.get(itemDto.getIntegralProductId()));
            if (exchangeNum.compareTo(new BigDecimal(itemDto.getNumber())) < 0) {
                //查看数据库 看积分商品是否不能再兑换
                IntegralProduct integralProduct = remoteGoodsService.getIntegralProduct(itemDto.getIntegralProductId());
                if (ObjectUtil.isNotNull(integralProduct)) {
                    BigDecimal alreadyExchangeNum = integralProduct.getAlreadyExchangeNum();
                    BigDecimal allExchangeNum = integralProduct.getAllExchangeNum();
                    BigDecimal canExchangeNum = allExchangeNum.subtract(alreadyExchangeNum);
                    exchangeNum = canExchangeNum;
                    if (exchangeNum.compareTo(new BigDecimal(itemDto.getNumber())) < 0) {
                        throw new ServiceException(SystemCode.ITEM_EXCHANGE_OUT);
                    }
                }
                exchangeNum = exchangeNum.subtract(new BigDecimal(itemDto.getNumber()));
                exchangeNumRedisKey.set(itemDto.getSkuId().toString(), exchangeNum.toString());
            }
        }

    }


    /**
     * 检查商品库存
     *
     * @param createOrderDto
     * @return void
     * <AUTHOR>
     * @date 2020/7/26 14:35
     */
    private void checkStock(CreateOrderDto createOrderDto) {
        GoodsSkuStockRedisKey redisStock = new GoodsSkuStockRedisKey();
        List<ItemDto> itemDtoList = createOrderDto.getItemDtoList();
        OrderSetting orderSetting = orderSettingMapper.selectOne(null);
        for (ItemDto itemDto : itemDtoList) {
            //redis预减库存
            BigDecimal stock = new BigDecimal(redisStock.get(itemDto.getSkuId().toString()));

            SkuStock skuStock = remoteGoodsService.findSkuStockById(itemDto.getSkuId());
            //多规格实际出库
            Integer sourceStock = itemDto.getNumber();
            if (ObjectUtil.isNotNull(skuStock) &&  skuStock.getReallyOutStock()>1){
                itemDto.setNumber(skuStock.getReallyOutStockNum(itemDto.getNumber()));
            }
            if (stock.compareTo(new BigDecimal(itemDto.getNumber())) < 0) {
                //读取数据库，刷新缓存，查看商品是否确实售罄

                if (ObjectUtil.isNotNull(skuStock)) {
                    stock = skuStock.getStock();
                }
                //这里增加判断，如果订单设置里设置了允许负库存下单，则这里不再抛出异常
                if (!orderSetting.getOpenNegativeOrder() && stock.compareTo(new BigDecimal(itemDto.getNumber())) < 0) {
                    throw new ServiceException(SystemCode.ITEM_SOLD_OUT);
                }
            }
            if (ObjectUtil.isNotNull(skuStock) &&  skuStock.getReallyOutStock()>1){
                //恢复数量
                itemDto.setNumber(sourceStock);
            }
            stock = stock.subtract(new BigDecimal(itemDto.getNumber()));
            redisStock.set(itemDto.getSkuId().toString(), stock.toString());
        }
    }

    /**
     * 检查商品的情况
     *
     * @param createOrderDto
     */
    private void checkProduct(CreateOrderDto createOrderDto) {
        List<ItemDto> itemDtoList = createOrderDto.getItemDtoList();
        for (ItemDto itemDto : itemDtoList) {
            ProductVo productVo = remoteGoodsService.findProductBySkuId(itemDto.getSkuId());
            if(null != productVo && productVo.getStatus() == ProductStatusEnum.SELL_OFF.getStatus()){
                // 判断上架状态
                throw new ServiceException( productVo.getName() + "已下架");
            }

            ProductVo linkProductVo = remoteGoodsService.findLinkProductBySkuId(itemDto.getSkuId());
            if(null != linkProductVo && linkProductVo.getStatus() == ProductStatusEnum.SELL_OFF.getStatus()){
                // 判断上架状态
                throw new ServiceException( productVo.getName() + "-关联商品已下架");
            }

            if(productVo.getProductType()== ProductTypeEnum.PACKAGE_PRODUCT.getStatus()){
                // 权益包商品判断使用期限
                DateTime endDate = DateUtil.parse(productVo.getPackageEndTime(), "yyyy-MM-dd");
                DateTime nowDate = DateUtil.parse(DateUtil.format(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd");
                if(endDate.isBefore(nowDate)){
                    throw new ServiceException( productVo.getName() + "已过期");
                }
            }
        }
    }


    /**
     * 根据商品的销售价格获取总价
     *
     * @param itemVoList
     * @param itemDtoList
     * @return java.math.BigDecimal
     * <AUTHOR>
     * @date 2020/7/26 14:36
     */
    private BigDecimal getTotalAmount(List<ItemVo> itemVoList, List<ItemDto> itemDtoList) {
        BigDecimal totalAmount = BigDecimal.ZERO;
        Map<String, ItemVo> itemVoMap = itemVoList.stream().collect(Collectors.toMap(t->t.getProductSkuId()+":"+t.getMemberTypeId(),ItemVo->ItemVo));
        for (ItemDto itemDto : itemDtoList) {
            ItemVo itemVo = itemVoMap.get(itemDto.getSkuId()+":"+itemDto.getMemberTypeId());
            itemVo.setProductQuantity(itemDto.getNumber());
            BigDecimal itemTotal = itemVo.getProductPrice().multiply(BigDecimal.valueOf(itemVo.getProductQuantity()));
            totalAmount = totalAmount.add(itemTotal);
        }
        return totalAmount;
    }
    /**
     * 根据通惠证商品的销售价格获取总价
     *
     * @param ticketVos
     * @param itemDtoList
     * @return java.math.BigDecimal
     * <AUTHOR>
     * @date 2020/7/26 14:36
     */
    private BigDecimal getTotalTicketAmount(List<TicketVo> ticketVos, List<TicketItemDto> itemDtoList) {
        BigDecimal totalAmount = BigDecimal.ZERO;
        Map<Long, TicketVo> itemVoMap = ticketVos.stream().collect(Collectors.toMap(TicketVo::getProductId, v -> v));
        for (TicketItemDto ticketItemDto : itemDtoList) {
            TicketVo ticketVo = itemVoMap.get(ticketItemDto.getTicketId());
            ticketVo.setProductQuantity(ticketItemDto.getNumber());
            BigDecimal itemTotal = ticketVo.getProductPrice().multiply(BigDecimal.valueOf(ticketVo.getProductQuantity()));
            totalAmount = totalAmount.add(itemTotal);
        }
        return totalAmount;
    }

    /**
     * 检查用户收货地址信息
     *
     * @param addressList
     * @param dto
     * @return com.medusa.gruul.account.api.entity.MiniAccountAddress
     * <AUTHOR>
     * @date 2020/7/26 14:37
     */
    private MiniAccountAddress checkAddress(List<MiniAccountAddress> addressList, CreateOrderDto dto) {
        MiniAccountAddress accountAddress = new MiniAccountAddress();
        if (CollUtil.isEmpty(addressList)) {
            throw new ServiceException("收货地址数据已更新,请刷新后重试");
        }
        for (MiniAccountAddress address : addressList) {
            if (address.getId().equals(dto.getMiniAccountAddressId())) {
                accountAddress = address;
            }
        }
        if (ObjectUtil.isNull(accountAddress)) {
            throw new ServiceException(OrderCode.NO_VALID_SHIP_TO_ADDRESS);
        }
        return accountAddress;
    }




    /**
     * 创建订单
     *
     * @param dto
     * @param orderGroupId
     * @param skuStockList
     * @param curUser 订单用户（订单所有者）
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2020/7/26 14:37
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createOrder(CreateOrderDto dto, Long orderGroupId, List<SkuStock> skuStockList, CurUserDto curUser) {
        List<WarehouseShopDto> warehouseShopDtoList = dto.getWarehouseShopDtoList();
        boolean done = false;
        OrderFailedRedisKey orderFailed = new OrderFailedRedisKey();
        log.info("订单用户信息:" + curUser.toString());

        String userId = curUser.getUserId();

        //查询会员持有的积分、收货地址
        AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(curUser.getUserId(), Arrays.asList(1,2
                , 3, 5));

        BigDecimal payGolden = dto.getPayGolden();
        BigDecimal payCommission = dto.getPayCommission();

        //判断当前金豆是否足够
        if(payGolden.compareTo(accountInfoDto.getMiniAccountunt().getCurrentGolden())>0){
            throw new ServiceException("金豆余额不足");
        }
        //判断当前佣金是否足够
        if(payCommission.compareTo(accountInfoDto.getMiniAccountunt().getCurrentCommission())>0){
            throw new ServiceException("佣金余额不足");
        }
        // 1.先计算跨店铺使用优惠券
        List<ItemDto> itemList = dto.getItemDtoList();
        //查询商品描述信息
        //会员等级id
        //String memberId = accountInfoDto.getMiniAccountunt().getMemberLevelId();
        List<Map<String,String>>dataList = new ArrayList<>();
        for (ItemDto itemDto : itemList) {
            Map<String,String>dataMap = new HashMap<>();
            dataMap.put("skuId",itemDto.getSkuId()+"");
            dataMap.put("memberTypeId",itemDto.getMemberTypeId()+"");
            dataMap.put("productQuantity",itemDto.getNumber()+"");
            dataList.add(dataMap);
        }
        //List<ItemVo> itemVoList = remoteGoodsService.findItemVoByIds(dto.getItemSkuIds());
        String jsonString = JSON.toJSONString(dataList);
        List<ItemVo> itemVoList = remoteGoodsService.findItemVoByItemVo(jsonString);
        Integer mallOrderType = dto.getMallOrderType();
        Long upgradeMemberLevelId = dto.getUpgradeMemberLevelId();
        Long upgradeMemberTypeId = dto.getUpgradeMemberTypeId();

        //判断是否为升级订单
        if(mallOrderType!=null && mallOrderType == ProductTypeEnum.UPGRADE_PRODUCT.getStatus()){

            if(upgradeMemberTypeId==null){
                throw new ServiceException("订单提交参数异常【会员升级类型为空】，请重新提交");
            }
            MemberType memberType= remoteMiniAccountService.getMemberTypeById(upgradeMemberTypeId);
            if(memberType == null){
                throw new ServiceException("订单提交参数异常【会员升级类型不存在】，请重新提交");
            }
            if(memberType.getStatus() == MemberTypeStatusEnum.NO.getStatus()){
                throw new ServiceException("该会员类型已停用，无法升级");
            }

            MemberLevelRuleMessage memberLevelRuleMessage = remoteMiniAccountService.getMemberLevelRuleMessageByMemberTypeId(upgradeMemberTypeId);
            if(memberLevelRuleMessage == null){
                log.error("会员升级类型对应会员规则信息不存在");
                throw new ServiceException("订单提交参数异常【升级会员等级异常】，请重新提交");
            }
            String type = memberLevelRuleMessage.getType();
            if(StringUtils.isEmpty(type)){
                log.error("会员升级类型对应会员规则信息升级方式不存在");
                throw new ServiceException("订单提交参数异常【升级会员等级异常】，请重新提交");
            }

            if(upgradeMemberLevelId!=null){

                if(!memberLevelRuleMessage.getType().contains(MemberLevelRuleTypeEnum.APPOINT_GOODS.getStatus())){
                    throw new ServiceException("订单提交参数异常【升级规则异常】，请重新提交");
                }
                List<MemberLevelRuleProduct>memberLevelRuleProductList =
                        remoteMiniAccountService.getMemberLevelRuleProductByMainId(memberLevelRuleMessage.getId());

                List<MemberLevelRule>memberLevelRuleList =
                        remoteMiniAccountService.getMemberLevelRuleByMainId(memberLevelRuleMessage.getId());

                MemberLevelRule upgradeMemberLevelRule = null;
                if(memberLevelRuleList != null && !memberLevelRuleList.isEmpty()){
                    for (MemberLevelRule memberLevelRule : memberLevelRuleList) {
                        if(Long.valueOf(memberLevelRule.getMemberLevelId()).equals(upgradeMemberLevelId)){
                            upgradeMemberLevelRule = memberLevelRule;
                        }
                    }
                }else{
                    log.error("会员升级类型对应会员规则记录不存在");
                    throw new ServiceException("订单提交参数异常【升级商品异常】，请重新提交");
                }

                if(upgradeMemberLevelRule == null){
                    log.error("会员升级等级对应的规则记录不存在！");
                    throw new ServiceException("订单提交参数异常【升级商品异常】，请重新提交");
                }

                if(!(memberLevelRuleProductList!=null && !memberLevelRuleProductList.isEmpty())){
                    log.error("会员升级类型对应会员规则商品不存在");
                    throw new ServiceException("订单提交参数异常【升级商品异常】，请重新提交");
                }
                boolean b = false;
                for (ItemVo itemVo : itemVoList) {
                    for (MemberLevelRuleProduct memberLevelRuleProduct : memberLevelRuleProductList) {
                        if(itemVo.getProductSkuId().equals(Long.valueOf(memberLevelRuleProduct.getSkuId()))){
                            b = true;
                            itemVo.setUpgradeProductQuantity(upgradeMemberLevelRule.getProductQty());
                            itemVo.setUpgradeProductPrice(upgradeMemberLevelRule.getProductAmount().divide(new BigDecimal(upgradeMemberLevelRule.getProductQty()),2, RoundingMode.HALF_UP));
                            //设置金额
                            itemVo.setAmount(upgradeMemberLevelRule.getProductAmount());
                            break;
                        }
                    }
                }
                if(!b){
                    throw new ServiceException("订单提交参数异常【升级商品异常】，请重新提交");
                }

            }else{

                if(memberLevelRuleMessage.getType().contains(MemberLevelRuleTypeEnum.APPOINT_GOODS.getStatus())){
                    throw new ServiceException("订单提交参数异常【升级会员等级异常】，请重新提交");
                }
                List<MemberLevelRuleProduct>memberLevelRuleProductList =
                        remoteMiniAccountService.getMemberLevelRuleProductByMainId(memberLevelRuleMessage.getId());
                if(memberLevelRuleProductList!=null && !memberLevelRuleProductList.isEmpty()){
                    boolean b = false;
                    for (ItemVo itemVo : itemVoList) {
                        for (MemberLevelRuleProduct memberLevelRuleProduct : memberLevelRuleProductList) {
                            if(itemVo.getProductSkuId().equals(Long.valueOf(memberLevelRuleProduct.getSkuId()))){
                                b = true;
                                break;
                            }
                        }
                    }
                    if(!b){
                        throw new ServiceException("订单提交参数异常【升级商品异常】，请重新提交");
                    }
                }else{
                    log.error("会员升级类型对应会员规则商品不存在");
                    throw new ServiceException("订单提交参数异常【升级商品异常】，请重新提交");
                }
            }

        }

        // 未分店铺订单前所有订单的订单总金额(未减除优惠金额)
        final BigDecimal[] totalAmount = {BigDecimal.ZERO};
        // 未分店铺订单前所有订单的订单实际支付总金额(已减除优惠金额，已加上运费)
        BigDecimal totalPayAmount = BigDecimal.ZERO;
        List<MiniAccountCouponOrderItemDto> miniAccountCouponOrderItemList = new ArrayList<>();
        itemVoList.forEach(itemVo -> {
            //复购价
            Integer count = orderItemMapper.getCountByMemberType(curUser.getUserId(),itemVo.getMemberTypeId(), OrderStatusEnum.getPaidStatus());
            if(!dto.getNotCheckFlag()){
                if(count>0){
                    itemVo.setBuyType(BuyTypeEnum.AGAIN.getCode());
                }else{
                    itemVo.setBuyType(BuyTypeEnum.ONE.getCode());
                }
            }
            //升级订单
            if(dto.getMallOrderType()!=null&&dto.getMallOrderType() == ProductTypeEnum.UPGRADE_PRODUCT.getStatus()){
                //升级价格
                if(itemVo.getUpgradeProductPrice()!=null&&itemVo.getUpgradeProductQuantity()!=null){
                    itemVo.setProductPrice(itemVo.getUpgradeProductPrice());
                    itemVo.setProductQuantity(itemVo.getUpgradeProductQuantity());
                    //设置金额
                    itemVo.setAmount(itemVo.getAmount());
                }
            }else{
                //会员类型id
                Long memberTypeId = itemVo.getMemberTypeId();
                //若商品传入的会员类型为空，则将默认会员等级所属的会员类型作为传入的会员类型
                if(memberTypeId == null){
                    //获取默认会员等级
                    MemberLevel memberLevel = remoteMiniAccountService.getDefaultMemberLevel();
                    if(memberLevel == null){
                        throw new ServiceException("默认会员等级为空！");
                    }
                    memberTypeId = memberLevel.getMemberTypeId();
                }
                String memberLevelId = remoteMiniAccountService.getMemberLevelId(accountInfoDto.getMiniAccountunt().getUserId(), memberTypeId);

                //根据会员的id来获取对应的全部会员商品价格
                //List<MemberLevelGoodsPrice> memberLevelGoodsPriceList = remoteGoodsService.selectMemberGoodsPrice(memberLevelId);
                //List<MemberLevelGoodsAgainPrice> memberLevelGoodsAgainPriceList = remoteGoodsService.selectMemberGoodsAgainPrice(memberLevelId);

                BigDecimal productPrice = itemVo.getProductPrice();
                //会员价
                List<MemberLevelGoodsPrice> memberLevelGoodsPricesList = this.remoteGoodsService.selectMemberGoodsPriceByProductId(memberLevelId, itemVo.getProductId(), itemVo.getProductSkuId());
                if(null != memberLevelGoodsPricesList && !memberLevelGoodsPricesList.isEmpty()){
                    if (memberLevelGoodsPricesList.get(0).getMemberLevelPrice()!=null){
                        if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==itemVo.getMemberPriceType()){
                            itemVo.setProductPrice(memberLevelGoodsPricesList.get(0).getMemberLevelPrice());
                        }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==itemVo.getMemberPriceType()){
                            itemVo.setProductPrice(productPrice.multiply(memberLevelGoodsPricesList.get(0).getMemberLevelPrice()).divide(new BigDecimal(100),4, RoundingMode.HALF_UP));
                        }
                    }
                }
                if(count > 0){
                    // 复购价
                    List<MemberLevelGoodsAgainPrice> memberLevelGoodsAgainPricesList = this.remoteGoodsService.selectMemberGoodsAgainPriceByProductId(memberLevelId, itemVo.getProductId(), itemVo.getProductSkuId());
                    if(null != memberLevelGoodsAgainPricesList && !memberLevelGoodsAgainPricesList.isEmpty()){
                        if (memberLevelGoodsAgainPricesList.get(0).getMemberLevelAgainPrice()!=null){
                            if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==itemVo.getMemberAgainPriceType()){
                                itemVo.setProductPrice(memberLevelGoodsAgainPricesList.get(0).getMemberLevelAgainPrice());
                            }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==itemVo.getMemberAgainPriceType()){
                                itemVo.setProductPrice(productPrice.multiply(memberLevelGoodsAgainPricesList.get(0).getMemberLevelAgainPrice()).divide(new BigDecimal(100),4, RoundingMode.HALF_UP));
                            }
                        }
                    }

                }
            }
            // 设置非升级订单的金额
            if(!(itemVo.getUpgradeProductPrice()!=null && itemVo.getUpgradeProductQuantity()!=null)){
                itemVo.setAmount(itemVo.getProductPrice().multiply(new BigDecimal(itemVo.getProductQuantity())));
            }
            if (dto.getNotCheckFlag()){
                itemVo.setProductPrice(BigDecimal.ZERO);
                itemVo.setAmount(BigDecimal.ZERO);
            }

            totalAmount[0] = totalAmount[0].add(itemVo.getAmount());
            MiniAccountCouponOrderItemDto miniAccountCouponOrderItemDto = new MiniAccountCouponOrderItemDto();
            miniAccountCouponOrderItemDto.setNumber(new BigDecimal(itemVo.getProductQuantity()));
            miniAccountCouponOrderItemDto.setMemberTypeId(itemVo.getMemberTypeId());
            miniAccountCouponOrderItemDto.setSkuId(itemVo.getProductSkuId() + "");
            miniAccountCouponOrderItemDto.setProductPrice(itemVo.getProductPrice());
            miniAccountCouponOrderItemDto.setShopId(itemVo.getShopId());
            miniAccountCouponOrderItemDto.setAmount(itemVo.getAmount());
            miniAccountCouponOrderItemList.add(miniAccountCouponOrderItemDto);
        });

        // 改为：1.先校验优惠券是否可用，2.优惠券可用，则将优惠金额分解到每条明细中
        // 未分店铺订单前所有订单的优惠券总金额
        BigDecimal couponAmount;
        Map<String, ItemVo> itemVoMap = itemVoList.stream().collect(Collectors.toMap(t->t.getProductSkuId()+":"+t.getMemberTypeId(),ItemVo->ItemVo));
        //计算优惠金额
        Long accountCouponId = dto.getAccountCouponId();
        if(accountCouponId!=null){
            //查看用户可用优惠券：只查最大满额的一张或者用户手动选择的优惠券
            MiniAccountCouponByOrderDto miniAccountCouponByOrderDto = new MiniAccountCouponByOrderDto();
            miniAccountCouponByOrderDto.setUserId(userId);
            miniAccountCouponByOrderDto.setReplaceCreateOrderFlag(dto.getReplaceCreateOrderFlag());
            miniAccountCouponByOrderDto.setData(miniAccountCouponOrderItemList);
            miniAccountCouponByOrderDto.setMiniAccountCouponId(accountCouponId);
            miniAccountCouponByOrderDto.setAllFlag(false);

            List<MiniAccountCouponByOrderVo> miniAccountCouponByOrderList = remoteMiniAccountService.getCouponByUser(miniAccountCouponByOrderDto);
            //将返回的优惠券数据转换且分解到订单商品明细的优惠金额字段

            //符合使用优惠券的订单明细总金额
            if(miniAccountCouponByOrderList!=null && !miniAccountCouponByOrderList.isEmpty()) {
                miniAccountCouponByOrderList = miniAccountCouponByOrderList.stream().filter(e -> e.getId().equals(accountCouponId)).collect(Collectors.toList());
                MiniAccountCouponByOrderVo miniAccountCouponByOrderVo = miniAccountCouponByOrderList.get(0);

                //优惠金额--目前只有满减额
                couponAmount = miniAccountCouponByOrderVo.getPromotion();
                // 对得到优惠券条件的商品的金额进行求和
                BigDecimal couponTotalAmount = miniAccountCouponByOrderDto.getData().stream().filter(e -> e.getMiniAccountCouponId() != null).map(MiniAccountCouponOrderItemDto::getAmount)
                        .filter(Objects::nonNull) // 过滤 null 值（可选）
                        .reduce(BigDecimal.ZERO, BigDecimal::add); // 初始值为 0，逐个相加

                miniAccountCouponByOrderDto.getData().forEach(e -> {
                    if (e.getMiniAccountCouponId() != null) {
                        //计算优惠券分解金额
                        BigDecimal itemPromotionAmount = e.getAmount().divide(couponTotalAmount, 2, RoundingMode.HALF_UP).multiply(couponAmount);
                        String key = e.getSkuId() + ":" + e.getMemberTypeId();
                        if (null != itemVoMap.get(key)) {
                            ItemVo itemVo = itemVoMap.get(key);
                            itemVo.setYouhuiPrice(itemPromotionAmount);
                            itemVo.setMiniAccountCouponId(e.getMiniAccountCouponId());
                        }
                    }
                });
            } else {
                couponAmount = BigDecimal.ZERO;
            }

            //用户优惠券标记为已使用
            AccountCouponDto accountCouponDto = new AccountCouponDto();
            accountCouponDto.setUserId(userId);
            accountCouponDto.setOrderGroupId(orderGroupId);
            accountCouponDto.setAccountCouponId(accountCouponId);
            Boolean couponFlag = remoteMiniAccountService.updateAccountCouponOk(accountCouponDto);
            if(!(couponFlag != null && couponFlag )){
                throw new ServiceException("优惠券使用失败");
            }
        } else {
            couponAmount = BigDecimal.ZERO;
        }

        //优惠券计算完成，计算满减满赠
        // 计算满减满赠逻辑，在此方法里已对明细的促销金额进行分解赋值
        this.checkFullDonation(itemVoList, accountInfoDto);
        // 已被处理过的满减满赠活动id
        List<Long> fullDonationIdList = new ArrayList<>();
        // 满减满赠活动标识
        AtomicBoolean fullDonationFlag = new AtomicBoolean(false);
        // 活动满减总额
        AtomicReference<BigDecimal> fullDonationAmount = new AtomicReference<>(BigDecimal.ZERO);
        // 活动赠送的优惠券
        List<ShopCoupon> giftCoupons = new ArrayList<>();
        // 活动赠送的商品
        List<ItemVo> giftGoodsItemVoList = new ArrayList<>();
        itemVoList.forEach(itemVo -> {
            if(itemVo.getShopFullDonationRuleVo() != null){
                fullDonationFlag.set(true);
                if(!fullDonationIdList.contains(itemVo.getShopFullDonationRuleVo().getId())){
                    // 计算满减总额
                    if(itemVo.getShopFullDonationRuleVo().getReductionAmount() != null && itemVo.getShopFullDonationRuleVo().getReductionAmount().compareTo(BigDecimal.ZERO) > 0){
                        fullDonationAmount.set(fullDonationAmount.get().add(itemVo.getShopFullDonationRuleVo().getReductionAmount()));
                    }
                    // 计算赠送优惠券
                    if(StrUtil.isNotBlank(itemVo.getShopFullDonationRuleVo().getCouponIds())){
                        // 根据优惠券id查询优惠券
                        // 将逗号分隔的id组装成list，且将string转换成Long类型
                        List<String> couponIdStrList = Arrays.asList(itemVo.getShopFullDonationRuleVo().getCouponIds().split(","));
                        List<Long> couponIdList = couponIdStrList.stream().map(Long::valueOf).collect(Collectors.toList());
                        List<ShopCoupon> shopCouponList = this.remoteShopsService.getCouponByIds(couponIdList);
                        giftCoupons.addAll(shopCouponList);
                    }
                    // 计算赠送的商品
                    if(StrUtil.isNotBlank(itemVo.getShopFullDonationRuleVo().getSkuIds())){
                        // 将逗号分隔的id组装成list，且将string转换成Long类型
                        List<String> skuIdStrList = Arrays.asList(itemVo.getShopFullDonationRuleVo().getSkuIds().split(","));
                        List<Long> skuIdList = skuIdStrList.stream().map(Long::valueOf).collect(Collectors.toList());
                        List<ItemVo> itemVoByIds = this.remoteGoodsService.findItemVoByIds(skuIdList);
                        // 设置itemVoByIds 产品数量为1
                        itemVoByIds.forEach(itemVo2 -> {
                            itemVo2.setProductQuantity(1);
                            itemVo2.setProductPrice(BigDecimal.ZERO);
                            itemVo2.setMemberTypeId(itemVo.getMemberTypeId());
                            itemVo2.setGiftFlag(GiftFlagEnum.YES.getCode());
                            itemVo2.setBuyType(itemVo2.getBuyType());
                            itemVo2.setAmount(itemVo2.getProductPrice().multiply(new BigDecimal(itemVo2.getProductQuantity())));
                        });
                        giftGoodsItemVoList.addAll(itemVoByIds);
                    }
                    fullDonationIdList.add(itemVo.getShopFullDonationRuleVo().getId());
                }
                // 计算赠送自身件数
                if(itemVo.getShopFullDonationRuleVo().getSelfNum() != null && itemVo.getShopFullDonationRuleVo().getSelfNum() > 0){
                    ItemVo giftItemVo = new ItemVo();
                    BeanUtil.copyProperties(itemVo, giftItemVo);
                    giftItemVo.setShopFullDonationRuleVo(null);
                    giftItemVo.setFullDonationId(null);
                    giftItemVo.setProductQuantity(itemVo.getProductQuantity() * itemVo.getShopFullDonationRuleVo().getSelfNum());
                    giftItemVo.setProductPrice(BigDecimal.ZERO);
                    giftItemVo.setGiftFlag(GiftFlagEnum.YES.getCode());
                    giftItemVo.setPromotionAmount(BigDecimal.ZERO);
                    giftItemVo.setYouhuiPrice(BigDecimal.ZERO);
                    giftItemVo.setAmount(giftItemVo.getProductPrice().multiply(new BigDecimal(giftItemVo.getProductQuantity())));
                    giftGoodsItemVoList.add(giftItemVo);
                }

            }
        });
        // 将满减满赠活动赠送的商品加入队列传送过来的商品明细中
        if(!giftGoodsItemVoList.isEmpty()){
           itemVoList.addAll(giftGoodsItemVoList);
        }

        // 分组订单：按照店铺、产品类型将商品明细分组创建订单
        Map<String, Map<Integer, List<ItemVo>>> itemVoShopGroupMap = itemVoList.stream()
                .collect(Collectors.groupingBy(
                        ItemVo::getShopId,
                        Collectors.groupingBy(ItemVo::getProductType)
                ));
        // 将生成订单的需要做的数据库操作先存起来，然后再一起顺序执行
        //扣减库存
        List<List<OperateStockDto>> allOperateStockDtoList = new ArrayList<>();
        // 回退扣减库存
        List<List<BatchRevertStockDto>> allSkuStockRevertList = new ArrayList<>();
        // 扣减账户余额
        //List<MiniAccountBalanceDto> miniAccountBalanceDtoList = new ArrayList<>();
        // 订单
        List<Order> allOrderList = new ArrayList<>();
        // 订单明细
        List<List<OrderItem>> allOrderItemList = new ArrayList<>();
        //获取用户收货地址
        MiniAccountAddress accountAddress = checkAddress(accountInfoDto.getMiniAccountAddress(), dto);

        for (Map.Entry<String, Map<Integer, List<ItemVo>>> entry : itemVoShopGroupMap.entrySet()) {
            String shopId = entry.getKey();
            Map<Integer, List<ItemVo>> itemVoProductTypeGroupMap = entry.getValue();
            for (Map.Entry<Integer, List<ItemVo>> entry2 : itemVoProductTypeGroupMap.entrySet()) {
                //ItemShopDto itemShopDto = new ItemShopDto();
                //预生成的订单ID
                Long orderId = IdWorker.getId();
                Integer productType = entry2.getKey();
                List<ItemVo> itemVoProductTypeList = entry2.getValue();
                //itemShopDto.setShopId(shopId);
                //itemShopDto.setItemDtos(list2);
                //itemShopDto.setProductType(productType);
                //itemShopDtoList.add(itemShopDto);
                //创建订单详情并且获得每个商品的总价，且计算每条明细的实际付款金额
                List<OrderItem> orderItemList = getOrderItemList(itemVoProductTypeList, orderId, orderGroupId, shopId);
                // 通过订单明细计算订单总额、订单优惠券总额、订单促销优惠总额
                BigDecimal orderTotalAmount = orderItemList.stream() .map(item -> {
                            BigDecimal qty = BigDecimal.valueOf(Optional.ofNullable(item.getProductQuantity()).orElse(0));
                            BigDecimal prc = Optional.ofNullable(item.getProductPrice()).orElse(BigDecimal.ZERO);
                            return qty.multiply(prc);
                        }).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal orderCouponAmount = orderItemList.stream() .map(item -> Optional.ofNullable(item.getCouponAmount()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal orderPromotionAmount = orderItemList.stream() .map(item -> Optional.ofNullable(item.getPromotionAmount()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                Optional<Long> orderMiniAccountCouponId = orderItemList.stream()
                        .map(OrderItem::getMiniAccountCouponId)  // 假设字段类型为String
                        .filter(Objects::nonNull)
                        .findFirst();

                //物流配送才需要计算运费
                List<ItemDto> itemDtoList = new ArrayList<>(itemVoProductTypeList.size());
                if(!itemVoProductTypeList.isEmpty()){
                    itemVoProductTypeList.forEach(e -> {
                        ItemDto itemDto = new ItemDto();
                        BeanUtil.copyProperties(e, itemDto);
                        itemDto.setSkuId(e.getProductSkuId());
                        itemDto.setNumber(e.getProductQuantity());
                        itemDtoList.add(itemDto);
                    });
                }
                BigDecimal freightAmount = BigDecimal.ZERO;
                if (dto.getDeliverType().equals(DeliverTypeEnum.LOGISTICS)) {
                    //计算运费
                    CountCostDto costDto = getFreightAmount(accountAddress, dto.getDeliverType(), itemDtoList);
                    freightAmount = costDto.getCost();
                    if (ObjectUtil.isNull(freightAmount) || freightAmount.equals(BigDecimal.valueOf(-1))) {
                        freightAmount = BigDecimal.ZERO;
                    }
                }

                //扣除库存
                Set<OperateStockDto> skuSet = new HashSet<>();
                //回退扣除库存
                List<BatchRevertStockDto> skuStockRevertList = new ArrayList<>();
                for (ItemVo itemVoData : itemVoProductTypeList) {
                    skuSet.addAll(skuStockList.stream()
                            .map(vo -> new OperateStockDto(itemVoData.getProductSkuId(), itemVoData.getProductQuantity()))
                            .collect(Collectors.toSet()));
                    BatchRevertStockDto batchRevertStockDto = new BatchRevertStockDto();
                    batchRevertStockDto.setNumber(itemVoData.getProductQuantity());
                    batchRevertStockDto.setSkuId(itemVoData.getProductSkuId());
                    skuStockRevertList.add(batchRevertStockDto);
                }
                allSkuStockRevertList.add(skuStockRevertList);
                //删除购物车数据
                //remoteGoodsService.deleteShoppingCartByOrder(dto.getItemSkuIds(), curUser.getUserId());
                remoteGoodsService.deleteShoppingCartByJson(jsonString, curUser.getUserId());

                //sku规格总库存扣减
                List<OperateStockDto> operateStockDtoList = new ArrayList<>(skuSet);
                //boolean goodsSuccess = remoteGoodsService.batchSubtractStock(operateStockDtoList);
                allOperateStockDtoList.add(operateStockDtoList);
                //创建订单
                Order order = new Order();
                order.setId(orderId);
                order.setShopId(shopId);
                order.setOrderGroupId(orderGroupId);
                order.setUserId(curUser.getUserId());
                order.setUserName(curUser.getNikeName());
                order.setUserAvatarUrl(curUser.getAvatarUrl());
                order.setUserNote(dto.getUserNote());
                if(accountAddress!=null){
                    order.setProvinceCode(accountAddress.getProvinceCode());
                    order.setCityCode(accountAddress.getCityCode());
                    order.setCountyCode(accountAddress.getCountyCode());
                }
                order.setType(dto.getOrderType());
                if(warehouseShopDtoList!=null && !warehouseShopDtoList.isEmpty() && StringUtil.isNotEmpty(shopId)){
                    for (WarehouseShopDto warehouseShopDto : warehouseShopDtoList) {
                        if(warehouseShopDto.getShopId().equals(shopId) && StrUtil.isNotBlank(warehouseShopDto.getWarehouseId())){
                            order.setWarehouseId(Long.parseLong(warehouseShopDto.getWarehouseId()));
                        }
                    }
                }
                order.setTotalAmount(orderTotalAmount.add(freightAmount));
                order.setPromotionAmount(orderPromotionAmount);
                //应付金额（实际支付金额）=订单总金额-促销优化金额+运费
                BigDecimal payAmount = NumberUtil.sub(orderTotalAmount, orderPromotionAmount);
                //减掉优惠券金额
                payAmount = NumberUtil.sub(payAmount, orderCouponAmount);
                order.setYouhuiPrice(orderCouponAmount);
                order.setCouponId(orderMiniAccountCouponId.orElse(null));
                order.setOrderUserId(dto.getOrderUserId());

                if (payAmount.compareTo(BigDecimal.ZERO) < 0) {
                    payAmount = BigDecimal.ZERO.add(freightAmount);
                } else {
                    payAmount = payAmount.add(freightAmount);
                }
                totalPayAmount = totalPayAmount.add(payAmount);
                //BigDecimal cashAmount = payAmount.subtract(dto.getPayCommission().add(dto.getPayGolden()));

                //实际金额=应付金额-退款金额
                order.setDiscountsAmount(payAmount.setScale(2, RoundingMode.HALF_UP));
                order.setPayAmount(payAmount.setScale(2, RoundingMode.HALF_UP));
                order.setFreightAmount(freightAmount);
                order.setCashAmount(payAmount.setScale(2, RoundingMode.HALF_UP));
                order.setGoldenAmount(BigDecimal.ZERO);
                order.setCommissionAmount(BigDecimal.ZERO);
                order.setPayType(dto.getPayType());
                order.setSourceType(dto.getSourceType());
                order.setMallOrderType(dto.getMallOrderType());

                order.setUpgradeMemberLeverId(dto.getUpgradeMemberLevelId());
                order.setUpgradeMemberTypeId(dto.getUpgradeMemberTypeId());

                order.setStatus(OrderStatusEnum.WAIT_FOR_PAY);
                order.setCustomForm(dto.getCustomForm());
                order.setStoreFrontId(dto.getStoreFrontId());
                //order.setMemberId(memberId);
                order.setUploadEffectStatus(CommonConstants.NUMBER_ZERO);
                //保存邀请人用户id-改为邀请码了
                if(StrUtil.isNotBlank(dto.getInviteCode())){
                    // 解密
                    try {
                        String inviteCode = URLDecoder.decode(dto.getInviteCode(), "UTF-8");
                        log.info("登录方法，传进来的邀请码，解码后：{}", inviteCode);
                        String codeDes = AESUtil.decryptDataBase64MD5Key(inviteCode, null);
                        log.info("登录方法，传进来的邀请码，解密后：{}", codeDes);
                        order.setInviteUserId(codeDes);
                    } catch (Exception exception) {
                        exception.printStackTrace();
                    }
                }
                //创建订单收货
                //installOrderDelivery(dto, orderId, accountAddress,curUser);
                //备注
                order.setNote(dto.getNote());
                //orderMapper.insert(order);
                log.info("订单{},支付金额:{},总金额:{},运费:{},优惠券金额：{},促销优惠金额：{}", JSONObject.toJSONString(order), payAmount, orderTotalAmount,
                        freightAmount, orderCouponAmount, orderPromotionAmount);
                allOrderList.add(order);
                /*for (OrderItem orderItem : orderItemList) {
                    orderItemMapper.insert(orderItem);
                }*/
                allOrderItemList.add(orderItemList);
                // 现金支付小于等于0
                /*if(order.getCashAmount().compareTo(new BigDecimal(0))<=0){
                    paymentNotify(order.getId());
                }*/
                //done = true;
                // 订单自动取消
                /*BaseOrderMessage baseOrderMessage = new BaseOrderMessage();
                baseOrderMessage.setOrderId(order.getId());
                baseOrderMessage.setTenantId(order.getTenantId());
                baseOrderMessage.setShopId(order.getShopId());
                sender.sendAutoCancelOrderMessage(baseOrderMessage, getExTime(order.getType()));*/
            }
        }
        // 插入订单及其其他操作
        if(!allOrderList.isEmpty()){
            for (int i = 0; i < allOrderList.size(); i++) {
                int errorResult = 0;
                Order order = allOrderList.get(i);
                List<OrderItem> orderItemList = allOrderItemList.get(i);
                // 计算佣金、金豆分解金额
                if(payGolden.add(payCommission).compareTo(BigDecimal.ZERO) > 0){
                    if(payGolden.compareTo(BigDecimal.ZERO) > 0){
                        BigDecimal orderPayGolden = order.getPayAmount().divide(totalPayAmount,2, RoundingMode.HALF_UP).multiply(payGolden);
                        order.setGoldenAmount(orderPayGolden);
                        order.setCommissionAmount(null);
                    }
                    if(payCommission.compareTo(BigDecimal.ZERO) > 0){
                        BigDecimal orderPayCommission = order.getPayAmount().divide(totalPayAmount,2, RoundingMode.HALF_UP).multiply(payCommission);
                        order.setCommissionAmount(orderPayCommission);
                    }
                    // 重新计算现金支付金额
                    BigDecimal goldenAmount = order.getGoldenAmount() == null ? BigDecimal.ZERO : order.getGoldenAmount();
                    BigDecimal commissionAmount = order.getCommissionAmount() == null ? BigDecimal.ZERO : order.getCommissionAmount();
                    order.setCashAmount(order.getPayAmount().subtract(goldenAmount).subtract(commissionAmount));
                }
                try{
                    // 扣减库存
                    List<OperateStockDto> operateStockDtos = allOperateStockDtoList.get(i);
                    boolean goodsSuccess = remoteGoodsService.batchSubtractStock(operateStockDtos);
                    if(goodsSuccess){
                        // 扣除库存成功
                        errorResult = 1;
                        // 扣减个人佣金、金豆余额
                        BigDecimal goldenAmount = order.getGoldenAmount() == null ? BigDecimal.ZERO : order.getGoldenAmount();
                        BigDecimal commissionAmount = order.getCommissionAmount() == null ? BigDecimal.ZERO : order.getCommissionAmount();
                        boolean orderNext = true;
                        if(goldenAmount.add(commissionAmount).compareTo(BigDecimal.ZERO) > 0){
                            MiniAccountBalanceDto miniAccountBalanceDto = new MiniAccountBalanceDto();
                            miniAccountBalanceDto.setUserId(accountInfoDto.getMiniAccountunt().getUserId());
                            miniAccountBalanceDto.setPayCommission(commissionAmount);
                            miniAccountBalanceDto.setPayGolden(goldenAmount);
                            miniAccountBalanceDto.setOrderId(order.getId() + "");
                            boolean miniAccountSuccess = remoteMiniAccountService.subtractMiniAccountBalance(miniAccountBalanceDto);
                            if(miniAccountSuccess){
                                errorResult = 2;
                            }else{
                                orderNext = false;
                                //先取消掉，暂时没有用的
                                //OrderFailMessage failMessage = new OrderFailMessage().stockFail(dto.getCouponId(),curUser.getUserId(), curUser.getShopId(),TenantContextHolder.getTenantId());
                                //sender.sendCreateOrderFailMessage(failMessage);
                                log.error("扣减用户余额失败");
                                orderFailed.setNxPx(orderGroupId.toString(), "扣减用户余额失败", TimeConstants.ONE_DAY);
                                log.error("恢复商品库存！");

                                //恢复库存
                                BatchRevertStockMessage message = new BatchRevertStockMessage();
                                message.setList(allSkuStockRevertList.get(i));
                                message.setTenantId(TenantContextHolder.getTenantId());
                                sender.sendBatchRevertStockMessage(message);
                            }
                        }
                        if(orderNext){
                            //创建订单收货
                            dto.setWarehouseId(order.getWarehouseId() == null ? "" : order.getWarehouseId() + "");
                            installOrderDelivery(dto, order.getId(), accountAddress,curUser, order.getShopId());
                            // 插入订单
                            orderMapper.insert(order);
                            // 插入订单明细
                            for (OrderItem orderItem : orderItemList) {
                                orderItemMapper.insert(orderItem);
                            }
                            // 判断是否不用现金支付
                            if(order.getCashAmount().compareTo(BigDecimal.ZERO) <= 0){
                                paymentNotify(order.getId());
                            }
                            done = true;
                            // 订单自动取消
                            BaseOrderMessage baseOrderMessage = new BaseOrderMessage();
                            baseOrderMessage.setOrderId(order.getId());
                            baseOrderMessage.setTenantId(order.getTenantId());
                            baseOrderMessage.setShopId(order.getShopId());
                            sender.sendAutoCancelOrderMessage(baseOrderMessage, getExTime(order.getType()));
                        }
                    }else{
                        log.error("库存扣除失败");
                        orderFailed.setNxPx(orderGroupId.toString(), "库存扣除失败", TimeConstants.ONE_DAY);
                    }
                }catch (Exception e) {
                    log.error("创建订单失败:{}", e.getMessage());
                    log.error(e.getMessage(), e);
                    orderFailed.setNxPx(orderGroupId.toString(), e.getMessage(), TimeConstants.ONE_DAY);
                    //判断错误类型
                    //恢复库存
                    log.info("判断错误标识errorResult：{}，大于0恢复库存，大于1恢复库存，恢复金豆，佣金扣减",errorResult);
                    if(errorResult > 0){
                        BatchRevertStockMessage message = new BatchRevertStockMessage();
                        message.setList(allSkuStockRevertList.get(i));
                        message.setTenantId(TenantContextHolder.getTenantId());
                        sender.sendBatchRevertStockMessage(message);
                    }
                    //恢复用户消费余额
                    if(errorResult > 1){
                        AccountReturnBalanceMessage accountReturnBalanceMessage = new AccountReturnBalanceMessage();
                        accountReturnBalanceMessage.setTenantId(TenantContextHolder.getTenantId());
                        accountReturnBalanceMessage.setPayCommission(order.getCommissionAmount());
                        accountReturnBalanceMessage.setPayGolden(order.getGoldenAmount());
                        accountReturnBalanceMessage.setOrderId(order.getId() + "");
                        accountReturnBalanceMessage.setUserId(accountInfoDto.getMiniAccountunt().getUserId());
                        sender.sendRevertMiniAccountBalanceMessage(accountReturnBalanceMessage);
                    }
                    //优惠券要回退
                    if(accountCouponId != null){
                        // 查询在订单中是否有使用，如果没有则回退
                        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.notIn(Order::getStatus, OrderStatusEnum.getInvalidStatus())
                                .eq(Order::getCouponId, accountCouponId);
                        List<Order> orders = this.orderMapper.selectList(queryWrapper);
                        if(null == orders || orders.isEmpty()){
                            AccountCouponDto accountCouponDto = new AccountCouponDto();
                            accountCouponDto.setAccountCouponId(accountCouponId);
                            //accountCouponDto.setOrderGroupId(order.getOrderGroupId());
                            accountCouponDto.setUserId(userId);
                            remoteMiniAccountService.updateAccountCouponNo(accountCouponDto);
                        }
                    }
                }

            }
        }

        log.info("整个方法最后判断订单是否成功：done-{}，不成功则抛出异常，回滚数据",done);
        if(!done){
            throw new ServiceException("创建订单失败！");
        }
        return done;
    }


    /**
     * 创建通惠证订单
     * @param createTicketOrderDto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createTicketOrder(CreateTicketOrderDto createTicketOrderDto) {
        CurUserDto curUser= CurUserUtil.getHttpCurUser();
        OrderFailedRedisKey orderFailed = new OrderFailedRedisKey();
        Long orderId = IdWorker.getId();
        //查询通惠证商品描述信息
        try {
            List<TicketVo> ticketVos = remoteShopsService.queryTicketItemVoByIds(createTicketOrderDto.getItemTicketIds());
            BigDecimal totalAmount = getTotalTicketAmount(ticketVos, createTicketOrderDto.getItemDtoList());
            //创建订单详情并且获得每个商品的总价
            List<OrderItem> orderItemList = getTicketOrderItemList(ticketVos, orderId, createTicketOrderDto.getItemDtoList());
            final boolean[] goodsSuccess = {false};
            //扣除可购买数量，从redis减库存
            ticketVos.forEach(e -> {
                Long ticketId = e.getProductId();
                Long value = remoteShopsService.passTicketSubtractStock(ticketId);
                if(value >= 0L){
                    goodsSuccess[0] = true;
                }
            });


            if (goodsSuccess[0]) {
                //创建订单
                Order order = new Order();
                order.setId(orderId);
                order.setUserId(curUser.getUserId());
                order.setUserName(curUser.getNikeName());
                order.setUserAvatarUrl(curUser.getAvatarUrl());
                order.setType(createTicketOrderDto.getOrderType());
                order.setTotalAmount(totalAmount);
                order.setPromotionAmount(BigDecimal.ZERO);
                //应付金额（实际支付金额）=订单总金额-促销优化金额+运费
                BigDecimal payAmount = NumberUtil.sub(totalAmount, BigDecimal.ZERO);
                if (payAmount.compareTo(BigDecimal.ZERO) < 0) {
                    payAmount = BigDecimal.ZERO.add(BigDecimal.ZERO);
                } else {
                    payAmount = payAmount.add(BigDecimal.ZERO);
                }
                log.info("订单{},支付金额:{},总金额:{},运费:{}", JSONObject.toJSONString(createTicketOrderDto), payAmount, totalAmount, BigDecimal.ZERO);
                //实际金额=应付金额-退款金额
                order.setDiscountsAmount(payAmount.setScale(2, RoundingMode.DOWN));
                order.setPayAmount(payAmount.setScale(2, RoundingMode.DOWN));
                order.setFreightAmount(BigDecimal.ZERO);
                order.setPayType(createTicketOrderDto.getPayType());
                order.setSourceType(createTicketOrderDto.getSourceType());
                order.setStatus(OrderStatusEnum.WAIT_FOR_PAY);
                order.setCashAmount(payAmount.setScale(2, RoundingMode.DOWN));
                //获取主店铺id
                ShopsPartner shopsPartnerMain = remoteShopsService.getShopsPartnerMain();
                if(shopsPartnerMain!=null){
                    order.setShopId(shopsPartnerMain.getShopId());
                }
                orderMapper.insert(order);
                for (OrderItem orderItem : orderItemList) {
                    orderItemMapper.insert(orderItem);
                }
                //通惠证默认发货单为自提
                OrderDelivery orderDelivery = new OrderDelivery();
                orderDelivery.setOrderId(orderId);
                orderDelivery.setDeliveryType(DeliverTypeEnum.SELF);
                orderDelivery.setReceived(false);
                orderDelivery.setShopId(order.getShopId());
                List<Integer> infos = new ArrayList<>();
                infos.add(1);
                AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(curUser.getUserId(), infos);
                orderDelivery.setReceiverName(accountInfoDto.getMiniAccountunt().getNikeName());
                orderDelivery.setReceiverPhone(accountInfoDto.getMiniAccountunt().getPhone());
                orderDeliveryMapper.insert(orderDelivery);
            }else{
                OrderFailMessage failMessage = new OrderFailMessage().stockFail(null,curUser.getUserId(), curUser.getShopId(),TenantContextHolder.getTenantId());
                sender.sendCreateOrderFailMessage(failMessage);
                orderFailed.setNxPx(orderId.toString(), "已售罄", TimeConstants.ONE_DAY);
            }
        }catch (Exception e){
            log.error(e.getMessage(), e);
            orderFailed.setNxPx(orderId.toString(), e.getMessage(), TimeConstants.ONE_DAY);
        }

        return orderId.toString();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createIntegralProductOrder(CreateOrderDto dto, Long orderId, List<SkuStock> skuStockList, CurUserDto curUser) {
        Boolean done = false;
        OrderFailedRedisKey orderFailed = new OrderFailedRedisKey();
        log.info("当前用户信息:" + curUser.toString());
        try {
            //查询会员持有的积分、收货地址
            AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(curUser.getUserId(), Arrays.asList(1,2
                    , 3, 5));
            //查询商品描述信息
            List<ItemVo> itemVoList = remoteGoodsService.findItemVoByIds(dto.getItemSkuIds());
            BigDecimal allIntegral = BigDecimal.ZERO;
            BigDecimal allAmount = BigDecimal.ZERO;
            Map<Long, ItemVo> itemVoMap = itemVoList.stream().collect(Collectors.toMap(ItemVo::getProductSkuId, v -> v));
            for (ItemDto item : dto.getItemDtoList()) {
                ItemVo itemVo = itemVoMap.get(item.getSkuId());
                String integralProductId = item.getIntegralProductId();
                IntegralProduct integralProduct = remoteGoodsService.getIntegralProduct(integralProductId);
                if(integralProduct==null){
                    throw new ServiceException("积分商品不存在！");
                }
                itemVo.setProductQuantity(item.getNumber());
                itemVo.setAmount(integralProduct.getAmount());
                itemVo.setIntegral(integralProduct.getIntegral());
                itemVo.setIntegralProductId(integralProductId);
                allIntegral= allIntegral.add(integralProduct.getIntegral().multiply(new BigDecimal(item.getNumber())));
                if(integralProduct.getAmount()!=null){
                    allAmount = allAmount.add(integralProduct.getAmount().multiply(new BigDecimal(item.getNumber())));
                }
            }
            MiniAccount miniAccount = accountInfoDto.getMiniAccountunt();
            if(miniAccount.getCurrentIntegral().compareTo(allIntegral)<0){
                throw new ServiceException("积分不足");
            }
            //创建订单详情并且获得每个商品的总价
            List<OrderItem> orderItemList = getIntegralOrderItemList(itemVoList, orderId, dto.getItemDtoList());

            //物流配送才需要计算运费
            BigDecimal freightAmount = BigDecimal.ZERO;
            MiniAccountAddress accountAddress = null;
            if (dto.getDeliverType().equals(DeliverTypeEnum.LOGISTICS)) {
                //获取用户收货地址
                accountAddress = checkAddress(accountInfoDto.getMiniAccountAddress(), dto);
                //计算运费
                CountCostDto costDto = getFreightAmount(accountAddress, dto.getDeliverType(),
                        dto.getItemDtoList());
                freightAmount = costDto.getCost();
                if (ObjectUtil.isNull(freightAmount) || freightAmount.equals(BigDecimal.valueOf(-1))) {
                    freightAmount = BigDecimal.ZERO;
                }
            }
            BigDecimal promotionAmount = BigDecimal.ZERO;
            //扣除库存
            List<ItemDto> itemDtoList = dto.getItemDtoList();
            Set<OperateStockDto> skuSet = new HashSet<>();
            List<OperateExchangeNumParam>operateExchangeNumParamList = new ArrayList<>();
            for (ItemDto itemDto : itemDtoList) {
                skuSet.addAll(skuStockList.stream()
                        .map(vo -> new OperateStockDto(itemDto.getSkuId(), itemDto.getNumber()))
                        .collect(Collectors.toSet()));
                OperateExchangeNumParam operateExchangeNumParam = new OperateExchangeNumParam();
                operateExchangeNumParam.setNumber(itemDto.getNumber());
                operateExchangeNumParam.setIntegralProductId(Long.valueOf(itemDto.getIntegralProductId()));
                operateExchangeNumParamList.add(operateExchangeNumParam);
            }

            List<OperateStockDto> operateStockDtoList = new ArrayList<>(skuSet);
            boolean goodsSuccess = remoteGoodsService.batchSubtractStock(operateStockDtoList);
            if(!goodsSuccess){
                OrderFailMessage failMessage = new OrderFailMessage().stockFail(dto.getCouponId(),curUser.getUserId(), curUser.getShopId(),TenantContextHolder.getTenantId());
                sender.sendCreateOrderFailMessage(failMessage);
                orderFailed.setNxPx(orderId.toString(), "库存扣除失败", TimeConstants.ONE_DAY);
            }
            Boolean b = remoteGoodsService.batchIntegralProductSubtractExchangeNum(operateExchangeNumParamList);
            if(!b){
                OrderFailMessage failMessage = new OrderFailMessage().stockFail(dto.getCouponId(),curUser.getUserId(), curUser.getShopId(),TenantContextHolder.getTenantId());
                sender.sendCreateOrderFailMessage(failMessage);
                orderFailed.setNxPx(orderId.toString(), "商品可兑换数扣除失败", TimeConstants.ONE_DAY);
            }
            //创建订单
            Order order = new Order();
            order.setId(orderId);
            order.setUserId(curUser.getUserId());
            order.setUserName(curUser.getNikeName());
            if(StringUtil.isNotEmpty(dto.getWarehouseId())){
                order.setWarehouseId(Long.valueOf(dto.getWarehouseId()));
            }
            order.setUserAvatarUrl(curUser.getAvatarUrl());
            order.setUserNote(dto.getUserNote());
            order.setType(dto.getOrderType());
            order.setTotalAmount(allAmount.add(freightAmount));
            order.setPromotionAmount(promotionAmount);
            //应付金额（实际支付金额）=订单总金额-促销优化金额+运费
            BigDecimal payAmount = NumberUtil.sub(allAmount, promotionAmount);
            if (payAmount.compareTo(BigDecimal.ZERO) == -1) {
                payAmount = BigDecimal.ZERO.add(freightAmount);
            } else {
                payAmount = payAmount.add(freightAmount);
            }
            //实际金额=应付金额-退款金额
            order.setDiscountsAmount(payAmount.setScale(2, BigDecimal.ROUND_DOWN));
            order.setPayAmount(payAmount.setScale(2, BigDecimal.ROUND_DOWN));
            order.setFreightAmount(freightAmount);
            order.setPayType(dto.getPayType());
            order.setSourceType(dto.getSourceType());
            order.setStatus(OrderStatusEnum.WAIT_FOR_PAY);
            order.setCustomForm(dto.getCustomForm());
            order.setAllIntegral(allIntegral);
            //创建订单收货
            installOrderDelivery(dto, orderId, accountAddress,curUser, order.getShopId());
            orderMapper.insert(order);
            for (OrderItem orderItem : orderItemList) {
                orderItemMapper.insert(orderItem);
            }
            /**
             * 支付金额为0
             */
            if(order.getPayAmount().compareTo(new BigDecimal(0))<=0){
                paymentNotify(order.getId());
            }
            done = true;
            // 订单自动取消
            BaseOrderMessage baseOrderMessage = new BaseOrderMessage();
            baseOrderMessage.setOrderId(order.getId());
            baseOrderMessage.setTenantId(order.getTenantId());
            baseOrderMessage.setShopId(order.getShopId());
            sender.sendAutoCancelOrderMessage(baseOrderMessage, getExTime(order.getType()));
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            orderFailed.setNxPx(orderId.toString(), e.getMessage(), TimeConstants.ONE_DAY);
        }
        return done;
    }

    private void settingRealAmount(List<OrderItem> orderItemList) {
        for (OrderItem orderItem : orderItemList) {
            log.info("设置实际付款之前的数据：" + JSONUtil.toJsonStr(orderItem));
            BigDecimal realAmount = NumberUtil.sub(orderItem.getRealAmount(), orderItem.getCouponAmount(),
                    orderItem.getPromotionAmount());
            orderItem.setRealAmount(realAmount.setScale(2, RoundingMode.HALF_UP));
            if (NumberUtil.isLessOrEqual(orderItem.getRealAmount(), BigDecimal.ZERO)) {
                orderItem.setRealAmount(BigDecimal.ZERO);
            }
            log.info("设置实际付款之后的数据：" + JSONUtil.toJsonStr(orderItem));
        }
    }

    private void installOrderDelivery(CreateOrderDto dto, Long orderId,
                                      MiniAccountAddress accountAddress,CurUserDto curUser, String shopId) {
        OrderDelivery orderDelivery = new OrderDelivery();
        orderDelivery.setOrderId(orderId);
        orderDelivery.setDeliveryType(dto.getDeliverType());
        orderDelivery.setDeliveryTemplateId(dto.getDeliveryTemplateId());
        orderDelivery.setReceived(false);
        orderDelivery.setShopId(shopId);
        //门店id不为空
        if(StringUtil.isNotEmpty(dto.getStoreFrontId())){
            orderDelivery.setStoreFrontId(dto.getStoreFrontId());
        }
        if(StringUtil.isNotEmpty(dto.getWarehouseId())){
            orderDelivery.setWarehouseId(Long.valueOf(dto.getWarehouseId()));
        }
        if (ObjectUtil.isNotNull(accountAddress)) {
            orderDelivery.setReceiverName(accountAddress.getUserName());
            orderDelivery.setReceiverPhone(accountAddress.getPhone());
            orderDelivery.setReceiverPostCode(accountAddress.getPostCode());
            orderDelivery.setReceiverProvince(accountAddress.getProvince());
            orderDelivery.setReceiverCity(accountAddress.getCity());
            orderDelivery.setReceiverRegion(accountAddress.getCounty());
            orderDelivery.setReceiverDetailAddress(accountAddress.getDetailInfo());
        }else{
            //因为小程序下单自提收货地址可以为空，因为原库数据库字段收货人，收货电话电话为必填，所以去用户名和用户手机号
            AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(curUser.getUserId(), Arrays.asList(1,2,
                    3, 5));
            MiniAccount miniAccount = accountInfoDto.getMiniAccountunt();
            orderDelivery.setReceiverName(miniAccount.getNikeName());
            orderDelivery.setReceiverPhone(miniAccount.getPhone());
        }
        orderDeliveryMapper.insert(orderDelivery);
    }

    private List<OrderItem> getTicketOrderItemList(List<TicketVo> ticketVos, Long orderId, List<TicketItemDto> itemDtoList) {
        List<OrderItem> itemList = new LinkedList<>();
        for (TicketVo ticketVo : ticketVos) {
            OrderItem orderItem = new OrderItem();
            orderItem.setOrderId(orderId);
            orderItem.setProductId(ticketVo.getProductId());
            orderItem.setProductSkuId(ticketVo.getProductId());
            orderItem.setShopTicketId(ticketVo.getProductId() + "");
            orderItem.setProductPic(ticketVo.getProductPic());
            orderItem.setProductName(ticketVo.getProductName());
            orderItem.setProductPrice(ticketVo.getProductPrice());
            orderItem.setProductQuantity(ticketVo.getProductQuantity());
            orderItem.setPromotionAmount(BigDecimal.ZERO);
            orderItem.setCouponAmount(BigDecimal.ZERO);
            orderItem.setProductOriginalPrice(BigDecimal.ZERO);
            if(ticketVo.getTicketType()== PromotionTypeEnum.SUBTRACT.getType()){//满减
                String specs = "满"+ticketVo.getFullAmount().stripTrailingZeros().toPlainString()+"减"+ticketVo.getPromotion().stripTrailingZeros().toPlainString();
                orderItem.setSpecs(specs);
            }
            if(ticketVo.getTicketType()== PromotionTypeEnum.DISCOUNT.getType()){//满减
                String specs = "满"+ticketVo.getFullAmount().stripTrailingZeros().toPlainString()+"打"+ticketVo.getPromotion().divide(new BigDecimal(10)).stripTrailingZeros().toPlainString()+"折";
                orderItem.setSpecs(specs);
            }
            //该商品经过优惠后的最终金额
            orderItem.setRealAmount(orderItem.getProductPrice().multiply(BigDecimal.valueOf(orderItem.getProductQuantity())));
            itemList.add(orderItem);
        }
        itemList.sort(Comparator.comparing(OrderItem::getRealAmount).reversed());
        log.info("return orderItem {}", JSONUtil.toJsonStr(itemList));
        return itemList;
    }

    /**
     * 组装订单详情表
     *
     * @param itemVoList
     * @param orderId
     * @return
     */
    private List<OrderItem> getOrderItemList(List<ItemVo> itemVoList, Long orderId, Long orderGroupId, String shopId) {
        List<OrderItem> itemList = new LinkedList<>();
        for (ItemVo itemVo : itemVoList) {
            OrderItem orderItem = new OrderItem();
            orderItem.setOrderId(orderId);
            orderItem.setOrderGroupId(orderGroupId);
            orderItem.setMemberTypeId(itemVo.getMemberTypeId());
            orderItem.setShopId(shopId);
            orderItem.setGiftFlag(itemVo.getGiftFlag());
            if(itemVo.getBuyType()!=null){
                orderItem.setBuyType(itemVo.getBuyType());
            }
            orderItem.setPriceType(itemVo.getPriceType());
            orderItem.setProductId(itemVo.getProductId());
            if (StrUtil.isNotBlank(itemVo.getProductSkuPic())) {
                orderItem.setProductPic(itemVo.getProductSkuPic());
            } else {
                orderItem.setProductPic(itemVo.getProductPic());
            }
            orderItem.setProductName(itemVo.getProductName());
            orderItem.setProductSn(itemVo.getProductSn());
            orderItem.setProductPrice(itemVo.getProductPrice());
            orderItem.setProductOriginalPrice(itemVo.getProductOriginalPrice());
            orderItem.setProductQuantity(itemVo.getProductQuantity());
            orderItem.setUnDeliveryQuantity(itemVo.getProductQuantity());
            orderItem.setProductSkuId(itemVo.getProductSkuId());
            orderItem.setProductSkuCode(itemVo.getProductSkuCode());
            orderItem.setPromotionAmount(itemVo.getPromotionAmount());
            orderItem.setCouponAmount(itemVo.getYouhuiPrice());
            orderItem.setMiniAccountCouponId(itemVo.getMiniAccountCouponId());
            //该商品经过优惠后的最终金额
            BigDecimal couponAmount = itemVo.getYouhuiPrice() == null ? BigDecimal.ZERO : itemVo.getYouhuiPrice();
            BigDecimal promotionAmount = itemVo.getPromotionAmount() == null ? BigDecimal.ZERO : itemVo.getPromotionAmount();
            BigDecimal realAmount = itemVo.getAmount().subtract(couponAmount).subtract(promotionAmount).compareTo(BigDecimal.ZERO) > 0 ?
                    itemVo.getAmount().subtract(couponAmount).subtract(promotionAmount) : BigDecimal.ZERO;
            orderItem.setRealAmount(realAmount);
            // 记录满减满赠活动id
            if(null != itemVo.getFullDonationId()){
                orderItem.setActivityType(ActivityTypeEnum.FULL_DONATION.getCode());
                orderItem.setActivityId(itemVo.getFullDonationId());
                orderItem.setActivityRuleId(itemVo.getShopFullDonationRuleVo().getId());
            }
            orderItem.setSpecs(itemVo.getSpecs());
            orderItem.setProviderId(itemVo.getProviderId());
            orderItem.setSaleMode(itemVo.getSaleMode());
            orderItem.setSpecs2(itemVo.getSpecs2());
            orderItem.setLinkProductId(itemVo.getLinkProductId());
            orderItem.setLinkSkuId(itemVo.getLinkSkuId());
            itemList.add(orderItem);
        }
        itemList.sort(Comparator.comparing(OrderItem::getRealAmount).reversed());
        log.info("创建订单订单明细 orderItem {}", JSONUtil.toJsonStr(itemList));
        return itemList;
    }

    /**
     * 组装积分商品订单详情
     * @param itemVoList
     * @param orderId
     * @param itemDtoList
     * @return
     */
    private List<OrderItem> getIntegralOrderItemList(List<ItemVo> itemVoList, Long orderId, List<ItemDto> itemDtoList) {
        List<OrderItem> itemList = new LinkedList<>();
        for (ItemVo itemVo : itemVoList) {
            OrderItem orderItem = new OrderItem();
            orderItem.setOrderId(orderId);
            orderItem.setProductId(itemVo.getProductId());
            orderItem.setIntegralProductId(itemVo.getIntegralProductId());
            if (StrUtil.isNotBlank(itemVo.getProductSkuPic())) {
                orderItem.setProductPic(itemVo.getProductSkuPic());
            } else {
                orderItem.setProductPic(itemVo.getProductPic());
            }
            orderItem.setProductName(itemVo.getProductName());
            orderItem.setProductSn(itemVo.getProductSn());
            orderItem.setProductPrice(itemVo.getAmount());
            orderItem.setProductOriginalPrice(itemVo.getAmount());
            orderItem.setProductQuantity(itemVo.getProductQuantity());
            orderItem.setProductSkuId(itemVo.getProductSkuId());
            orderItem.setProductSkuCode(itemVo.getProductSkuCode());
            orderItem.setPromotionAmount(BigDecimal.ZERO);
            orderItem.setCouponAmount(BigDecimal.ZERO);
            //该商品经过优惠后的最终金额
            orderItem.setRealAmount(orderItem.getProductPrice().multiply(BigDecimal.valueOf(itemVo.getProductQuantity())));
            orderItem.setSpecs(itemVo.getSpecs());
            orderItem.setProviderId(itemVo.getProviderId());
            orderItem.setSaleMode(itemVo.getSaleMode());
            orderItem.setIntegral(itemVo.getIntegral());
            orderItem.setSpecs2(itemVo.getSpecs2());
            orderItem.setLinkProductId(itemVo.getLinkProductId());
            orderItem.setLinkSkuId(itemVo.getLinkSkuId());
            itemList.add(orderItem);
        }
        itemList.sort(Comparator.comparing(OrderItem::getRealAmount).reversed());
        log.info("return orderItem {}", JSONUtil.toJsonStr(itemList));
        return itemList;
    }


    /**
     * 根据订单类型获取订单过期时间
     *
     * @param type
     * @return long
     * <AUTHOR>
     * @date 2020/7/26 14:39
     */
    private long getExTime(OrderTypeEnum type) {
        OrderSetting orderSetting = orderSettingMapper.selectOne(null);
        if (type.equals(OrderTypeEnum.SEC_KILL)) {
            return TimeConstants.ONE_MINUTES * orderSetting.getFlashOrderOvertime();
        } else {
            return TimeConstants.ONE_MINUTES * orderSetting.getNormalOrderOvertime();
        }
    }


    /**
     * 取消订单
     *
     * @param orderId
     * @return void
     * <AUTHOR>
     * @date 2020/7/26 14:39
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelOrder(Long orderId) {
        Order order = baseMapper.selectById(orderId);
        if (ObjectUtil.isNull(order)) {
            throw new ServiceException(SystemCode.DATA_NOT_EXIST);
        }
        if (!order.getUserId().equals(CurUserUtil.getHttpCurUser().getUserId())) {
            throw new ServiceException("您无此操作的权限");
        }
        if (!OrderStatusEnum.canCancel(order.getStatus())) {
            throw new ServiceException(OrderCode.ABNORMAL_DATA_STATUS);
        }
        cancelOrder(orderId, order, OrderStatusEnum.BUYER_CANCEL_CLOSE);
    }


    /**
     * 过期自动取消订单
     *
     * @param orderId
     * @return void
     * <AUTHOR>
     * @date 2020/7/26 14:39
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoCancelOrder(Long orderId) {
        Order order = baseMapper.selectById(orderId);
        if (ObjectUtil.isNull(order)) {
            return;
        }
        if (!OrderStatusEnum.canCancel(order.getStatus())) {
            return;
        }
        cancelOrder(orderId, order, OrderStatusEnum.BUYER_PAY_TIMEOUT_CLOSE);
    }

    /**
     * Cancel order.
     *
     * @param orderId the order id
     * @param order   the order
     * @param status  the status
     */
    public void cancelOrder(Long orderId, Order order, OrderStatusEnum status) {

        //Todo 归还优惠券
        /*BigDecimal youhuiPrice = order.getYouhuiPrice();
        if(youhuiPrice.compareTo(BigDecimal.ZERO)>0){
            String userId = order.getUserId();
            AccountCouponDto accountCouponDto = new AccountCouponDto();
            //accountCouponDto.setOrderId(orderId);
            accountCouponDto.setOrderGroupId(order.getOrderGroupId());
            accountCouponDto.setUserId(userId);
            remoteMiniAccountService.updateAccountCouponNo(accountCouponDto);
        }*/
        //优惠券要回退
        Long accountCouponId = order.getCouponId();
        if(accountCouponId != null){
            // 查询在订单中是否有使用，如果没有则回退
            LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.notIn(Order::getStatus, OrderStatusEnum.getInvalidStatus())
                    .eq(Order::getCouponId, accountCouponId);
            List<Order> orders = this.orderMapper.selectList(queryWrapper);
            if(null == orders || orders.isEmpty()){
                AccountCouponDto accountCouponDto = new AccountCouponDto();
                accountCouponDto.setAccountCouponId(accountCouponId);
                //accountCouponDto.setOrderGroupId(order.getOrderGroupId());
                accountCouponDto.setUserId(order.getUserId());
                remoteMiniAccountService.updateAccountCouponNo(accountCouponDto);
            }
        }
        //Todo 归还积分
        order.setStatus(status);
        order.setCloseTime(LocalDateTime.now());
        List<OperateStockDto> operateStockDtoList =
                orderItemMapper.selectItemDtoByOrderIds(Collections.singletonList(orderId));
        log.info("归还库存参数:{}", operateStockDtoList.toString());
        if(order.getType() == OrderTypeEnum.TICKET){
            // 通惠证订单
            try {
                operateStockDtoList.forEach(e -> {
                    remoteShopsService.passTicketAddStock(e.getSkuId());
                });
            }catch (Exception e){
                throw new ServiceException("库存归还失败，请稍后重试");
            }

        }else {

            //积分订单归还积分商品可兑换数
            if (order.getType() == OrderTypeEnum.INTEGRAL) {
                List<OrderItem> orderItems = orderItemMapper.selectByOrderId(orderId);
                if (orderItems != null && !orderItems.isEmpty()) {
                    List<OperateExchangeNumParam> list = new ArrayList<>();
                    for (OrderItem orderItem : orderItems) {
                        OperateExchangeNumParam operateExchangeNumParam = new OperateExchangeNumParam();
                        operateExchangeNumParam.setNumber(orderItem.getProductQuantity());
                        operateExchangeNumParam.setIntegralProductId(Long.valueOf(orderItem.getIntegralProductId()));
                        list.add(operateExchangeNumParam);
                    }
                    Boolean b = remoteGoodsService.batchIntegralProductAddExchangeNum(list);
                    if (!b) {
                        OrderFailMessage failMessage = new OrderFailMessage().stockFail(order.getCouponId()
                                , order.getUserId(), order.getShopId(), TenantContextHolder.getTenantId());
                        sender.sendCancelFailOrderMessage(failMessage);
                        throw new ServiceException("积分商品可兑换数归还失败，请稍后重试");
                    }
                }
            }
//            Boolean success = remoteGoodsService.batchRevertStock(operateStockDtoList);
//            if (!success) {
//                OrderFailMessage failMessage = new OrderFailMessage().stockFail(order.getCouponId()
//                        ,order.getUserId(), order.getShopId(),TenantContextHolder.getTenantId());
//                sender.sendCancelFailOrderMessage(failMessage);
//                throw new ServiceException("库存归还失败，请稍后重试");
//            }
            //改为发送队列
            String shopUserId = order.getUserId();
            AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(shopUserId, Arrays.asList(1, 2,
                    3, 5));
            LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OrderItem::getOrderId, orderId);
            List<OrderItem> orderItemList = orderItemMapper.selectList(wrapper);
            if (orderItemList != null && !orderItemList.isEmpty()) {
                BatchRevertStockMessage message = new BatchRevertStockMessage();
                List<BatchRevertStockDto> list = new ArrayList<>();
                for (OrderItem orderItem : orderItemList) {
                    BatchRevertStockDto batchRevertStockDto = new BatchRevertStockDto();
                    batchRevertStockDto.setNumber(orderItem.getProductQuantity());
                    batchRevertStockDto.setSkuId(orderItem.getProductSkuId());
                    list.add(batchRevertStockDto);
                }
                message.setList(list);
                message.setTenantId(order.getTenantId());
                sender.sendBatchRevertStockMessage(message);
            }
            //恢复用户消费余额
            BigDecimal commissionAmount = order.getCommissionAmount() == null ? BigDecimal.ZERO : order.getCommissionAmount();
            BigDecimal goldenAmount = order.getCommissionAmount() == null ? BigDecimal.ZERO : order.getCommissionAmount();
            if((commissionAmount.add(goldenAmount)).compareTo(BigDecimal.ZERO) > 0){
                AccountReturnBalanceMessage accountReturnBalanceMessage = new AccountReturnBalanceMessage();
                accountReturnBalanceMessage.setTenantId(TenantContextHolder.getTenantId());
                accountReturnBalanceMessage.setPayCommission(order.getCommissionAmount());
                accountReturnBalanceMessage.setPayGolden(order.getGoldenAmount());
                accountReturnBalanceMessage.setOrderId(order.getId() + "");
                accountReturnBalanceMessage.setUserId(accountInfoDto.getMiniAccountunt().getUserId());
                accountReturnBalanceMessage.setCancelOrder(CommonConstants.NUMBER_ONE);
                sender.sendRevertMiniAccountBalanceMessage(accountReturnBalanceMessage);
            }

        }
        baseMapper.updateById(order);
    }


    /**
     * payment order.  线下支付订单
     *
     * @param orderId the order id
     * @param order   the order
     * @param status  the status
     */
    public void paymentOrder(Long orderId, Order order, OrderStatusEnum status) {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        if(order.getType().getCode() == OrderTypeEnum.TICKET.getCode()){
            order.setStatus(OrderStatusEnum.WAIT_FOR_COMMENT);
            order.setPayTime(LocalDateTime.now());
            order.setPayType(PayTypeEnum.WECHAT);
            baseMapper.updateById(order);
            OrderDelivery orderDelivery = orderDeliveryMapper.selectByOrderId(order.getId());
            orderDelivery.setDeliveryTime(LocalDateTime.now());
            orderDelivery.setReceiveTime(LocalDateTime.now());
            orderDelivery.setReceived(true);
            orderDeliveryMapper.updateById(orderDelivery);
            OrderVo vo = baseMapper.selectOrderVoById(order.getId());
            sender.sendReceiptOrderMessage(vo);
        }else{
            order.setStatus(status);
            order.setPayTime(LocalDateTime.now());
            order.setPayType(PayTypeEnum.OFFLINE);
            RelationInfoVo relationInfoVo = remoteMiniInfoService.getRelationInfoByAccountId(curUserDto.getUserId());

            if(StringUtils.isNotEmpty(order.getStoreFrontId())
                    &&!order.getStoreFrontId().equals(relationInfoVo.getStoreFrontId())){
                throw new ServiceException("不是您门店订单，不允许线下付款");
            }

            order.setDepartmentId(relationInfoVo.getDepartmentId());//部门id
            order.setDepartmentCode(relationInfoVo.getDepartmentCode());//部门标识
            order.setDepartmentName(relationInfoVo.getDepartmentName());//部门名称
            order.setEmployeeId(relationInfoVo.getEmployeeId());//职员id
            order.setEmployeeOutId(relationInfoVo.getEmployeeOutId());//职员标识
            order.setEmployeeName(relationInfoVo.getEmployeeName());//职员名称
            order.setStoreFrontId(relationInfoVo.getStoreFrontId());//门店id
            order.setStoreFrontCode(relationInfoVo.getStoreFrontCode());//门店标识
            order.setStoreFrontName(relationInfoVo.getStoreFrontName());//门店名称
            order.setAccountId(relationInfoVo.getAccountId());//用户id
            order.setAccountName(relationInfoVo.getAccountName());//用户名称

            baseMapper.updateById(order);
            OrderVo vo = baseMapper.selectOrderVoById(order.getId());
            sender.sendPayedOrderMessage(vo);
            //提醒发货
            wxMpSendPayMessage(relationInfoVo,orderId,order);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PayResultDto payOrder(Long orderId, Boolean userBalance, HttpServletRequest request) {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        //查询会员持有的积分、收货地址
        AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(curUserDto.getUserId(), Arrays.asList(2,
                3));
        log.info("当前用户信息:" + curUserDto.toString());
        //检查账号
        checkAccount(accountInfoDto);

        ShopConfigDto shopConfig = remoteMiniInfoService.getShopConfig();
        PayResultDto payResultDto = new PayResultDto();
        if (StrUtil.isBlank(shopConfig.getPayInfo().getMchId())) {
            //throw new ServiceException("商户配置不存在");
            payResultDto.setReturnCode("FAIL");
            payResultDto.setReturnMsg("暂不支持线上付款功能！");
            return payResultDto;
        }
        PayInfoVo payInfo = shopConfig.getPayInfo();
        if (StrUtil.isBlank(payInfo.getMchId())) {
            //throw new ServiceException("支付配置不存在");
            payResultDto.setReturnCode("FAIL");
            payResultDto.setReturnMsg("暂不支持线上付款功能！");
            return payResultDto;
        }
        if (shopConfig == null) {
            throw new ServiceException("商户配置不存在");
        }
        if (payInfo == null) {
            throw new ServiceException("支付配置不存在");
        }


        boolean payFlag = true;
        Long id = IdWorker.getId();//支付id
        int orderPayType = 0;
        BigDecimal amount = BigDecimal.ZERO;
        OrderVo orderVo = baseMapper.selectOrderVoById(Long.valueOf(orderId));
        orderVo.setPayId(id);
        if (ObjectUtil.isNull(orderVo)) {
            throw new ServiceException(SystemCode.DATA_NOT_EXIST);
        }
        if (!OrderStatusEnum.canPay(orderVo.getStatus())) {
            throw new ServiceException("当前状态不能支付此订单");
        }
        if (NumberUtil.isLess(orderVo.getCashAmount(), OrderConstant.MIN_PAY_FEE)) {
            throw new ServiceException("支付金额有误");
        }
        log.info("进入支付订单===================>");
        //发送到期自动取消消息
        //BaseOrderMessage message =
        //        new BaseOrderMessage().setOrderId(Long.valueOf(orderId)).setTenantId(TenantContextHolder.getTenantId());
        //注释掉订单的消息队列
        //sender.sendAutoCancelOrderMessage(message, getExTime(orderVo.getType()));
        //log.info("超时取消订单成功===================>");
        orderPayType = orderVo.getPayType().getCode();
        amount = amount.add(orderVo.getCashAmount());
        orderVo.setExpireTime(orderVo.getCreateTime().plusSeconds(getExTime(orderVo.getType()) / 1000));
        updateById(orderVo);
        if (!orderVo.getPayType().equals(PayTypeEnum.WECHAT)&&!orderVo.getPayType().equals(PayTypeEnum.WECHAT_H5)) {
            payFlag = false;
        }

        if(!payFlag){
            payResultDto.setReturnCode("FAIL");
            payResultDto.setReturnMsg("下单成功");
            return payResultDto;
        }

        Integer payType = payInfo.getPayType();
        PayRequestDto dto = new PayRequestDto();
        dto.setPayChannel(payType);
        if (payType.equals(1)) {
            if (orderPayType==PayTypeEnum.WECHAT.getCode()) {
                dto.setTradeType(1);
            }
            if (orderPayType==PayTypeEnum.WECHAT_H5.getCode()) {
                dto.setTradeType(4);
            }
        } else if (payType.equals(2)) {
            dto.setTradeType(101);
        } else if (payType.equals(3)) {
            dto.setTradeType(201);
        } else if (payType.equals(4)) {
            dto.setTradeType(301);
        }
        dto.setOutTradeNo(String.valueOf(id));
        dto.setRouteKey(OrderQueueEnum.QUEUE_ORDER_PAYMENT_NOTIFY.getRouteKey());
        dto.setOpenId(curUserDto.getOpenId());
        dto.setTotalFee(amount);
        dto.setTerminalIp(IPUtils.getIpAddr(request));
        dto.setFeeType("CNY");
        if (orderPayType == OrderTypeEnum.SEC_KILL.getCode()) {
            dto.setTimeoutExpress("30m");
        } else {
            dto.setTimeoutExpress("6h");
        }
        dto.setBody(String.valueOf(id));
        dto.setSubject("");
        PayResultDto resultDto = remotePaymentService.payRequest(dto);
        if (ObjectUtil.isNull(resultDto)
                || ObjectUtil.isNull(resultDto.getWxResult())
                || ObjectUtil.isNull(resultDto.getWxResult().getTransactionId())) {
            log.error("支付创建失败,入参为: {}", dto.toString());
            log.error("支付创建失败,出参为: {}", resultDto == null ? "" : resultDto.toString());
            return resultDto;
        }
        orderVo.setTransactionId(resultDto.getWxResult().getTransactionId());
        this.updateById(orderVo);
        return resultDto;
    }

    /**
     *
     * @param orderGroupId   改为传输orderGroupId
     * @param userBalance
     * @param request     the request
     * @return
     */
    @Override
    public PayResultDto payOrderShops(String orderGroupId, Boolean userBalance, HttpServletRequest request) {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        //查询会员持有的积分、收货地址
        AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(curUserDto.getUserId(), Arrays.asList(2,
                3));
        log.info("当前用户信息:" + curUserDto.toString());
        //检查账号
        checkAccount(accountInfoDto);

        //List<String> orderIdList = Arrays.asList(orderGroupId.split(","));

        ShopConfigDto shopConfig = remoteMiniInfoService.getShopConfig();
        PayResultDto payResultDto = new PayResultDto();
        if (shopConfig == null) {
            throw new ServiceException("商户配置不存在");
        }
        if (StrUtil.isBlank(shopConfig.getPayInfo().getMchId())) {
            //throw new ServiceException("商户配置不存在");
            payResultDto.setReturnCode("FAIL");
            payResultDto.setReturnMsg("暂不支持线上付款功能！");
            return payResultDto;
        }
        PayInfoVo payInfo = shopConfig.getPayInfo();

        if (payInfo == null) {
            throw new ServiceException("支付配置不存在");
        }
        if (StrUtil.isBlank(payInfo.getMchId())) {
            //throw new ServiceException("支付配置不存在");
            payResultDto.setReturnCode("FAIL");
            payResultDto.setReturnMsg("暂不支持线上付款功能！");
            return payResultDto;
        }

        boolean payFlag = true;
        Long id = IdWorker.getId();//支付id
        int orderPayType = 0;
        BigDecimal amount = BigDecimal.ZERO;
        //通过orderGroupId查询订单
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Order::getOrderGroupId, orderGroupId);
        List<Order> orderList = baseMapper.selectList(wrapper);
        for (Order orderVo : orderList) {
            //OrderVo orderVo = baseMapper.selectOrderVoById(Long.valueOf(orderId));
            if (ObjectUtil.isNull(orderVo)) {
                throw new ServiceException(SystemCode.DATA_NOT_EXIST);
            }
            if (!OrderStatusEnum.canPay(orderVo.getStatus())) {
                throw new ServiceException("当前状态不能支付此订单");
            }
            if (NumberUtil.isLess(orderVo.getCashAmount(), BigDecimal.ZERO)) {
                throw new ServiceException("支付金额有误");
            }
            log.info("进入支付订单===================>");
            //发送到期自动取消消息
            //BaseOrderMessage message =
            //        new BaseOrderMessage().setOrderId(Long.valueOf(orderId)).setTenantId(TenantContextHolder.getTenantId());
            //注释掉订单的消息队列
            //sender.sendAutoCancelOrderMessage(message, getExTime(orderVo.getType()));
            //log.info("超时取消订单成功===================>");
            orderVo.setPayId(id);
            orderPayType = orderVo.getPayType().getCode();
            amount = amount.add(orderVo.getCashAmount());
            orderVo.setExpireTime(orderVo.getCreateTime().plusSeconds(getExTime(orderVo.getType()) / 1000));
            updateById(orderVo);
            if (!orderVo.getPayType().equals(PayTypeEnum.WECHAT)&&!orderVo.getPayType().equals(PayTypeEnum.WECHAT_H5)) {
                payFlag = false;
            }
        }
        if(!payFlag){
            payResultDto.setReturnCode("FAIL");
            payResultDto.setReturnMsg("下单成功");
            return payResultDto;
        }

        Integer payType = payInfo.getPayType();
        PayRequestDto dto = new PayRequestDto();
        dto.setPayChannel(payType);
        if (payType.equals(1)) {
            if (orderPayType==PayTypeEnum.WECHAT.getCode()) {
                dto.setTradeType(1);
            }
            if (orderPayType==PayTypeEnum.WECHAT_H5.getCode()) {
                dto.setTradeType(4);
            }
        } else if (payType.equals(2)) {
            dto.setTradeType(101);
        } else if (payType.equals(3)) {
            dto.setTradeType(201);
        } else if (payType.equals(4)) {
            dto.setTradeType(301);
        }
        dto.setOutTradeNo(String.valueOf(id));
        dto.setRouteKey(OrderQueueEnum.QUEUE_ORDER_PAYMENT_NOTIFY.getRouteKey());
        dto.setOpenId(curUserDto.getOpenId());
        dto.setTotalFee(amount);
        dto.setTerminalIp(IPUtils.getIpAddr(request));
        dto.setFeeType("CNY");
        if (orderPayType == OrderTypeEnum.SEC_KILL.getCode()) {
            dto.setTimeoutExpress("30m");
        } else {
            dto.setTimeoutExpress("6h");
        }
        dto.setBody(String.valueOf(id));
        dto.setSubject("");
        PayResultDto resultDto = remotePaymentService.payRequest(dto);
        if (ObjectUtil.isNull(resultDto)
                || ObjectUtil.isNull(resultDto.getWxResult())
                || ObjectUtil.isNull(resultDto.getWxResult().getTransactionId())) {
            log.error("支付创建失败,入参为: {}", dto.toString());
            log.error("支付创建失败,出参为: {}", resultDto == null ? "" : resultDto.toString());
            return resultDto;
        }
        for (Order orderVo : orderList) {
            //OrderVo orderVo = baseMapper.selectOrderVoById(Long.valueOf(orderId));
            orderVo.setTransactionId(resultDto.getWxResult().getTransactionId());
            this.updateById(orderVo);
        }
        return resultDto;
    }





    /**
     * 使用微信支付
     *
     * @param request
     * @param curUserDto
     * @param orderVo
     * @return com.medusa.gruul.payment.api.model.dto.PayResultDto
     * <AUTHOR>
     * @date 2020/8/12 21:15
     */
    private PayResultDto userWechat(HttpServletRequest request, CurUserDto curUserDto, OrderVo orderVo) {
        ShopConfigDto shopConfig = remoteMiniInfoService.getShopConfig();
        if (shopConfig == null) {
            throw new ServiceException("商户配置不存在");
        }
        PayInfoVo payInfo = shopConfig.getPayInfo();
        if (payInfo == null) {
            throw new ServiceException("支付配置不存在");
        }
        Integer payType = payInfo.getPayType();
        PayRequestDto dto = new PayRequestDto();
        dto.setPayChannel(payType);
        if (payType.equals(1)) {
            if (orderVo.getPayType().equals(PayTypeEnum.WECHAT)) {
                dto.setTradeType(1);
            }
            if (orderVo.getPayType().equals(PayTypeEnum.WECHAT_H5)) {
                dto.setTradeType(4);
            }
        } else if (payType.equals(2)) {
            dto.setTradeType(101);
        } else if (payType.equals(3)) {
            dto.setTradeType(201);
        } else if (payType.equals(4)) {
            dto.setTradeType(301);
        }
        dto.setOutTradeNo(orderVo.getId().toString());
        dto.setRouteKey(OrderQueueEnum.QUEUE_ORDER_PAYMENT_NOTIFY.getRouteKey());
        dto.setOpenId(curUserDto.getOpenId());
        dto.setTotalFee(orderVo.getPayAmount());
        dto.setTerminalIp(IPUtils.getIpAddr(request));
        dto.setFeeType("CNY");
        if (orderVo.getType() == OrderTypeEnum.SEC_KILL) {
            dto.setTimeoutExpress("30m");
        } else {
            dto.setTimeoutExpress("6h");
        }
        dto.setBody(orderVo.getId().toString());
        dto.setSubject("");
        PayResultDto resultDto = remotePaymentService.payRequest(dto);
        if (ObjectUtil.isNull(resultDto)
                || ObjectUtil.isNull(resultDto.getWxResult())
                || ObjectUtil.isNull(resultDto.getWxResult().getTransactionId())) {
            log.error("支付创建失败,入参为: {}", dto.toString());
            log.error("支付创建失败,出参为: {}", resultDto == null ? "" : resultDto.toString());

        }
        orderVo.setTransactionId(resultDto.getWxResult().getTransactionId());
        this.updateById(orderVo);
        return resultDto;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void paymentNotify(Long orderId) {
        List<OrderDataVo> list = baseMapper.getOrderDataVo(String.valueOf(orderId));
        for (OrderDataVo orderDataVo : list) {
            TenantContextHolder.setTenantId(orderDataVo.getTenantId());
            ShopContextHolder.setShopId(orderDataVo.getShopId());
            Order order = this.getById(orderDataVo.getId());
            if(order.getType().getCode() == OrderTypeEnum.TICKET.getCode()){
                order.setStatus(OrderStatusEnum.WAIT_FOR_COMMENT);
                order.setPayTime(LocalDateTime.now());
                order.setPayType(PayTypeEnum.WECHAT);
                baseMapper.updateById(order);
                OrderDelivery orderDelivery = orderDeliveryMapper.selectByOrderId(order.getId());
                orderDelivery.setDeliveryTime(LocalDateTime.now());
                orderDelivery.setReceiveTime(LocalDateTime.now());
                orderDelivery.setReceived(true);
                orderDeliveryMapper.updateById(orderDelivery);
                OrderVo vo = baseMapper.selectOrderVoById(order.getId());
                sender.sendReceiptOrderMessage(vo);
            }else{
                order.setStatus(OrderStatusEnum.WAIT_FOR_SEND);
                order.setPayTime(LocalDateTime.now());
                //order.setPayType(PayTypeEnum.WECHAT);

                String storeFrontId = order.getStoreFrontId();
                RelationInfoVo relationInfoVo = new RelationInfoVo();
                //门店id不为空，为到店自提订单
                if(StringUtil.isNotEmpty(storeFrontId)){
                    //自提订单
                    relationInfoVo = remoteMiniInfoService.getRelationInfoByStoreFrontId(storeFrontId);
                    //如果是通过邀请码下单
                    if(StringUtils.isNotEmpty(order.getInviteUserId())){
                        RelationInfoVo userRelationInfo = remoteMiniInfoService.getRelationInfoByMiniAccountId(order.getInviteUserId());
                        //判断下单门店和邀请用户是否一个门店
                        if(StringUtils.isNotEmpty(userRelationInfo.getStoreFrontId())
                                &&userRelationInfo.getStoreFrontId().equals(storeFrontId)){
                            relationInfoVo = userRelationInfo;
                        }
                    }
                }else{
                    //邮寄订单
                    relationInfoVo = remoteMiniInfoService.getRelationInfoDefaultDepartment();
                }
                order.setDepartmentId(relationInfoVo.getDepartmentId());//部门id
                order.setDepartmentCode(relationInfoVo.getDepartmentCode());//部门标识
                order.setDepartmentName(relationInfoVo.getDepartmentName());//部门名称
                order.setEmployeeId(relationInfoVo.getEmployeeId());//职员id
                order.setEmployeeOutId(relationInfoVo.getEmployeeOutId());//职员标识
                order.setEmployeeName(relationInfoVo.getEmployeeName());//职员名称
                order.setStoreFrontId(relationInfoVo.getStoreFrontId());//门店id
                order.setStoreFrontCode(relationInfoVo.getStoreFrontCode());//门店标识
                order.setStoreFrontName(relationInfoVo.getStoreFrontName());//门店名称
                order.setAccountId(relationInfoVo.getAccountId());//用户id
                order.setAccountName(relationInfoVo.getAccountName());//用户名称

                baseMapper.updateById(order);
                OrderVo vo = baseMapper.selectOrderVoById(order.getId());
                sender.sendPayedOrderMessage(vo);
                //提醒发货
                wxMpSendPayMessage(relationInfoVo,orderId,order);
                //极光消息推送
                String shopId = order.getShopId();
                String tenantId = order.getTenantId();
                TenantContextHolder.setTenantId(tenantId);
                ShopContextHolder.setShopId(shopId);



                //android
                Result<List<AccountInfo>> result = remoteMiniInfoService.getAccountRegistrationId("android");
                if(result!=null&&result.getData()!=null){
                    List<String> androidList = new ArrayList<>();
                    List<AccountInfo> accountInfoList = result.getData();
                    if(accountInfoList!=null&&accountInfoList.size()>0){
                        for (AccountInfo accountInfo : accountInfoList) {
                            androidList.add(accountInfo.getRegistrationId());
                        }
                    }
                    if(androidList!=null&&androidList.size()>0){
                        String[] registrationIds = androidList.toArray(new String[0]);
                        String[] alias = {};
                        JPushUtils.jiguangSend_batch("android", "registrationId", "消息通知", order.getId()+"订单已付款，请发货！", alias, registrationIds, "data", "","");
                    }
                }
                //ios
                Result<List<AccountInfo>> result2 = remoteMiniInfoService.getAccountRegistrationId("ios");
                if(result2!=null&&result2.getData()!=null){
                    List<String> iosList = new ArrayList<>();
                    List<AccountInfo> accountInfoList = result2.getData();
                    if(accountInfoList!=null&&accountInfoList.size()>0){
                        for (AccountInfo accountInfo : accountInfoList) {
                            iosList.add(accountInfo.getRegistrationId());
                        }
                    }
                    if(iosList!=null&&iosList.size()>0){
                        String[] registrationIds = iosList.toArray(new String[0]);
                        String[] alias = {};
                        JPushUtils.jiguangSend_batch("ios", "registrationId", "消息通知", order.getId()+"订单已付款，请发货！", alias, registrationIds, "data", "","");
                    }
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receiptOrder(Long orderId, boolean isSystem) {
        Order order = baseMapper.selectById(orderId);
        log.info("确认收货收到的订单：{}", JSONObject.toJSONString(order));

        if(order.getWxDeliverStatusEnum()!=null&&order.getWxDeliverStatusEnum().getCode()== WxDeliverStatusEnum.DELIVER.getCode()){
            order.setWxDeliverStatusEnum(WxDeliverStatusEnum.RECEIVING);
        }
        OrderSetting orderSetting = orderSettingMapper.selectOne(null);
        if (ObjectUtil.isNull(order)) {
            throw new ServiceException(SystemCode.DATA_NOT_EXIST);
        }
        if (!isSystem) {
            if (!order.getUserId().equals(CurUserUtil.getHttpCurUser().getUserId())) {
                throw new ServiceException("您无此操作的权限");
            }
        }
        if (!OrderStatusEnum.canReceipt(order.getStatus())) {
            if(!isSystem){
                // 非系统消息或者非管理后台
                throw new ServiceException("当前状态不能确认此订单");
            }else{
                // 如果是队列消息，则判断订单状态为WAIT_FOR_SEND（待发货）、SHIPPED（配送中）则重新加入自动签收队列
                if(OrderStatusEnum.WAIT_FOR_SEND == order.getStatus() || OrderStatusEnum.SHIPPED == order.getStatus()){
                    log.info("重新加入自动签收队列确认收货收到的订单：{}", JSONObject.toJSONString(order));
                    BaseOrderMessage baseOrderMessage = new BaseOrderMessage();
                    baseOrderMessage.setOrderId(order.getId());
                    baseOrderMessage.setShopId(order.getShopId());
                    baseOrderMessage.setTenantId(order.getTenantId());
                    sender.sendAutoReceiptOrderMessage(baseOrderMessage,
                            orderSetting.getConfirmOvertime() * TimeConstants.ONE_DAY);
                }
                return;
            }
        }
        List<AfsOrder> afsOrderList = afsOrderMapper.selectProgressByOrderId(orderId);
        for (AfsOrder afsOrder : afsOrderList) {
            if (!afsOrder.getStatus().equals(AfsOrderStatusEnum.WAIT_FOR_BUSINESS_APPROVED) && !isSystem) {
                throw new ServiceException("当前订单有正在进行中的售后订单");
            }
        }
        order.setStatus(OrderStatusEnum.WAIT_FOR_COMMENT);
        baseMapper.updateById(order);

        LambdaQueryWrapper<OrderDelivery>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderDelivery::getOrderId, orderId);
        List<OrderDelivery> orderDeliveryList = orderDeliveryMapper.selectList(wrapper);
        if(orderDeliveryList!=null&&orderDeliveryList.size()>0){
            for (OrderDelivery orderDelivery : orderDeliveryList) {
                orderDelivery.setReceived(true);
                orderDelivery.setReceiveTime(LocalDateTime.now());
                orderDeliveryMapper.updateById(orderDelivery);
            }
        }

        //到期未评价自动完成订单
        BaseOrderMessage message =
                new BaseOrderMessage().setOrderId(orderId).setTenantId(order.getTenantId()).setShopId(order.getShopId());
        sender.sendAutoCompletedOrderMessage(message, orderSetting.getFinishOvertime() * TimeConstants.ONE_DAY);
        OrderVo vo = orderInfo(order.getId());
        sender.sendReceiptOrderMessage(vo);
    }

    @Override
    public Integer checkOrder(GroupOrderResultDto dto) {
        //先查询已经失败的订单里面有没有
        OrderFailedRedisKey orderFailed = new OrderFailedRedisKey();
        String failMsg = orderFailed.get(dto.getOrderId().toString());
        if (StrUtil.isNotBlank(failMsg)) {
            throw new ServiceException(failMsg);
        }
        //再查询库存是否已经售罄
        Collection<SkuStock> skuStockList = remoteGoodsService.findSkuStockListByIds(dto.getSkuIdSet());
        Map<Long, SkuStock> skuStockMap = null;
        if (CollUtil.isNotEmpty(skuStockList)) {
            skuStockMap = skuStockList.stream().collect(Collectors.toMap(SkuStock::getId, v -> v));
        }
        if(null == skuStockMap){
            log.error("{}  》 skuIds无数据库记录。", JSON.toJSONString(dto.getSkuIdSet()));
            return -1;
        }
        //获取订单设置，判断是否开启负库存下单
        OrderSetting orderSetting = orderSettingMapper.selectOne(null);
        for (ItemDto itemDto : dto.getItems()) {
            SkuStock skuStock = skuStockMap.get(itemDto.getSkuId());
            if (ObjectUtil.isNotNull(skuStock)) {
                boolean hasStock = skuStock.getStock().compareTo(new BigDecimal(itemDto.getNumber())) > -1;
                if (!orderSetting.getOpenNegativeOrder() && !hasStock) {
                    return -1;
                }
            } else {
                log.error("{}  》 单个skuId无数据库记录。", JSON.toJSONString(itemDto.getSkuId()));
                return -1;
            }
        }
        return 0;
    }

    @Override
    public Integer checkOrderShops(GroupOrderResultShopsDto dto) {
        String orderIds = dto.getOrderIds();
        String[] split = orderIds.split(",");
        OrderFailedRedisKey orderFailed = new OrderFailedRedisKey();
        for (String orderId : split) {
            //先查询已经失败的订单里面有没有
            String failMsg = orderFailed.get(orderId);
            if (StrUtil.isNotBlank(failMsg)) {
                throw new ServiceException(failMsg);
            }
        }
        List<ItemShopDto> itemShopDtoList = new ArrayList<>();
        List<ItemDto> itemDtoList = dto.getItems();
        for (ItemDto itemDto : itemDtoList) {
            Long skuId = itemDto.getSkuId();
            SkuStock skuStock = remoteGoodsService.findSkuStockById(skuId);
            itemDto.setShopId(skuStock.getShopId());
        }

        Map<String, List<ItemDto>> listMap = itemDtoList.stream().collect(Collectors.groupingBy(ItemDto::getShopId));
        for (Map.Entry<String, List<ItemDto>> entry : listMap.entrySet()) {
            ItemShopDto itemShopDto = new ItemShopDto();
            List<ItemDto> list = entry.getValue();
            String shopId = entry.getKey();
            itemShopDto.setItemDtos(list);
            itemShopDto.setShopId(shopId);
            itemShopDtoList.add(itemShopDto);
        }

        for (ItemShopDto itemShopDto : itemShopDtoList) {
            List<ItemDto> itemDtos = itemShopDto.getItemDtos();
            Set<Long> collect = itemDtos.stream().map(ItemDto::getSkuId).collect(Collectors.toSet());
            //再查询库存是否已经售罄
            Collection<SkuStock> skuStockList = remoteGoodsService.findSkuStockListByIds(collect);
            Map<Long, SkuStock> skuStockMap = null;
            if (CollUtil.isNotEmpty(skuStockList)) {
                skuStockMap = skuStockList.stream().collect(Collectors.toMap(SkuStock::getId, v -> v));
            }
            if(null == skuStockMap){
                log.error("{}  》 skuIds无数据库记录。", JSON.toJSONString(collect));
                return -1;
            }
            //获取订单设置，判断是否开启负库存下单
            OrderSetting orderSetting = orderSettingMapper.selectOne(null);
            for (ItemDto itemDto : itemDtos) {
                SkuStock skuStock = skuStockMap.get(itemDto.getSkuId());
                if (ObjectUtil.isNotNull(skuStock)) {
                    boolean hasStock = skuStock.getStock().compareTo(new BigDecimal(itemDto.getNumber())) > -1;
                    if (!orderSetting.getOpenNegativeOrder() && !hasStock) {
                        return -1;
                    }
                } else {
                    log.error("{}  》 单个skuId无数据库记录。", JSON.toJSONString(itemDto.getSkuId()));
                    return -1;
                }
            }
        }
        return 0;
    }

    @Override
    public PageUtils<ApiOrderVo> searchOrder(ApiSearchOrderDto dto) {
        List<Integer> orderStatusList = new ArrayList<>(4);
        boolean searchAfterOrder = false;
        //订单状态 -1：所有订单, 0.待付款（待买家付款）, 1.待发货（买家已付款）, 2.配送中（卖家已发货）, 3.待提货（商家直配已到达提货点或物流订单已发货）, 4.已完成（用户已经签收）, 5.待评价, 6
        // .售后订单, 7.已关闭
        switch (dto.getOrderStatus()) {
            case -1:
                orderStatusList.clear();
                break;
            case 0:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_PAY.getCode());
                orderStatusList.add(OrderStatusEnum.APPROVED.getCode());
                break;
            case 1:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
                break;
            case 2:
                orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());
                break;
            case 3:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());
                break;
            case 4:
                orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());
                break;
            case 5:
                orderStatusList.add(OrderStatusEnum.WAIT_FOR_COMMENT.getCode());
                break;
            case 6:
                searchAfterOrder = true;
                break;
            case 7:
                orderStatusList.add(OrderStatusEnum.REFUNDED.getCode());
                orderStatusList.add(OrderStatusEnum.PART_REFUNDED.getCode());
                orderStatusList.add(OrderStatusEnum.BUYER_PAY_TIMEOUT_CLOSE.getCode());
                orderStatusList.add(OrderStatusEnum.BUYER_CANCEL_CLOSE.getCode());
                orderStatusList.add(OrderStatusEnum.SELLER_CANCEL_CLOSE.getCode());
                orderStatusList.add(OrderStatusEnum.EXCHANGE_SUCCESS_CLOSE.getCode());
                orderStatusList.add(OrderStatusEnum.EXCHANGE_CANCEL_CLOSE.getCode());
                break;
            default:
                break;
        }
        Page<ApiOrderVo> page = baseMapper.searchApiOrderVoPage(new Page(dto.getCurrent(), dto.getSize()),
                orderStatusList, searchAfterOrder, CurUserUtil.getHttpCurUser().getUserId(),dto.getUploadEffectStatus());

        OrderSetting orderSetting = orderSettingMapper.selectOne(null);

        for (ApiOrderVo record : page.getRecords()) {
            PayInfoVo payInfo = remoteMiniInfoService.getShopConfig().getPayInfo();
            record.setUploadEffectFlag(CommonConstants.NUMBER_ZERO);
            if(payInfo!=null){
                record.setMchId(payInfo.getMchId());
            }
            if (ObjectUtil.isNull(record.getExpireTime())) {
                record.setExpireTime(record.getCreateTime().plusSeconds(getExTime(record.getType()) / 1000));
            }
            if (record.getType().equals(OrderTypeEnum.EXCHANGE)) {
                Long originalOrderId = afsOrderMapper.selectOriginalOrderIdByOrderId(record.getOrderId());
                record.setOriginalOrderId(originalOrderId);
            }

            if(StringUtils.isNotEmpty(record.getOrderUserId())
                    &&StringUtils.isNotEmpty(record.getUserId())
                    &&!record.getOrderUserId().equals(record.getUserId())){
                record.setOtherOrder(CommonConstants.NUMBER_ONE);
            }else{
                record.setOtherOrder(CommonConstants.NUMBER_ZERO);
            }

            LambdaQueryWrapper<OrderEvaluate>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OrderEvaluate::getOrderId,record.getOrderId());
            List<OrderEvaluate> orderEvaluateList = orderEvaluateMapper.selectList(wrapper);
            if(orderEvaluateList!=null&&orderEvaluateList.size()>0){
                OrderEvaluate orderEvaluate = orderEvaluateList.get(0);
                String effectImageUrl = orderEvaluate.getEffectImageUrl();
                if(StringUtils.isNotEmpty(effectImageUrl)){
                    String[] split = effectImageUrl.split(",");
                    if(split.length < orderSetting.getUploadPicNum()){
                        record.setUploadEffectFlag(CommonConstants.NUMBER_ONE);
                    }
                }
            }


        }
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void evaluateOrder(ApiOrderEvaluateDto dto) {
        OrderVo orderVo = baseMapper.selectOrderVoById(dto.getOrderId());
        if (ObjectUtil.isNull(orderVo)) {
            throw new ServiceException(SystemCode.DATA_NOT_EXIST);
        }
        if (!orderVo.getUserId().equals(CurUserUtil.getHttpCurUser().getUserId())) {
            throw new ServiceException("您无此操作的权限");
        }
        if (!OrderStatusEnum.canEvaluate(orderVo.getStatus())) {
            throw new ServiceException("当前状态不能评价此订单");
        }
        AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(CurUserUtil.getHttpCurUser().getUserId()
                , Arrays.asList(2, 3));
        if (CollUtil.isNotEmpty(accountInfoDto.getRestrictTypes()) && accountInfoDto.getRestrictTypes().contains(BlacklistEnum.REJECT_COMMENT.getType())) {
            throw new ServiceException("账号异常请联系客服");
        }
        String defaultComment = "此用户没有填写评价";
        OrderEvaluate orderEvaluate = new OrderEvaluate();
        orderEvaluate.setOrderId(orderVo.getId());
        orderEvaluate.setUserId(orderVo.getUserId());
        orderEvaluate.setUserName(orderVo.getUserName());
        orderEvaluate.setUserAvatarUrl(orderVo.getUserAvatarUrl());
        orderEvaluate.setShopRate(dto.getShopRate());
        orderEvaluate.setShopId(orderVo.getShopId());
        orderEvaluateMapper.insert(orderEvaluate);

        Map<String, ApiOrderProductEvaluateDto> productEvaluateMap =
                dto.getProductEvaluateDtoList().stream().collect(Collectors.toMap(t -> t.getProductSkuId() + ":" + t.getPriceType(), ItemVo -> ItemVo));
        List<Long> productIdList = new ArrayList<>();
        for (OrderItem orderItem : orderVo.getOrderItemList()) {
            ApiOrderProductEvaluateDto productEvaluateDto = productEvaluateMap.get(orderItem.getProductSkuId()+":"+orderItem.getPriceType());
            OrderProductEvaluate productEvaluate = new OrderProductEvaluate();
            productEvaluate.setOrderId(orderVo.getId());
            productEvaluate.setProductId(orderItem.getProductId());
            productEvaluate.setProductSkuId(orderItem.getProductSkuId());
            productEvaluate.setProductPic(orderItem.getProductPic());
            productEvaluate.setProductQuantity(orderItem.getProductQuantity());
            productEvaluate.setProductPrice(orderItem.getProductPrice());
            productEvaluate.setProductName(orderItem.getProductName());
            productEvaluate.setSpecs(orderItem.getSpecs());
            productEvaluate.setShopId(orderVo.getShopId());
            if (StrUtil.isNotBlank(productEvaluateDto.getComment())) {
                productEvaluate.setComment(productEvaluateDto.getComment());
            } else {
                productEvaluate.setComment(defaultComment);
            }
            productEvaluate.setPicture(productEvaluateDto.getPicture());
            productEvaluate.setRate(productEvaluateDto.getRate());
            productEvaluateMapper.insert(productEvaluate);
            productIdList.add(productEvaluate.getProductId());
        }
        //订单完成评价消息订阅
        if(CollectionUtil.isNotEmpty(productIdList)){
            OrderEvaluatedMessage message = new OrderEvaluatedMessage();
            message.setProductIdList(productIdList);
            message.setTenantId(TenantContextHolder.getTenantId());
            sender.sendOrderEvaluateMessage(message);
        }

        completeOrder(orderVo.getId());
    }

    @Override
    public void evaluateOrder(Long orderId) {
        OrderVo orderVo = baseMapper.selectOrderVoById(orderId);
        if(null == orderVo){
            return;
        }
        OrderEvaluate orderEvaluate =
                orderEvaluateMapper.selectOne(new LambdaQueryWrapper<OrderEvaluate>().eq(OrderEvaluate::getOrderId,
                        orderVo.getId()));
        if (ObjectUtil.isNull(orderEvaluate)) {
            String defaultComment = "系统自动好评";
            orderEvaluate = new OrderEvaluate();
            orderEvaluate.setOrderId(orderVo.getId());
            orderEvaluate.setUserId(orderVo.getUserId());
            orderEvaluate.setUserName(orderVo.getUserName());
            orderEvaluate.setUserAvatarUrl(orderVo.getUserAvatarUrl());
            orderEvaluate.setShopRate(5);
            orderEvaluateMapper.insert(orderEvaluate);
            List<Long> productIdList = new ArrayList<>();
            for (OrderItem orderItem : orderVo.getOrderItemList()) {
                OrderProductEvaluate productEvaluate = new OrderProductEvaluate();
                productEvaluate.setOrderId(orderVo.getId());
                productEvaluate.setProductId(orderItem.getProductId());
                productEvaluate.setProductSkuId(orderItem.getProductSkuId());
                productEvaluate.setProductPic(orderItem.getProductPic());
                productEvaluate.setProductQuantity(orderItem.getProductQuantity());
                productEvaluate.setProductPrice(orderItem.getProductPrice());
                productEvaluate.setProductName(orderItem.getProductName());
                productEvaluate.setSpecs(orderItem.getSpecs());
                productEvaluate.setComment(defaultComment);
                productEvaluate.setRate(5);
                productEvaluateMapper.insert(productEvaluate);
                productIdList.add(productEvaluate.getProductId());
            }
            //订单完成评价消息订阅
            if(CollectionUtil.isNotEmpty(productIdList)){
                OrderEvaluatedMessage message = new OrderEvaluatedMessage();
                message.setProductIdList(productIdList);
                message.setTenantId(TenantContextHolder.getTenantId());
                sender.sendOrderEvaluateMessage(message);
            }
            completeOrder(orderVo.getId());
        }
    }

    @Override
    public OrderVo orderInfo(Long orderId) {
        OrderVo vo = baseMapper.selectOrderVoById(orderId);


        List<OrderItemVo> orderItemList = vo.getOrderItemList();
        if(orderItemList!=null&&orderItemList.size()>0){
            for (OrderItemVo orderItemVo : orderItemList) {
                AfsOrder afsOrder = orderItemVo.getAfs();

                if(afsOrder!=null){
                    if(afsOrder.getStatus()!=null
                            &&afsOrder.getStatus() == AfsOrderStatusEnum.CLOSE
                            &&afsOrder.getCloseType() == AfsOrderCloseTypeEnum.USER_CANCEL){
                        //售后单已撤销
                        //待发货数量大于0，可以申请退款
                        if(orderItemVo.getUnDeliveryQuantity()>0){
                            orderItemVo.setAfsOrderFlag(CommonConstants.NUMBER_ONE);
                        }else{
                            //不能申请退款
                            orderItemVo.setAfsOrderFlag(CommonConstants.NUMBER_ZERO);
                        }
                    }else{
                        //售后单未撤销
                        //查看售后详情
                        orderItemVo.setAfsOrderFlag(CommonConstants.NUMBER_TWO);
                    }
                }else{
                    //没有售后单
                    //待发货数量大于0，可以申请退款
                    if(orderItemVo.getUnDeliveryQuantity()>0){
                        orderItemVo.setAfsOrderFlag(CommonConstants.NUMBER_ONE);
                    }else{
                        //不能申请退款
                        orderItemVo.setAfsOrderFlag(CommonConstants.NUMBER_ZERO);
                    }
                }
            }
        }

        if (vo.getStatus().equals(OrderStatusEnum.WAIT_FOR_PAY) && ObjectUtil.isNull(vo.getExpireTime())) {
            vo.setExpireTime(vo.getCreateTime().plusSeconds(getExTime(vo.getType()) / 1000));
        }
        PayInfoVo payInfo = remoteMiniInfoService.getShopConfig().getPayInfo();
        if(payInfo!=null){
            vo.setMchId(payInfo.getMchId());
        }
        vo.setProductTotalQuantity(vo.getOrderItemList().stream().mapToInt(OrderItem::getProductQuantity).sum());
        if(vo.getWarehouseId()!=null){
            Warehouse warehouse = remoteGoodsService.getWarehouseById(vo.getWarehouseId());
            if(warehouse!=null){
                vo.setWarehouseFullName(warehouse.getWarehouseFullName());
                vo.setWarehouseAddress(warehouse.getWarehouseAddress());
            }
        }

        if(StringUtils.isNotEmpty(vo.getOrderUserId())
                &&StringUtils.isNotEmpty(vo.getUserId())
                &&!vo.getOrderUserId().equals(vo.getUserId())){
            vo.setOtherOrder(CommonConstants.NUMBER_ONE);
        }else{
            vo.setOtherOrder(CommonConstants.NUMBER_ZERO);
        }

        List<ManageOrderDeliveryVo> orderDeliveryList = vo.getOrderDeliveryList();

        if(orderDeliveryList!=null&&orderDeliveryList.size()>0){
            for (ManageOrderDeliveryVo manageOrderDeliveryVo : orderDeliveryList) {
                if(manageOrderDeliveryVo!=null&&StringUtil.isNotEmpty(manageOrderDeliveryVo.getStoreFrontId())){
                    StoreFrontOrderVo storeFrontOrderVo = remoteMiniInfoService.getPlatformDepartmentByStoreFrontId(manageOrderDeliveryVo.getStoreFrontId());
                    if(storeFrontOrderVo!=null){
                        manageOrderDeliveryVo.setSelfName(storeFrontOrderVo.getEmployeeName());
                        manageOrderDeliveryVo.setSelfDetailAddress(storeFrontOrderVo.getStoreFrontAddress());
                        manageOrderDeliveryVo.setStoreFrontName(storeFrontOrderVo.getStoreFrontName());
                    }else{
                        manageOrderDeliveryVo.setSelfName("");
                        manageOrderDeliveryVo.setSelfDetailAddress("");
                        manageOrderDeliveryVo.setStoreFrontName("");
                    }
                }else{
                    manageOrderDeliveryVo.setSelfName("");
                    manageOrderDeliveryVo.setSelfDetailAddress(manageOrderDeliveryVo.getSelfDetailAddress());
                    manageOrderDeliveryVo.setStoreFrontName("");
                }
            }
        }
        //删除默认的发货方式
        if(orderDeliveryList.size()>0){
            orderDeliveryList.remove(0);
        }

        List<DeliverTypeEnum>deliverTypeList = new ArrayList<>();
        for (ManageOrderDeliveryVo manageOrderDeliveryVo : orderDeliveryList) {
            DeliverTypeEnum deliveryType = manageOrderDeliveryVo.getDeliveryType();
            Long warehouseId = manageOrderDeliveryVo.getWarehouseId();
            if(warehouseId!=null){
                Warehouse warehouse = remoteGoodsService.getWarehouseById(warehouseId);
                if(warehouse!=null){
                    manageOrderDeliveryVo.setWarehouseFullName(warehouse.getWarehouseFullName());
                    manageOrderDeliveryVo.setWarehouseAddress(warehouse.getWarehouseAddress());
                }
            }
            AfsOrder afsOrder = manageOrderDeliveryVo.getAfs();
            if(afsOrder!=null){
                if(afsOrder.getStatus()!=null
                        &&afsOrder.getStatus() == AfsOrderStatusEnum.CLOSE
                        &&afsOrder.getCloseType() == AfsOrderCloseTypeEnum.USER_CANCEL){
                    //售后单已撤销
                    manageOrderDeliveryVo.setAfsOrderFlag(CommonConstants.NUMBER_ONE);
                }else{
                    //售后单未撤销
                    //查看售后详情
                    manageOrderDeliveryVo.setAfsOrderFlag(CommonConstants.NUMBER_TWO);
                }
            }else{
                //没有售后单
                manageOrderDeliveryVo.setAfsOrderFlag(CommonConstants.NUMBER_ONE);
            }
            deliverTypeList.add(deliveryType);
        }
        vo.setDeliverTypeList(deliverTypeList);


        vo.setOrderDeliveryList(orderDeliveryList);

        OrderSetting orderSetting = orderSettingMapper.selectOne(null);

        LambdaQueryWrapper<OrderEvaluate>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderEvaluate::getOrderId,vo.getId());
        List<OrderEvaluate> orderEvaluateList = orderEvaluateMapper.selectList(wrapper);
        if(orderEvaluateList!=null&&orderEvaluateList.size()>0){
            OrderEvaluate orderEvaluate = orderEvaluateList.get(0);
            String effectImageUrl = orderEvaluate.getEffectImageUrl();
            if(StringUtils.isNotEmpty(effectImageUrl)){
                String[] split = effectImageUrl.split(",");
                if(split.length < orderSetting.getUploadPicNum()){
                    vo.setUploadEffectFlag(CommonConstants.NUMBER_ONE);
                }
                List<String>effectImageUrlList = new ArrayList<>(Arrays.asList(split));
                vo.setEffectImgUrlList(effectImageUrlList);
            }
        }

        //判断付款金额是否等于订单总额
        vo.setAllAfsOrderFlag(CommonConstants.NUMBER_ZERO);
        List<AfsSimpleVo> afsSimpleVoList = remoteAfsService.getAfsOrderByReceiptBillId(vo.getId());
        //是否存在售后单
        Boolean allAfsFlag = false;
        if(afsSimpleVoList!=null&&afsSimpleVoList.size()>0){
            for (AfsSimpleVo afsSimpleVo : afsSimpleVoList) {
                if(afsSimpleVo.getCloseType()!=null){
                    if(!(afsSimpleVo.getCloseType().equals(AfsOrderCloseTypeEnum.USER_CANCEL)
                            &&afsSimpleVo.getStatus().equals(AfsOrderStatusEnum.CLOSE))){
                        allAfsFlag = true;
                    }
                }else{
                    allAfsFlag = true;
                }
            }
        }
        if(!allAfsFlag){
            BigDecimal totalAmount = vo.getTotalAmount();
            BigDecimal payAmount = vo.getPayAmount();
            List<SpecialSetting> specialSettingList = remoteMiniInfoService.getSpecialSetting();
            if(specialSettingList!=null&&specialSettingList.size()>0){
                SpecialSetting specialSetting = specialSettingList.get(0);
                Integer allAfsOrderFlag = specialSetting.getAllAfsOrderFlag();
                //整单退-启用特殊配置是否整单退并且订单金额不等于实际支付金额
                if(allAfsOrderFlag == CommonConstants.NUMBER_ONE && totalAmount.compareTo(payAmount)!=0){
                    vo.setAllAfsOrderFlag(CommonConstants.NUMBER_ONE);
                }
            }
            if(vo.getMallOrderType() == ProductTypeEnum.UPGRADE_PRODUCT.getStatus()){
                //激活订单必须整单退
                vo.setAllAfsOrderFlag(CommonConstants.NUMBER_ONE);
            }
        }
        return vo;
    }

    @Override
    public PageUtils searchOrderEvaluate(ApiSearchEvaluateDto dto) {
        Page<ManageEvaluateVo> page = orderEvaluateMapper.userSearchOrderEvaluate(new Page(dto.getCurrent(),
                dto.getSize()), CurUserUtil.getHttpCurUser().getUserId());
        return new PageUtils(page);
    }

    @Override
    public void completeOrder(Long orderId) {
        Order order = baseMapper.selectById(orderId);
        if (ObjectUtil.isNull(order)) {
            return;
        }
        if (!OrderStatusEnum.canCompleted(order.getStatus())) {
            return;
        }
        order.setStatus(OrderStatusEnum.COMPLETE);
        order.setCompleteTime(LocalDateTime.now());
        baseMapper.updateById(order);
        OrderVo orderVo = orderInfo(orderId);
        List<OrderItemVo> itemList = orderVo.getOrderItemList();

        //构建dto
        OrderPaidDto orderPaidDto = new OrderPaidDto();
        ArrayList<OrderPaidDetailDto> dtoItemList = new ArrayList<>(itemList.size());
        BeanUtils.copyProperties(order, orderPaidDto);
        //手动设置 参数 这里的userId  = shopUserId
        orderPaidDto.setOrderId(orderId);
        orderPaidDto.setShopUserId(order.getUserId());
        for (int i = 0; i < itemList.size(); i++) {
            OrderItemVo itemVo = itemList.get(i);
            OrderPaidDetailDto itemDto = new OrderPaidDetailDto();
            BeanUtils.copyProperties(itemVo, itemDto);
            dtoItemList.add(itemDto);
            if (i==0){
                orderPaidDto.setMemberTypeId(itemVo.getMemberTypeId());
            }
        }

        orderPaidDto.setOrderDetails(dtoItemList);
        sender.sendCompletedOrderMessage(orderVo);
        sender.sendOrderPayedPrizeMessage(orderPaidDto);
    }

    @Override
    public CountCostDto getFreightAmount(GetCostDto dto) {
        log.info("GetCostDto is {}", ObjectUtil.isNotNull(dto) ? dto.toString() : "");
        CountCostDto dtoResult = new CountCostDto();
        TimeInterval timer = DateUtil.timer();
        if (dto.getType().equals(1)) {
            // 以规格作为分组
            Map<Long, List<ItemDto>> dataMap = dto.getItems().stream().collect(Collectors.groupingBy(ItemDto::getSkuId));

            List<ItemDto> itemDtoList = new ArrayList<>();
            for (Map.Entry<Long, List<ItemDto>> entry : dataMap.entrySet()) {
                Long key = entry.getKey();
                List<ItemDto> list = entry.getValue();
                Integer number = 0;
                // 将同规格产品的数量相加
                for (ItemDto itemDto : list) {
                    number = number + itemDto.getNumber();
                }
                ItemDto itemDto = new ItemDto();
                itemDto.setSkuId(key);
                itemDto.setNumber(number);
                itemDtoList.add(itemDto);
            }
            dto.setItems(itemDtoList);
            List<LogisticsFreightDto> freightDtoList = new ArrayList<>();
            // 查询商品对应的邮费模板
            List<ItemVo> itemVos = remoteGoodsService.findItemVoByIds(dto.getItemSkuIds());
            Map<Long, Integer> numberMap = dto.getItems().stream().collect(Collectors.toMap(ItemDto::getSkuId,
                    ItemDto::getNumber));
            for (ItemVo itemVo : itemVos) {
                // 商品包邮 跳出当前add追加
                if (itemVo.getFreightTemplateId() == 0) {
                    continue;
                }
                LogisticsFreightDto freightDto = new LogisticsFreightDto();
                freightDto.setFreightTemplateId(itemVo.getFreightTemplateId());
                freightDto.setPrice(itemVo.getProductPrice());
                freightDto.setWeight(itemVo.getWeight());
                freightDto.setNum(numberMap.get(itemVo.getProductSkuId()));

                freightDtoList.add(freightDto);
            }
            log.info("getLogisticsFreightCalculation param is {} {}", JSONUtil.toJsonStr(freightDtoList),
                    dto.getRegion());
            BigDecimal cost = BigDecimal.ZERO;
            if (CollectionUtil.isNotEmpty(freightDtoList)) {
                cost = remoteLogisticsFeginService.getLogisticsFreightCalculation(JSONUtil.toJsonStr(freightDtoList),
                        dto.getRegion());
            }
            log.info("getLogisticsFreightCalculation result is {}", cost);
            log.info("查询快递运费耗时{}ms", timer.intervalRestart());
            dtoResult = new CountCostDto();
            dtoResult.setCost(cost);
        }
        return dtoResult;
    }

    @Override
    public PageUtils<ApiIntegralOrderVo> getApiIntegralOrderVoByUserId(ApiIntegralOrderParam apiIntegralOrderParam) {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        if (ObjectUtil.isNull(curUserDto)) {
            throw new ServiceException(SystemCode.UNAUTHORIZED);
        }
        log.info("当前用户信息:" + curUserDto.toString());
        String userId = curUserDto.getUserId();
        IPage<ApiIntegralOrderVo> apiIntegralOrderVoByUserId = this.baseMapper.getApiIntegralOrderVoByUserId(new Page<>(apiIntegralOrderParam.getCurrent(), apiIntegralOrderParam.getSize()), userId);
        return new PageUtils<>(apiIntegralOrderVoByUserId);
    }

    @Override
    public BigDecimal getUserExchangeNumByUserId(String userId, String skuId, Long productId) {
        return this.baseMapper.getUserExchangeNumByUserId(userId,skuId,productId);
    }

    @Override
    public OrderOverviewVo orderOverview() {
        List<OrderStatusEnum> orderStatusEnumList = Arrays.asList(OrderStatusEnum.WAIT_FOR_PAY,
                OrderStatusEnum.SHIPPED, OrderStatusEnum.WAIT_FOR_PICKUP);
        List<Order> orderList = baseMapper.selectList(new QueryWrapper<Order>().lambda()
                .in(Order::getStatus, orderStatusEnumList)
                .eq(Order::getUserId, CurUserUtil.getHttpCurUser().getUserId())
        );
        OrderOverviewVo vo = new OrderOverviewVo();
        vo.setWaitForPay(orderList.stream().filter(o -> o.getStatus() == OrderStatusEnum.WAIT_FOR_PAY).count());
        vo.setShipped(orderList.stream().filter(o -> o.getStatus() == OrderStatusEnum.SHIPPED).count());
        vo.setWaitForPickup(orderList.stream().filter(o -> o.getStatus() == OrderStatusEnum.WAIT_FOR_PICKUP).count());
        return vo;
    }

    @Override
    public OrderOverviewVo orderOverview(String userId) {
        List<OrderStatusEnum> orderStatusEnumList = Arrays.asList(OrderStatusEnum.WAIT_FOR_PAY, OrderStatusEnum.APPROVED,
                OrderStatusEnum.SHIPPED, OrderStatusEnum.WAIT_FOR_PICKUP, OrderStatusEnum.WAIT_FOR_SEND);
        List<Order> orderList = baseMapper.selectList(new QueryWrapper<Order>().lambda()
                .in(Order::getStatus, orderStatusEnumList)
                .eq(Order::getUserId, userId)
                .notIn(Order::getType, OrderTypeEnum.REPLENISH)
        );
        OrderOverviewVo vo = new OrderOverviewVo();
        List<AfsOrder> afsOrders = afsOrderMapper.selectList(new LambdaQueryWrapper<AfsOrder>()
                .eq(AfsOrder::getUserId, userId));
        long afsOrdersNumber = 0;
        if (CollectionUtil.isNotEmpty(afsOrders)) {
            //计算售后订单未处理个数
            long count = afsOrders.stream().filter(afsOrder -> afsOrder.getStatus() != AfsOrderStatusEnum.SUCCESS && afsOrder.getStatus() != AfsOrderStatusEnum.CLOSE).count();
            afsOrdersNumber = count;
        }
        vo.setWaitForPay(orderList.stream().filter(o -> (o.getStatus() == OrderStatusEnum.WAIT_FOR_PAY ||
                o.getStatus() == OrderStatusEnum.APPROVED)).count());
        vo.setShipped(orderList.stream().filter(o -> o.getStatus() == OrderStatusEnum.SHIPPED).count());
        vo.setWaitForPickup(orderList.stream().filter(o -> o.getStatus() == OrderStatusEnum.WAIT_FOR_PICKUP).count());
        vo.setWithDelivery(orderList.stream().filter(o -> o.getStatus() == OrderStatusEnum.WAIT_FOR_SEND).count());
        vo.setAfsOrder(afsOrdersNumber);
        return vo;
    }

    @Override
    public OrderShareInfo orderShareInfo(Long orderId) {
        OrderShareSetting setting = orderShareSettingService.getSetting();
        OrderDelivery orderDelivery = orderDeliveryMapper.selectByOrderId(orderId);
        List<SimpleOrderItemVo> orderItemVos = orderItemMapper.selectSimpleOrderItemVoByOrderId(orderId);
        OrderShareInfo orderShareInfo = new OrderShareInfo();
        if (ObjectUtil.isNull(setting)) {
            throw new ServiceException("获取分享设置失败，请联系商家检查分享设置");
        }
        orderShareInfo.setTitle(setting.getTitle().replace("{sname}", orderDelivery.getReceiverName()));
        orderShareInfo.setBackground(setting.getBackground());
        List<ShareItemVo> shareItemVos = new ArrayList<>();
        for (SimpleOrderItemVo orderItemVo : orderItemVos) {
            ShareItemVo shareItemVo = new ShareItemVo();
            shareItemVo.setProductName(orderItemVo.getProductName());
            shareItemVo.setProductPic(orderItemVo.getProductPic());
            shareItemVo.setProductQuantity(orderItemVo.getProductQuantity());
            shareItemVos.add(shareItemVo);
        }
        orderShareInfo.setItemList(shareItemVos);
        return orderShareInfo;
    }

    @Override
    public void refundNotify(RefundNotifyResultDto message) {
        log.info(message.toString());
        Order order = baseMapper.selectOne(new QueryWrapper<Order>().lambda().eq(Order::getTransactionId, message.getOutTradeNo()));
        order.setRefundTransactionId(message.getRefundId());
        baseMapper.updateById(order);
        OrderVo vo = baseMapper.selectOrderVoById(order.getId());
        sender.sendRefundSuccess(vo);
    }

    /**
     * 查询会员历史订单
     * @param dto
     * @return
     */
    @Override
    public PageUtils<ApiManageHistoryOrderVo> searchHistoryOrder(ApiSearchHistoryOrderDto dto) {
        List<Integer> orderStatusList = new ArrayList<>(5);

        orderStatusList.add(OrderStatusEnum.WAIT_FOR_SEND.getCode());
        orderStatusList.add(OrderStatusEnum.SHIPPED.getCode());
        orderStatusList.add(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());
        orderStatusList.add(OrderStatusEnum.COMPLETE.getCode());
        orderStatusList.add(OrderStatusEnum.APPROVED.getCode());
        if(StrUtil.isNotBlank(dto.getUserId()) && StrUtil.isBlank(dto.getPhone())){
            // 根据用户id查询用户电话
            //查询会员信息
            AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(dto.getUserId(), Arrays.asList(1));
            dto.setPhone(accountInfoDto.getMiniAccountunt().getPhone());
        }
        Page<ApiManageHistoryOrderVo> page = baseMapper.searchApiHistoryOrderVoPage(new Page(dto.getCurrent(), dto.getSize()),
                orderStatusList, dto.getPhone(), dto.getUserId());
        for (ApiManageHistoryOrderVo record : page.getRecords()) {
            String source = record.getSource();
            if("mall".equalsIgnoreCase(source)){
                // 来源于商城本身的订单
                List<SimpleOrderItemVo> orderItemVos = orderItemMapper.selectSimpleOrderItemVoByOrderId(record.getOrderId());
                record.setItemVoList(orderItemVos);
            }else if("history".equalsIgnoreCase(source)){
                // 来源于历史订单
                List<SimpleOrderItemVo> orderItemVos = new ArrayList<>();
                List<SimpleSalesItemVo> salesItemVos = salesItemLydHistoryMapper.selectSimpleItemVoBySalesId(record.getId());
                BeanUtil.copyProperties(orderItemVos, salesItemVos);
                record.setItemVoList(orderItemVos);
            }
        }
        return new PageUtils(page);
    }

    @Override
    public RelationInfoVo getRelationInfoByInviteCode(String inviteCode) {
        RelationInfoVo relationInfoVo = new RelationInfoVo();
        if(StrUtil.isNotBlank(inviteCode)){
            // 解密
            try {
                inviteCode = URLDecoder.decode(inviteCode, "UTF-8");
                log.info("登录方法，传进来的邀请码，解码后：{}", inviteCode);
                String userId = AESUtil.decryptDataBase64MD5Key(inviteCode, null);
                log.info("登录方法，传进来的邀请码，解密后：{}", userId);
                relationInfoVo = remoteMiniInfoService.getRelationInfoByMiniAccountId(userId);
            } catch (Exception exception) {
                exception.printStackTrace();
            }
        }
        return relationInfoVo;
    }

    @Override
    public PageUtils<ApiMemberOrderVo> getApiMemberOrder(ApiMemberOrderParam param) {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        if (ObjectUtil.isNull(curUserDto)) {
            throw new ServiceException(SystemCode.UNAUTHORIZED);
        }
        param.setUserId(curUserDto.getUserId());
        Page<ApiMemberOrderVo> page = baseMapper.searchApiMemberOrder(new Page(param.getCurrent(), param.getSize()), param);
        return new PageUtils(page);
    }

    @Override
    public Boolean getUpgrade(UpgradeDto dto) {

        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        if (ObjectUtil.isNull(curUserDto)) {
            throw new ServiceException(SystemCode.UNAUTHORIZED);
        }
        log.info("当前用户信息:" + curUserDto.toString());

        Boolean result = false;
        //获取主店铺
        ShopsPartner shopsPartner = remoteShopsService.getShopsPartnerMain();
        //获取主店铺特殊配置
        List<SpecialSetting> SpecialSettingList = remoteMiniInfoService.getSpecialSettingByShopId(shopsPartner.getShopId());
        SpecialSetting specialSetting = SpecialSettingList.get(0);
        if(specialSetting.getMemberSales() == CommonConstants.NUMBER_ONE){
            ApiMemberLevelVo apiMemberLevel = remoteMiniAccountService.getApiMemberLevel(curUserDto.getUserId());
            BigDecimal nextAmount = apiMemberLevel.getNextAmount();
            String memberId = apiMemberLevel.getMemberId();
            BigDecimal memberAmount = apiMemberLevel.getMemberAmount();
            List<MemberLevelGoodsPrice> memberLevelGoodsPriceList = remoteGoodsService.selectMemberGoodsPrice(memberId);
            BigDecimal amount = BigDecimal.ZERO;
            for (ItemDto itemDto : dto.getItems()) {
                Integer priceType = itemDto.getPriceType();
                Long skuId = itemDto.getSkuId();
                Integer number = itemDto.getNumber();
                SkuStock skuStock = remoteGoodsService.findSkuStockById(skuId);
                //会员价
                if(priceType == CommonConstants.NUMBER_ONE && CollUtil.isNotEmpty(memberLevelGoodsPriceList)){
                    //获取商品规格会员价
                    List<MemberLevelGoodsPrice> memberLevelGoodsPrices = memberLevelGoodsPriceList.stream().filter(memberLevelGoodsPrice->  memberLevelGoodsPrice.getSkuId().equals(itemDto.getSkuId())).collect(Collectors.toList());
                    //将商品的价格替换成会员价
                    if(CollectionUtil.isNotEmpty(memberLevelGoodsPrices)){
                        if (memberLevelGoodsPrices.get(0).getMemberLevelPrice()!=null){
                            if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==itemDto.getPriceType()){
                                BigDecimal price = memberLevelGoodsPrices.get(0).getMemberLevelPrice();
                                amount = amount.add(price.multiply(new BigDecimal(number)));

                            }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==itemDto.getPriceType()){
                                BigDecimal price = skuStock.getPrice().multiply(memberLevelGoodsPrices.get(0).getMemberLevelPrice()).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP);
                                amount = amount.add(price.multiply(new BigDecimal(number)));
                            }
                        }
                    }
                }
            }
            if(nextAmount!=null&&memberAmount.add(amount).compareTo(nextAmount)>=0){
                result = true;
            }

        }
        return result;
    }

    @Override
    public Integer getUnDeliveryQuantity(String userId, Long memberTypeId) {
        Integer allUnDeliveryQuantity = 0;
        UnShippedOrderItemParam param = new UnShippedOrderItemParam();
        param.setUserId(userId);
        param.setMemberTypeId(memberTypeId);
        List<UnShippedOrderItemVo> list = orderItemMapper.getUnShippedOrderItem( param);
        if(list!=null&&list.size()>0){
            for (UnShippedOrderItemVo unShippedOrderItemVo : list) {
                Integer unDeliveryQuantity = unShippedOrderItemVo.getUnDeliveryQuantity();
                Long productId = unShippedOrderItemVo.getProductId();
                Long productSkuId = unShippedOrderItemVo.getProductSkuId();
                Integer afterQty = remoteAfsService.getAfterQty(productId,productSkuId,userId);
                if(afterQty == null){
                    afterQty = 0;
                }
                if(unDeliveryQuantity - afterQty>=0){
                    unShippedOrderItemVo.setUnDeliveryQuantity(unDeliveryQuantity - afterQty);
                }else{
                    unShippedOrderItemVo.setUnDeliveryQuantity(0);
                }
                if(unShippedOrderItemVo.getUnDeliveryQuantity() != 0){
                    allUnDeliveryQuantity = allUnDeliveryQuantity + unShippedOrderItemVo.getUnDeliveryQuantity();
                }
            }
        }
        return allUnDeliveryQuantity;
    }
    /**
     * 分页查询未发货订单商品
     * @param param
     * @return
     */
    @Override
    public List<UnShippedOrderItemVo> getUnShippedOrderItem(UnShippedOrderItemParam param) {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        if (ObjectUtil.isNull(curUserDto)) {
            throw new ServiceException(SystemCode.UNAUTHORIZED);
        }
        param.setCurrent(1);
        param.setSize(10000);
        log.info("当前用户信息:" + curUserDto.toString());
        param.setUserId(curUserDto.getUserId());
        
        if(param.getMemberTypeId() == null){
            MemberType memberType = remoteMiniAccountService.getDefaultMemberType();
            if(memberType!=null){
                param.setMemberTypeId(Long.valueOf(memberType.getId()));
            }
        }
        
        List<UnShippedOrderItemVo> list = orderItemMapper.getUnShippedOrderItem( param);
        List<UnShippedOrderItemVo>dataList = new ArrayList<>();
        if(list!=null&&list.size()>0){
            for (UnShippedOrderItemVo unShippedOrderItemVo : list) {
                Integer unDeliveryQuantity = unShippedOrderItemVo.getUnDeliveryQuantity();
                Long productId = unShippedOrderItemVo.getProductId();
                Long productSkuId = unShippedOrderItemVo.getProductSkuId();
                Integer afterQty = remoteAfsService.getAfterQty(productId,productSkuId,curUserDto.getUserId());
                if(afterQty == null){
                    afterQty = 0;
                }
                if(unDeliveryQuantity - afterQty>=0){
                    unShippedOrderItemVo.setUnDeliveryQuantity(unDeliveryQuantity - afterQty);
                }else{
                    unShippedOrderItemVo.setUnDeliveryQuantity(0);
                }
                if(unShippedOrderItemVo.getUnDeliveryQuantity() != 0){
                    dataList.add(unShippedOrderItemVo);
                }
            }
        }

        return dataList;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrderDeliveryProxy(OrderDeliveryProxyDto dto) {

        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        if (ObjectUtil.isNull(curUserDto)) {
            throw new ServiceException(SystemCode.UNAUTHORIZED);
        }
        log.info("当前用户信息:" + curUserDto.toString());


        List<OrderDeliveryProxyItemDto> itemList = dto.getItemList();

        //根据店铺id分组
        Map<String, List<OrderDeliveryProxyItemDto>> dataList = itemList.stream().collect(Collectors.groupingBy(OrderDeliveryProxyItemDto::getShopId));
        for (Map.Entry<String, List<OrderDeliveryProxyItemDto>> entry : dataList.entrySet()) {

            String shopId = entry.getKey();
            List<OrderDeliveryProxyItemDto> orderDeliveryProxyItemDtoList = entry.getValue();

            if(StringUtils.isEmpty(shopId)){
                throw new ServiceException("店铺id不能为空！");
            }
            ShopContextHolder.setShopId(shopId);
            ShopsPartner shopsPartner = remoteShopsService.getByShopId(Long.valueOf(shopId));
            OrderDeliveryProxy orderDeliveryProxy = new OrderDeliveryProxy();
            BeanUtil.copyProperties(dto,orderDeliveryProxy);
            orderDeliveryProxy.setUserId(curUserDto.getUserId());
            orderDeliveryProxy.setDeliveryStatus(DeliveryStatusEnum.NO);
            orderDeliveryProxyService.save(orderDeliveryProxy);
            Map<String, List<OrderDeliveryProxyItemDto>> dataList2 = orderDeliveryProxyItemDtoList.stream().collect(Collectors.groupingBy(e -> e.getProductName() + "@@" + e.getProductSkuId()));
            for (Map.Entry<String, List<OrderDeliveryProxyItemDto>> entry2 : dataList2.entrySet()) {
                String key = entry2.getKey();
                String[] split = key.split("@@");
                List<OrderDeliveryProxyItemDto> list = entry2.getValue();
                if(list!=null&&list.size()>1){
                    throw new ServiceException(shopsPartner.getName()+"，"+split[0]+"商品重复，请重新提交数据！");
                }
            }

            if(dataList!=null&&dataList.size()>0){
                for (OrderDeliveryProxyItemDto orderDeliveryProxyItemDto : itemList) {
                    Long productId = orderDeliveryProxyItemDto.getProductId();
                    Long productSkuId = orderDeliveryProxyItemDto.getProductSkuId();
                    //发货数量
                    Integer qty = orderDeliveryProxyItemDto.getProductQuantity();
                    UnShippedOrderItemParam param = new UnShippedOrderItemParam();
                    param.setCurrent(1);
                    param.setSize(10);
                    param.setUserId(curUserDto.getUserId());
                    param.setProductId(productId);
                    param.setProductSkuId(productSkuId);
                    List<UnShippedOrderItemVo> list = orderItemMapper.getUnShippedOrderItem(param);
                    if(list!=null&&list.size()>0){
                        UnShippedOrderItemVo unShippedOrderItemVo = list.get(0);
                        //待发货数量
                        Integer qty2 = unShippedOrderItemVo.getUnDeliveryQuantity();
                        //售后数量
                        Integer afterQty = remoteAfsService.getAfterQty(unShippedOrderItemVo.getProductId(),unShippedOrderItemVo.getProductSkuId(),curUserDto.getUserId());
                        if(afterQty == null){
                            afterQty = 0;
                        }
                        if(qty2 - afterQty>=0){
                            qty2 = qty2 - afterQty;
                        }else{
                            qty2 = 0;
                        }
                        if(qty>qty2){
                            throw new ServiceException(shopsPartner.getName()+"，"+unShippedOrderItemVo.getProductName()+"商品待发货数量不足，无法代发货，请先下单购买！");
                        }
                    }else{
                        throw new ServiceException("订单商品不存在！");
                    }
                    OrderDeliveryProxyItem orderDeliveryProxyItem = new OrderDeliveryProxyItem();
                    BeanUtils.copyProperties(orderDeliveryProxyItemDto,orderDeliveryProxyItem);
                    orderDeliveryProxyItem.setUnDeliveryQuantity(orderDeliveryProxyItem.getProductQuantity());
                    orderDeliveryProxyItem.setMainId(orderDeliveryProxy.getId());
                    orderDeliveryProxyItemService.save(orderDeliveryProxyItem);
                }
                //发送代发货消息
                OrderDeliveryProxyMessage message = new OrderDeliveryProxyMessage();
                message.setId(orderDeliveryProxy.getId());
                message.setShopId(ShopContextHolder.getShopId());
                message.setTenantId(TenantContextHolder.getTenantId());
                sender.sendDeliveryProxyMessage(message);
            }else{
                throw new ServiceException("待发货订单明细列表不能为空！");
            }
        }
    }

    @Override
    public PageUtils getOrderDeliveryProxy(OrderDeliveryProxyParam param) {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        if (ObjectUtil.isNull(curUserDto)) {
            throw new ServiceException(SystemCode.UNAUTHORIZED);
        }
        log.info("当前用户信息:" + curUserDto.toString());
        param.setUserId(curUserDto.getUserId());
        Page<OrderDeliveryProxyVo> page = orderDeliveryProxyService.getOrderDeliveryProxy(param);
        return new PageUtils<>(page);
    }

    @Override
    public ApiOrderPayMessageVo getApiOrderPayMessage(ApiOrderPayMessageDto dto) {


        String shopUserId = "";
        if(dto.getReplaceCreateOrderFlag()!=null&&dto.getReplaceCreateOrderFlag() == CommonConstants.NUMBER_ONE){

            if(StringUtils.isEmpty(dto.getUserId())){
                throw new ServiceException("代下单需要认证用户信息！");
            }
            shopUserId = remoteMiniAccountService.getShopUserIdByUserId(dto.getUserId());
            if(StringUtils.isEmpty(shopUserId)){
                throw new ServiceException("代下单用户信息不存在！");
            }
        }else{
            CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
            if (ObjectUtil.isNull(curUserDto)) {
                throw new ServiceException(SystemCode.UNAUTHORIZED);
            }
            shopUserId = curUserDto.getUserId();
            log.info("当前用户信息:" + curUserDto.toString());
        }
        //查询用户持有的积分、收货地址
        AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(shopUserId, Arrays.asList(1,2,
                3, 5));

        ApiOrderPayMessageVo apiOrderPayMessageVo = new ApiOrderPayMessageVo();
        BigDecimal payAmount = dto.getPayAmount();
        BigDecimal currentGolden = accountInfoDto.getMiniAccountunt().getCurrentGolden();
        BigDecimal currentCommission = accountInfoDto.getMiniAccountunt().getCurrentCommission();
        //是否使用金豆
        Integer goldenFlag = dto.getGoldenFlag();
        //支付金豆
        BigDecimal payGolden = BigDecimal.ZERO;
        //是否使用佣金
        Integer commissionFlag = dto.getCommissionFlag();
        //支付佣金
        BigDecimal payCommission= BigDecimal.ZERO;

        //判断是否使用金豆
        if(goldenFlag!=null&&goldenFlag == CommonConstants.NUMBER_ONE){
            if(dto.getPayGolden()!=null&&dto.getPayGolden().compareTo(BigDecimal.ZERO)>0){
                //如果支付得金豆大于可用金豆提示报错
                if(dto.getPayGolden().compareTo(currentGolden)>0){
                    throw new ServiceException("支付金豆不能大于可用金豆"+currentGolden.stripTrailingZeros()+"！");
                }else{
                    payGolden = dto.getPayGolden();
                }

            }else{
                payGolden = currentGolden;
            }
        }

        //判断是否使用佣金
        if(commissionFlag!=null&&commissionFlag == CommonConstants.NUMBER_ONE){
            if(dto.getPayCommission()!=null&&dto.getPayCommission().compareTo(BigDecimal.ZERO)>0){
                //如果支付的佣金大于可用佣金提示报错
                if(dto.getPayCommission().compareTo(currentCommission)>0){
                    throw new ServiceException("支付佣金不能大于可用佣金"+currentCommission.stripTrailingZeros()+"！");
                }else{
                    payCommission = dto.getPayCommission();
                }

            }else{
                payCommission = currentCommission;
            }
        }


        apiOrderPayMessageVo.setCurrentGolden(currentGolden);
        apiOrderPayMessageVo.setCurrentCommission(currentCommission);

        //如果金豆大于0
        if(payGolden.compareTo(BigDecimal.ZERO)>0){
            //如果金豆余额大于等于订单应付金额-金豆支付
            if(payGolden.compareTo(payAmount)>=0){
                apiOrderPayMessageVo.setPayGolden(payAmount);
                apiOrderPayMessageVo.setPayCommission(BigDecimal.ZERO);
                apiOrderPayMessageVo.setPayMoney(BigDecimal.ZERO);
                apiOrderPayMessageVo.setPayType(PayTypeEnum.GOLDEN_BEAN);
            }else{
                //如果金豆余额小于订单应付金额，应付金额减掉金豆余额，金豆金额支付部分应付金额，剩余金额执行佣金计算逻辑
                apiOrderPayMessageVo.setPayGolden(payGolden);
                //如果佣金金额大于0
                if(payCommission.compareTo(BigDecimal.ZERO)>0){
                    //如果佣金金额大于等于订单剩余应付金额-金豆，佣金支付
                    if(payCommission.compareTo(payAmount.subtract(payGolden))>=0){
                        apiOrderPayMessageVo.setPayCommission(payAmount.subtract(payGolden));
                        apiOrderPayMessageVo.setPayMoney(BigDecimal.ZERO);
                        apiOrderPayMessageVo.setPayType(PayTypeEnum.GOLDEN_BEAN_AND_COMMISSION);
                    }else{
                        //如果佣金金额小于订单剩余应付金额，需要选择现金支付，这里支付方式为空 让前端选择
                        apiOrderPayMessageVo.setPayCommission(payCommission);
                        apiOrderPayMessageVo.setPayMoney(payAmount.subtract(payGolden).subtract(payCommission));
                    }
                }else{
                    //如果佣金金额小于0，佣金则不支付
                    apiOrderPayMessageVo.setPayCommission(BigDecimal.ZERO);
                    apiOrderPayMessageVo.setPayMoney(payAmount.subtract(payGolden));
                }
            }
        }else{
            //如果金豆金额小于0，金豆则不支付
            apiOrderPayMessageVo.setPayGolden(BigDecimal.ZERO);
            //如果佣金金额大于0
            if(payCommission.compareTo(BigDecimal.ZERO)>0){
                //如果佣金金额大于等于订单剩余应付金额-佣金支付
                if(payCommission.compareTo(payAmount)>=0){
                    apiOrderPayMessageVo.setPayCommission(payAmount);
                    apiOrderPayMessageVo.setPayMoney(BigDecimal.ZERO);
                    apiOrderPayMessageVo.setPayType(PayTypeEnum.COMMISSION);
                }else{
                    //如果佣金金额小于订单剩余应付金额，需要选择现金支付，这里支付方式为空 让前端选择
                    apiOrderPayMessageVo.setPayCommission(payCommission);
                    apiOrderPayMessageVo.setPayMoney(payAmount.subtract(payCommission));
                }
            }else{
                //如果佣金金额小于0，佣金则不支付
                apiOrderPayMessageVo.setPayCommission(BigDecimal.ZERO);
                apiOrderPayMessageVo.setPayMoney(payAmount);
            }
        }

        return apiOrderPayMessageVo;
    }

    @Override
    @Transactional
    public void uploadEffectUrl(OrderUploadEffectDto dto) {

        if(dto.getEffectImageUrlList().isEmpty()){
            throw new ServiceException("上传的效果图不能为空！");
        }
        if(dto.getOrderId() == null ||dto.getOrderId().equals("")){
            throw new ServiceException("订单id不能为空！");
        }
        OrderSetting orderSetting = orderSettingMapper.selectOne(null);
        Integer uploadPicNum = orderSetting.getUploadPicNum();
        if(dto.getEffectImageUrlList().size()>uploadPicNum){
            throw new ServiceException("最大允许上传"+uploadPicNum+"数量，您上传图片数量已超过限制");
        }

        Order order = this.getById(dto.getOrderId());
        if(order == null || order.equals("")){
            throw new ServiceException("订单不存在！");
        }

        LambdaQueryWrapper<OrderEvaluate>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderEvaluate::getOrderId,dto.getOrderId());

        List<OrderEvaluate> list = orderEvaluateMapper.selectList(wrapper);
        if(list!=null&&list.size()>0){
            if(list.size()>1){
                throw new ServiceException("订单评论只能有一条，请联系管理员！");
            }
            OrderEvaluate orderEvaluate = list.get(0);
            String effectImageUrl = "";
            for (String str: dto.getEffectImageUrlList()) {
                if(effectImageUrl.length()>0){
                    effectImageUrl+=",";
                }
                effectImageUrl+=str;
            }
            orderEvaluate.setEffectImageUrl(effectImageUrl);
            orderEvaluateMapper.updateById(orderEvaluate);
            order.setUploadEffectStatus(CommonConstants.NUMBER_ONE);
            this.updateById(order);
        }else{
            throw new ServiceException("评论不存在！");
        }
    }


    /**
     * 提醒发货
     * @param relationInfoVo
     * @param orderId
     * @param order
     */
    private void wxMpSendPayMessage(RelationInfoVo relationInfoVo,Long orderId,Order order){
        //提醒发货
        String miniAccountId = relationInfoVo.getMiniAccountId();
        if(StringUtils.isNotEmpty(miniAccountId)){
            //用户openId
            MiniAccountOauths miniAccountOauths = remoteMiniAccountService.getMiniAccountOauths(miniAccountId);
            //卖家发货消息模板
            WxMessageTemplateVo wxMessageTemplateVo = remoteMiniInfoService.getWxMessageTemplateByCode(CommonConstants.NUMBER_FOUR + "");
            //商品详情
            List<OrderItem> orderItemList = orderItemMapper.selectByOrderId(orderId);
            String goodsName = "";
            if(orderItemList!=null&&orderItemList.size()>0){
                for (OrderItem orderItem : orderItemList) {
                    if(goodsName.length()>0){
                        goodsName+=",";
                    }
                    goodsName+=orderItem.getProductName()+"*"+orderItem.getProductQuantity();
                }
            }

            if(miniAccountOauths!=null&&StringUtils.isNotEmpty(miniAccountOauths.getOpenId())
                    &&wxMessageTemplateVo!=null&&StringUtils.isNotEmpty(wxMessageTemplateVo.getTemplateId())
                    &&wxMessageTemplateVo.getType()==2){
                WxSendMessageDto wxSendMessageDto = new WxSendMessageDto();
                MiniInfoVo miniInfoVo = remoteMiniInfoService.getMiniInfoVoByTenantId(order.getTenantId());
                wxSendMessageDto.setAppMpId(miniInfoVo.getAppMpId());
                wxSendMessageDto.setAppMpSecret(miniInfoVo.getAppMpSecret());
                wxSendMessageDto.setAppId(miniInfoVo.getAppId());
                wxSendMessageDto.setAppSecret(miniInfoVo.getAppSecret());
                wxSendMessageDto.setTemplateId(wxMessageTemplateVo.getTemplateId());
                wxSendMessageDto.setPage("/page/index/index");
                wxSendMessageDto.setTenantId(order.getTenantId());
                wxSendMessageDto.setShopId(order.getShopId());
                wxSendMessageDto.setMessage(wxMessageTemplateVo.getName());
                wxSendMessageDto.setUseType(2);
                wxSendMessageDto.setMessageType(1);
                wxSendMessageDto.setTitle(wxMessageTemplateVo.getName());
                List<String>openIds = new ArrayList<>();
                openIds.add(miniAccountOauths.getMpOpenId());
                wxSendMessageDto.setOpenIds(openIds);
                List<WxMessageTemplateDetailVo> wxMessageTemplateDetailVoList = wxMessageTemplateVo.getList();
                List<Map<String,String>>mapList = new ArrayList<>();
                if(wxMessageTemplateDetailVoList!=null&&wxMessageTemplateDetailVoList.size()>0){
                    for (WxMessageTemplateDetailVo wxMessageTemplateDetailVo : wxMessageTemplateDetailVoList) {
                        Map<String,String> map = new HashMap<>();
                        if(wxMessageTemplateDetailVo.getName().equals("订单编号")){
                            map.put(wxMessageTemplateDetailVo.getKeyData(),order.getId()+"");
                        }
                        if(wxMessageTemplateDetailVo.getName().equals("支付金额")){
                            map.put(wxMessageTemplateDetailVo.getKeyData(),order.getPayAmount().toPlainString());
                        }
                        if(wxMessageTemplateDetailVo.getName().equals("付款时间")){
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                            map.put(wxMessageTemplateDetailVo.getKeyData(),order.getPayTime().format(formatter));
                        }
                        if(wxMessageTemplateDetailVo.getName().equals("付款方")){
                            map.put(wxMessageTemplateDetailVo.getKeyData(),order.getUserName());
                        }
                        if(wxMessageTemplateDetailVo.getName().equals("商品名称")){
                            map.put(wxMessageTemplateDetailVo.getKeyData(),goodsName);
                        }
                        mapList.add(map);
                    }
                }
                wxSendMessageDto.setMapList(mapList);
                sender.sendWxMpMessage(wxSendMessageDto);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean createPrizeOrder(PrizeProductDto prizeProductMessage){
        // 查询用户信息和地址
        if (prizeProductMessage.getCurUserDto()==null){
            throw new ServiceException("获取不到登录信息！");
        }
        if (null == prizeProductMessage.getAddress()){
            throw new ServiceException("获取不到地址信息！");
        }


        CurUserDto curUser = prizeProductMessage.getCurUserDto();
        MiniAccountAddress address = JSONObject.parseObject(prizeProductMessage.getAddress(),MiniAccountAddress.class);

        CreateOrderDto createOrderDto = new CreateOrderDto();
        createOrderDto.setDeliverType(DeliverTypeEnum.LOGISTICS);
        createOrderDto.setMiniAccountAddressId(address.getId());
        createOrderDto.setSourceType(SourceTypeEnum.MINI_PROGRAM);
        // 商超 订单
        createOrderDto.setOrderType(OrderTypeEnum.MALL);
        createOrderDto.setMallOrderType(ProductTypeEnum.PRIZE_PRODUCT.getStatus());
        // 支付方式
        createOrderDto.setPayType(PayTypeEnum.FREE);

        // 设置支付相关字段为0
        createOrderDto.setPayGolden(BigDecimal.ZERO);
        createOrderDto.setPayCommission(BigDecimal.ZERO);
        createOrderDto.setCouponPrice(BigDecimal.ZERO);

        // 商品
        ItemDto itemDto = new ItemDto();
        itemDto.setSkuId(prizeProductMessage.getSkuId());
        itemDto.setNumber(1); // 中奖商品数量固定为1
        itemDto.setShopId(prizeProductMessage.getShopId());
        // 会员类型
        Long memberTypeId = prizeProductMessage.getMemberTypeId();
        itemDto.setMemberTypeId(memberTypeId);

        List<ItemDto> itemDtoList = new ArrayList<>();
        itemDtoList.add(itemDto);
        createOrderDto.setItemDtoList(itemDtoList);


        // 库存
        List<SkuStock> skuStockList = new ArrayList<>();
        SkuStock skuStock = new SkuStock();
        skuStock.setProductId(prizeProductMessage.getProductId());
        skuStock.setPrice(BigDecimal.ZERO); // 中奖商品价格为0
        skuStock.setOriginalPrice(BigDecimal.ZERO);
        skuStock.setSkuPrice(BigDecimal.ZERO);
  /*
        // 查询商品信息以补充缺少的字段
         ProductVo productVo = remoteGoodsService.findProductById(prizeProductMessage.getProductId());
        if (productVo != null) {
            // 补充必要的SKU信息
            skuStock.setSkuCode(productVo.getSkuCode());
            skuStock.setSpecs(productVo.getSpecs());
            skuStock.setWeight(productVo.getWeight());
            skuStock.setPic(productVo.getPic());
            skuStock.setStock(BigDecimal.valueOf(1)); // 设置库存为1
            skuStock.setShopId(prizeProductMessage.getShopId() != null ? prizeProductMessage.getShopId().toString() : null);
            if (StringUtils.isNotEmpty(productVo.getSpecs2())) {
                skuStock.setSpecs2(productVo.getSpecs2());
            }
        }
*/

        //备注
        LocalDateTime now = LocalDateTime.now();
        String note = String.format("%s年%s月%s日参与%s活动中奖 ", now.getYear(), now.getMonthValue(), now.getDayOfMonth(), prizeProductMessage.getPrizeName());
        createOrderDto.setNote( note);
        skuStockList.add(skuStock);
        //免运费标识
        createOrderDto.setNotCheckFlag(true);

        // 调用创建订单方法
        Boolean result = SpringContextHolder.getBean(IMiniOrderService.class).createOrder(createOrderDto, prizeProductMessage.getPostOrderId(), skuStockList, curUser);

        if (result) {
            log.info("中奖商品订单创建成功，用户ID: {}, 商品ID: {}, SKU ID: {},订单ID:{}",
                    curUser.getUserId(), prizeProductMessage.getProductId(), prizeProductMessage.getSkuId(),prizeProductMessage.getPostOrderId());
            // 奖品已发放 回调更新抽奖记录状态 返回地址
            Long accountPrizeId = prizeProductMessage.getId();

            Boolean updated = remoteShopsService.updateAccountPrizeStatus(accountPrizeId, SalePrizeEnums.PrizeStatus.STATUS_ISSUED.getCode(),prizeProductMessage.getPostOrderId());
            if (!updated){
                log.info("更新抽奖参与记录状态为{}失败", SalePrizeEnums.PrizeStatus.STATUS_ISSUED.getDesc());
                throw new ServiceException("更新抽奖参与记录状态为已发放失败", SystemCode.FAILURE_CODE);
            }
            return true;
        } else {
            log.error("中奖商品订单创建失败，用户ID: {},  商品ID: {}, SKU ID: {},订单ID:{}",
                    curUser.getUserId(), prizeProductMessage.getProductId(),prizeProductMessage.getSkuId(),prizeProductMessage.getPostOrderId());
            throw new ServiceException("中奖商品订单创建失败",SystemCode.FAILURE_CODE);
        }
    }

    @Override
    public BigDecimal getTeamsRegionAmount(String upLevelTime, Integer regionType, String agentRegionCode, Integer teamTimes) {
        String startDate = "";
        String endDate = "";
        if(teamTimes>0){
            // 获取当前日期
            LocalDate today = LocalDate.now();
            // 计算团队业绩周期
            LocalDate teamTimesLocalDate = today.minus(teamTimes, ChronoUnit.MONTHS);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            startDate = teamTimesLocalDate.format(formatter2) + " 00:00:00";
            endDate = today.format(formatter);
        }
        BigDecimal teamsRegionAmount =
                this.baseMapper.getTeamsRegionAmount(upLevelTime,regionType,agentRegionCode,startDate,endDate);
        return teamsRegionAmount;
    }

    @Override
    public Integer getOrderCountByRegionCode(String agentRegionCode, String upLevelTime,Integer regionType) {
        return this.baseMapper.getOrderCountByRegionCode(agentRegionCode,upLevelTime,regionType);
    }



}
