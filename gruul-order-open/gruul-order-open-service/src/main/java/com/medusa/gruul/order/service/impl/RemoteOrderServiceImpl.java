package com.medusa.gruul.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.account.api.model.message.AccountReturnBalanceMessage;
import com.medusa.gruul.afs.api.entity.AfsOrder;
import com.medusa.gruul.afs.api.entity.AfsOrderItem;
import com.medusa.gruul.afs.api.enums.AfsOrderTypeEnum;
import com.medusa.gruul.afs.api.feign.RemoteAfsService;
import com.medusa.gruul.afs.api.model.AfsRemoveDeliverOrderMessage;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.TimeConstants;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.order.api.constant.OrderQueueNameConstant;
import com.medusa.gruul.order.api.entity.*;
import com.medusa.gruul.order.api.enums.*;
import com.medusa.gruul.order.api.model.*;
import com.medusa.gruul.order.mapper.*;
import com.medusa.gruul.order.mq.Sender;
import com.medusa.gruul.order.service.IOrderDeliveryProxyItemService;
import com.medusa.gruul.order.service.IOrderDeliveryProxyService;
import com.medusa.gruul.order.service.IRemoteOrderService;
import com.medusa.gruul.payment.api.feign.RemotePaymentService;
import com.medusa.gruul.payment.api.model.dto.RefundRequestDto;
import com.medusa.gruul.payment.api.model.message.RefundRequestWechatMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <p>
 * 订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019 -09-02
 */
@Slf4j
@Service
public class RemoteOrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements IRemoteOrderService {
    @Resource
    private OrderProductEvaluateMapper orderProductEvaluateMapper;
    @Resource
    private OrderDeliveryMapper orderDeliveryMapper;
    @Resource
    private OrderDeliveryItemMapper orderDeliveryItemMapper;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private AfsOrderMapper afsOrderMapper;
    @Resource
    private AfsOrderItemMapper afsOrderItemMapper;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private OrderSettingMapper orderSettingMapper;
    @Resource
    private RemotePaymentService remotePaymentService;
    @Resource
    private RemoteMiniAccountService remoteMiniAccountService;
    @Resource
    private RemoteAfsService remoteAfsService;

    @Autowired
    private IOrderDeliveryProxyService orderDeliveryProxyService;
    @Autowired
    private IOrderDeliveryProxyItemService orderDeliveryProxyItemService;
    @Resource
    private Sender sender;


    @Override
    public OrderVo orderInfo(Long orderId) {
        OrderVo vo = baseMapper.selectOrderVoById(orderId);
        vo.setProductTotalQuantity(vo.getOrderItemList().stream().mapToInt(OrderItem::getProductQuantity).sum());
        return vo;
    }

    @Override
    public List<ProductRateVo> productRate(List<Long> productIds) {
        return orderProductEvaluateMapper.selectProductRateByList(productIds);
    }

    @Override
    public List<GetOrderListDto> getOrderListByIds(GetOrderListParam param) {
        if (param.getOrderIds().isEmpty()) {
            return new ArrayList<>();
        }
        return baseMapper.selectOrderListByIds(param.getOrderIds());
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public int doLogisticsOrderDelivery(List<OrderDeliveryDto> orderDeliveryDtos) {
        AtomicInteger res = new AtomicInteger(0);
        OrderSetting orderSetting = orderSettingMapper.selectOne(null);
        orderDeliveryDtos.forEach(orderDeliveryDto -> {

            TenantContextHolder.setTenantId(orderDeliveryDto.getTenantId());
            ShopContextHolder.setShopId(orderDeliveryDto.getShopId());


            OrderDto orderDto = baseMapper.selectOneById(orderDeliveryDto.getOrderId());


            if (null == orderDto) {
                throw new ServiceException(SystemCode.DATA_NOT_EXIST);
            }

            Long orderId = orderDeliveryDto.getOrderId();


            orderDeliveryDto.setDeliveryTime(LocalDateTime.now());

            OrderDelivery orderDelivery = new OrderDelivery();
            BeanUtils.copyProperties(orderDeliveryDto,orderDelivery);
            for (DeliverTypeEnum deliverTypeEnum : DeliverTypeEnum.values()) {
                if(deliverTypeEnum.getCode() == orderDeliveryDto.getDeliveryType()){
                    orderDelivery.setDeliveryType(deliverTypeEnum);
                    break;
                }
            }
            orderDelivery.setDeliveryCompany(orderDeliveryDto.getDeliveryCompany());
            orderDelivery.setDeliverySn(orderDeliveryDto.getDeliverySn());
            orderDelivery.setDeliverySnShow(orderDeliveryDto.getDeliverySnShow());
            orderDelivery.setDeliveryCode(orderDeliveryDto.getDeliveryCode());
            orderDelivery.setReceived(false);
            orderDelivery.setOrderDeliveryProxyId(orderDeliveryDto.getOrderDeLiveryProxyId());
            orderDelivery.setWarehouseId(orderDeliveryDto.getWarehouseId());
            int i1 = orderDeliveryMapper.insert(orderDelivery);


            List<OrderDeliveryItemDto> itemList = orderDeliveryDto.getItemList();
            for (OrderDeliveryItemDto orderDeliveryItemDto : itemList) {
                OrderDeliveryItem orderDeliveryItem = new OrderDeliveryItem();
                BeanUtils.copyProperties(orderDeliveryItemDto,orderDeliveryItem);
                orderDeliveryItem.setMainId(orderDelivery.getId());
                orderDeliveryItem.setOrderId(orderId);
                orderDeliveryItem.setOrderDeliveryProxyId(orderDeliveryItemDto.getOrderDeLiveryProxyId());
                orderDeliveryItem.setOrderDeliveryProxyItemId(orderDeliveryItemDto.getOrderDeliveryProxyItemId());
                orderDeliveryItemMapper.insert(orderDeliveryItem);

                //更新代发货明细待发货数量
                if(orderDeliveryItemDto.getOrderDeliveryProxyItemId()!=null
                        &&!orderDeliveryItemDto.getOrderDeliveryProxyItemId().equals("")){
                    OrderDeliveryProxyItem orderDeliveryProxyItem = orderDeliveryProxyItemService.getById(orderDeliveryItemDto.getOrderDeliveryProxyItemId());
                    orderDeliveryProxyItem.setUnDeliveryQuantity(orderDeliveryProxyItem.getUnDeliveryQuantity()-orderDeliveryItemDto.getProductQuantity());
                    orderDeliveryProxyItemService.updateById(orderDeliveryProxyItem);
                }

                Long orderItemId = orderDeliveryItemDto.getOrderItemId();

                //更新订单明细待发货数
                OrderItem orderItem = orderItemMapper.selectById(orderItemId);
                orderItem.setUnDeliveryQuantity(orderItem.getUnDeliveryQuantity()-orderDeliveryItemDto.getProductQuantity());
                orderItemMapper.updateById(orderItem);
            }

            //更新待发货订单状态
            if(orderDeliveryDto.getOrderDeLiveryProxyId()!=null){
                OrderDeliveryProxy orderDeliveryProxy = orderDeliveryProxyService.getById(orderDeliveryDto.getOrderDeLiveryProxyId());
                if(orderDeliveryProxy!=null){
                    LambdaQueryWrapper<OrderDeliveryProxyItem>wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(OrderDeliveryProxyItem::getMainId,orderDeliveryProxy.getId());
                    List<OrderDeliveryProxyItem> list = orderDeliveryProxyItemService.list(wrapper);
                    if(list!=null&&list.size()>0){
                        Boolean b = true;
                        for (OrderDeliveryProxyItem orderDeliveryProxyItem : list) {
                            Integer unDeliveryQuantity = orderDeliveryProxyItem.getUnDeliveryQuantity();
                            if(unDeliveryQuantity>0){
                                b = false;
                                break;
                            }
                        }
                        if(b){
                            orderDeliveryProxy.setDeliveryStatus(DeliveryStatusEnum.YES);
                            orderDeliveryProxyService.updateById(orderDeliveryProxy);
                        }
                    }

                }
            }

            //判断是否更新订单状态为待收货
            LambdaQueryWrapper<OrderItem>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OrderItem::getOrderId,orderId);
            List<OrderItem> orderItemList = orderItemMapper.selectList(wrapper);
            Boolean b = true;
            if(orderItemList!=null&&orderItemList.size()>0){
                for (OrderItem orderItem : orderItemList) {
                    Integer unDeliveryQuantity = orderItem.getUnDeliveryQuantity();
                    if(unDeliveryQuantity!=0){
                        b = false;
                        break;
                    }
                }
            }else{
                b = false;
            }

            if(b){
                orderDto.setStatus(OrderStatusEnum.WAIT_FOR_PICKUP.getCode());
            }
            orderDto.setUpdateTime(LocalDateTime.now());
            int i = baseMapper.updateOneById(orderDto);

            //查询会员持有的积分、收货地址
            AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(orderDto.getUserId(),
                    Collections.singletonList(4));
            OrderVo vo = orderMapper.selectOrderVoById(orderDto.getId());
            //小程序下单发送订阅消息
            //旧程序发货消息-先去掉
            //if(accountInfoDto!=null&&accountInfoDto.getMiniAccountOauths()!=null){
            //    sender.sendDeliveryMessage(vo, accountInfoDto.getMiniAccountOauths().getOpenId());
            //}
            sender.sendShippedOrderMessage(vo);

            BaseOrderMessage baseOrderMessage = new BaseOrderMessage();
            baseOrderMessage.setOrderId(vo.getId());
            baseOrderMessage.setShopId(vo.getShopId());
            baseOrderMessage.setTenantId(vo.getTenantId());
            sender.sendAutoReceiptOrderMessage(baseOrderMessage,
                    orderSetting.getConfirmOvertime() * TimeConstants.ONE_DAY);
            res.set(i + i1);
        });
        return res.get();
    }


    @Override
    public List<ProductBuyerVo> getProductLastBuyers(String[] productIds) {
        List<ProductBuyerVo> vos = new ArrayList<>();
        for (String productId : productIds) {
            ProductBuyerVo vo = new ProductBuyerVo();
            vo.setProductId(productId);
            List<BuyerVo> buyerVos = baseMapper.getProductLastBuyers(productId);
            vo.setBuyerList(buyerVos);
            vos.add(vo);
        }
        return vos;
    }

    /**
     * waitSendProduct
     *
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @Date 2020/6/20 11:06 AM
     */
    @Override
    public List<Long> waitSendProductList(String sendBillId) {
        return baseMapper.waitSendProductList(sendBillId);
    }

    @Override
    public OrderSetting getOrderSetting() {
        return orderSettingMapper.selectOne(null);
    }


    @Override
    public List<OrderVo> orderInfoList(List<Long> orderIds) {
        if (CollUtil.isEmpty(orderIds)) {
            return CollUtil.newArrayList();
        }
        List<OrderVo> vos = baseMapper.selectOrderVoListByIds(orderIds);
        return vos;
    }


    @Override
    public Long createExchangeOrder(ExchangeOrderDto dto) {
        OrderVo orderVo = baseMapper.selectOrderVoById(dto.getOrderId());
        //生成订单
        Order exchangeOrder = new Order();

        exchangeOrder.setId(IdWorker.getId());
        exchangeOrder.setUserId(orderVo.getUserId());
        exchangeOrder.setUserName(orderVo.getUserName());
        exchangeOrder.setUserAvatarUrl(orderVo.getUserAvatarUrl());
        exchangeOrder.setUserNote("");
        exchangeOrder.setType(OrderTypeEnum.EXCHANGE);
        exchangeOrder.setTotalAmount(BigDecimal.ZERO);
        exchangeOrder.setDiscountsAmount(BigDecimal.ZERO);
        exchangeOrder.setPayAmount(BigDecimal.ZERO);
        exchangeOrder.setFreightAmount(BigDecimal.ZERO);
        exchangeOrder.setPromotionAmount(BigDecimal.ZERO);
        exchangeOrder.setCouponId(null);
        exchangeOrder.setPayType(PayTypeEnum.FREE);
        exchangeOrder.setTransactionId(null);
        exchangeOrder.setPayTime(LocalDateTime.now());
        exchangeOrder.setSourceType(orderVo.getSourceType());
        exchangeOrder.setStatus(OrderStatusEnum.WAIT_FOR_SEND);
        exchangeOrder.setNote("");
        exchangeOrder.setCustomForm(orderVo.getCustomForm());
        exchangeOrder.setRefundAmount(BigDecimal.ZERO);
        baseMapper.insert(exchangeOrder);


        //生成订单明细
        OrderItem orderItem =
                orderVo.getOrderItemList().stream()
                        .filter(o -> o.getProductSkuId().equals(dto.getProductSkuId()))
                        .findFirst().get();
        OrderItem exchangeOrderItem = new OrderItem();

        exchangeOrderItem.setOrderId(exchangeOrder.getId());
        exchangeOrderItem.setProductId(orderItem.getProductId());
        exchangeOrderItem.setProductPic(orderItem.getProductPic());
        exchangeOrderItem.setProductName(orderItem.getProductName());
        exchangeOrderItem.setProductSn(orderItem.getProductSn());
        exchangeOrderItem.setProductPrice(orderItem.getProductPrice());
        exchangeOrderItem.setProductOriginalPrice(orderItem.getProductOriginalPrice());
        exchangeOrderItem.setProductQuantity(dto.getProductQuantity());
        exchangeOrderItem.setProductSkuId(orderItem.getProductSkuId());
        exchangeOrderItem.setProductSkuCode(orderItem.getProductSkuCode());
        exchangeOrderItem.setPromotionAmount(BigDecimal.ZERO);
        exchangeOrderItem.setCouponAmount(BigDecimal.ZERO);
        exchangeOrderItem.setRealAmount(orderItem.getRealAmount());
        exchangeOrderItem.setSpecs(orderItem.getSpecs());
        exchangeOrderItem.setProviderId(orderItem.getProviderId());
        orderItemMapper.insert(exchangeOrderItem);

        //生成订物流信息
        OrderDelivery orderDelivery =
                orderVo.getOrderDelivery();
        OrderDelivery exchangeOrderDelivery = new OrderDelivery();
        exchangeOrderDelivery.setOrderId(exchangeOrder.getId());
        exchangeOrderDelivery.setDeliveryType(orderDelivery.getDeliveryType());
        exchangeOrderDelivery.setReceiverName(orderDelivery.getReceiverName());
        exchangeOrderDelivery.setReceiverPhone(orderDelivery.getReceiverPhone());
        exchangeOrderDelivery.setReceiverPostCode(orderDelivery.getReceiverPostCode());
        exchangeOrderDelivery.setReceiverProvince(orderDelivery.getReceiverProvince());
        exchangeOrderDelivery.setReceiverCity(orderDelivery.getReceiverCity());
        exchangeOrderDelivery.setReceiverRegion(orderDelivery.getReceiverRegion());
        exchangeOrderDelivery.setReceiverDetailAddress(orderDelivery.getReceiverDetailAddress());
        exchangeOrderDelivery.setDeliveryTemplateId(orderDelivery.getDeliveryTemplateId());
        orderDeliveryMapper.insert(exchangeOrderDelivery);
        return exchangeOrder.getId();
    }

    @Override
    public void closeOrder(Long afsId, BigDecimal refundAmount, Integer type, Long orderId) {
        OrderVo order = baseMapper.selectOrderVoById(orderId);
        log.info("订单数据为" + JSON.toJSONString(order));
        AfsOrder afsOrder = afsOrderMapper.selectById(afsId);


        //此次退款金额
        BigDecimal A = refundAmount;
        //订单已退款金额
        BigDecimal B1 = order.getRefundAmount();
        if(B1 == null){
            B1 = BigDecimal.ZERO;
        }
        order.setRefundAmount(NumberUtil.add(order.getRefundAmount(), refundAmount));
        //累计退款金额
        BigDecimal B2 = order.getRefundAmount();
        //现金金额
        BigDecimal C = order.getCashAmount();
        //佣金支付金额
        BigDecimal D = order.getCommissionAmount();
        //金豆支付金额
        BigDecimal E = order.getGoldenAmount();

        if (NumberUtil.isLess(order.getPayAmount(), order.getRefundAmount())) {
            throw new ServiceException("退款金额不得大于支付金额");
        }
        Integer refundQuantity = 0;

        LambdaQueryWrapper<AfsOrderItem>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AfsOrderItem::getDeleted, CommonConstants.NUMBER_ZERO);
        wrapper.eq(AfsOrderItem::getAfsId,afsId);
        List<AfsOrderItem> afsOrderItemList = afsOrderItemMapper.selectList(wrapper);


        for (OrderItemVo orderItemVo : order.getOrderItemList()) {
            log.info("orderItemVo is {}", JSONUtil.toJsonStr(orderItemVo));
            log.info("afsOrder is {}", JSONUtil.toJsonStr(afsOrder));
            for (AfsOrderItem afsOrderItem : afsOrderItemList) {
                if (orderItemVo.getProductSkuId().equals(afsOrderItem.getProductSkuId())
                &&orderItemVo.getMemberTypeId().equals(afsOrderItem.getMemberTypeId())) {
                    orderItemVo.setRefundAmount(refundAmount);
                    Integer returnQuantity = orderItemVo.getReturnQuantity();
                    if(returnQuantity == null){
                        returnQuantity = 0;
                    }
                    orderItemVo.setReturnQuantity(returnQuantity+afsOrderItem.getProductQuantity());
                    orderItemMapper.updateById(orderItemVo);
                    refundQuantity = refundQuantity + orderItemVo.getProductQuantity();
                }
            }
        }
        order.setRefundQuantity(refundQuantity);

        //新增退款逻辑
        if(C.compareTo(B2)>=0){//如果C大于等于B2，则现金退还的退款金额为A
            //现金退还金额
            //微信支付
            if(order.getPayType().equals(PayTypeEnum.WECHAT)){
                payReturnWechat(refundAmount, order);
            }
            //第三方支付---后续

        }else if(C.compareTo(B2)<0&&C.compareTo(B1)>0){//如果C小于B2大于B1
            //现金退还的退款金额为C减B1的差值F1
            BigDecimal F1 = C.subtract(B1);
            //微信支付
            if(order.getPayType().equals(PayTypeEnum.WECHAT)){
                payReturnWechat(F1, order);
            }
            //第三方支付---后续


            //此时尚有A减F1的差值F2尚未退回
            BigDecimal F2 = A.subtract(F1);
            if(F2.compareTo(BigDecimal.ZERO)>0){
                //如果佣金支付金额D大于等于F2
                if(D.compareTo(F2)>=0){
                    //则将F2退回佣金账户余额
                    payRefundCommission(F2,order);
                }else if(D.compareTo(F2)<0){//如果佣金支付金额D小于F2
                    //计算F2减D的差值F3
                    BigDecimal F3 = F2.subtract(D);
                    //若佣金支付金额D大于0，则将佣金支付金额D退回佣金账户余额
                    if(D.compareTo(BigDecimal.ZERO)>0){
                        //佣金退还金额
                        payRefundCommission(D,order);
                    }
                    //将F3退回金豆账户余额
                    payRefundGolden(F3,order);
                }
            }
        }else if(C.compareTo(B1)<=0){//如果C小于等于B1，表示现金支付的金额在上笔退款已经退完，接下来判断C+D与B2的大小关系，这里不能只判断D与A的关系，因为D可能也被退回了一部分
            //如果C+D大于等于B2
            if((C.add(D)).compareTo(B2)>=0){
                //佣金退还的退款金额为A
                payRefundCommission(A,order);
            }else if((C.add(D)).compareTo(B2)<0&&(C.add(D)).compareTo(B1)>0){//如果C+D小于B2大于B1
                //佣金退还的退款金额为C+D-B1的差值G1
                BigDecimal G1 = C.add(D).subtract(B1);
                payRefundCommission(G1,order);
                //此时尚有A减G1的差值G2尚未退回
                BigDecimal G2 = A.subtract(G1);
                //此时如果金豆支付金额E大于等于G2
                if(E.compareTo(G2)>=0){
                    //G2退回金豆账户余额
                    payRefundGolden(G2,order);
                }else if(E.compareTo(G2)<0){//金豆支付金额E小于G2
                    //E退回金豆账户余额
                    payRefundGolden(E,order);
                }
            }else if((C.add(D)).compareTo(B1)<=0){//如果C+D小于等于B1，表示现金支付+佣金支付的金额在上笔退款已经退完，接下来判断C+D+E与B2的大小关系
                //C+D+E大于等于B2
                if(C.add(D).add(E).compareTo(B2)>=0){
                    //金豆退还的退款金额为A
                    payRefundGolden(A,order);
                }else if(C.add(D).add(E).compareTo(B2)<0&&C.add(D).add(E).compareTo(B1)>0){//C+D+E小于B2大于B1

                    //则金豆退还的退款金额为C+D+E-B1的差值H1
                    BigDecimal H1 = C.add(D).add(E).subtract(B1);
                    payRefundGolden(H1,order);
                    //此时尚有A减H1的差值H2尚未退回，但是已没有余额可退，退出退款逻辑
                    BigDecimal H2 = A.subtract(H1);
                    if(H2.compareTo(BigDecimal.ZERO)>0){
                        log.info("订单{},剩余未支付金额{}未退，但是已没有余额可退，退出退款逻辑",order,H2);
                    }
                }else if(C.add(D).add(E).compareTo(B1)<=0){
                    log.info("订单{},表示金额已退完，无余额可退",order);
                }
            }
        }

        //针于退款，只要没钱了就关闭订单
        if (type.equals(AfsOrderTypeEnum.REFUND.getCode()) || type.equals(AfsOrderTypeEnum.RETURN_REFUND.getCode())) {
            BigDecimal payAmount = NumberUtil.sub(order.getPayAmount(), order.getFreightAmount());
            if (NumberUtil.equals(refundAmount, payAmount) || NumberUtil.equals(refundAmount,
                    order.getPayAmount()) || NumberUtil.equals(order.getRefundAmount(), order.getPayAmount())) {
                sender.sendCloseOrderMessage(order);
                order.setStatus(OrderStatusEnum.REFUNDED);
                order.setCloseTime(LocalDateTime.now());
            }else{
                Boolean b = true;
                List<OrderItemVo> orderItemList = order.getOrderItemList();
                if(orderItemList!=null&&orderItemList.size()>0){
                    for (OrderItem orderItem : orderItemList) {
                        Integer unDeliveryQuantity = orderItem.getUnDeliveryQuantity();
                        Integer returnQuantity = orderItem.getReturnQuantity();
                        if(returnQuantity == null){
                            returnQuantity = 0;
                        }
                        if(unDeliveryQuantity-returnQuantity!=0){
                            b = false;
                            break;
                        }
                    }
                }else{
                    b = false;
                }
                if(b){
                    order.setStatus(OrderStatusEnum.WAIT_FOR_PICKUP);
                    order.setUpdateTime(LocalDateTime.now());
                }
            }
            baseMapper.updateById(order);

        }
        sender.sendReturnOrderMessage(order);
        sender.sendRefundSuccess(order);
    }

    /**
     * 现金退款
     * @param refundAmount
     * @param order
     */
    private void payRefund(BigDecimal refundAmount, Order order) {
        log.info("订单{},本次退款现金金额{}",order.getId(),refundAmount);
        if (NumberUtil.isGreater(refundAmount, BigDecimal.ZERO)) {
            switch (order.getPayType()) {
                case WECHAT:
                case FRIEND:
                    wechatPayRefund(refundAmount, order);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 佣金退还
     * @param refundAmount
     * @param order
     */
    private void payRefundCommission(BigDecimal refundAmount, Order order){
        log.info("订单{},本次退款佣金金额{}",order.getId(),refundAmount);
        if (NumberUtil.isGreater(refundAmount, BigDecimal.ZERO)) {
            AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(order.getUserId(), Arrays.asList(1, 2,
                    3, 5));
            AccountReturnBalanceMessage accountReturnBalanceMessage = new AccountReturnBalanceMessage();
            accountReturnBalanceMessage.setTenantId(TenantContextHolder.getTenantId());
            accountReturnBalanceMessage.setPayCommission(refundAmount);
            accountReturnBalanceMessage.setPayGolden(BigDecimal.ZERO);
            accountReturnBalanceMessage.setOrderId(order.getId()+"");
            accountReturnBalanceMessage.setUserId(accountInfoDto.getMiniAccountunt().getUserId());
            accountReturnBalanceMessage.setCancelOrder(CommonConstants.NUMBER_ONE);
            sender.sendRevertMiniAccountBalanceMessage(accountReturnBalanceMessage);
        }
    }

    /**
     * 金豆退还
     * @param refundAmount
     * @param order
     */
    private void payRefundGolden(BigDecimal refundAmount, Order order){
        log.info("订单{},本次退款金豆金额{}",order.getId(),refundAmount);
        if (NumberUtil.isGreater(refundAmount, BigDecimal.ZERO)) {
            AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(order.getUserId(), Arrays.asList(1, 2,
                    3, 5));
            AccountReturnBalanceMessage accountReturnBalanceMessage = new AccountReturnBalanceMessage();
            accountReturnBalanceMessage.setTenantId(TenantContextHolder.getTenantId());
            accountReturnBalanceMessage.setPayCommission(BigDecimal.ZERO);
            accountReturnBalanceMessage.setPayGolden(refundAmount);
            accountReturnBalanceMessage.setOrderId(order.getId()+"");
            accountReturnBalanceMessage.setUserId(accountInfoDto.getMiniAccountunt().getUserId());
            accountReturnBalanceMessage.setCancelOrder(CommonConstants.NUMBER_ONE);
            sender.sendRevertMiniAccountBalanceMessage(accountReturnBalanceMessage);
        }
    }

    /**
     * 微信支付退回
     * @param refundAmount
     * @param order
     */
    private void payReturnWechat(BigDecimal refundAmount, Order order){
        log.info("订单{},本次微信退款金额{}",order.getId(),refundAmount);
        if (NumberUtil.isGreater(refundAmount, BigDecimal.ZERO)) {
            RefundRequestWechatMessage message = new RefundRequestWechatMessage();
            message.setTenantId(order.getTenantId());
            message.setOrderId(order.getTransactionId());
            message.setRouteKey(OrderQueueNameConstant.REFUND_NOTIFY);
            message.setTotalFee(NumberUtil.mul(refundAmount, 100).intValue());
            sender.sendRefundRequestWechatMessage(message);
        }
    }


    private void wechatPayRefund(BigDecimal refundAmount, Order order) {
        RefundRequestDto dto = new RefundRequestDto();
        try {
            dto.setOrderId(order.getTransactionId());
            dto.setRouteKey(OrderQueueNameConstant.REFUND_NOTIFY);
            dto.setTotalFee(NumberUtil.mul(refundAmount, 100).intValue());
            Result result = remotePaymentService.payRefund(dto);
            log.info("微信退款,入参为: {}", JSONUtil.toJsonStr(dto));
            log.info("微信退款,出参为: {}", JSONUtil.toJsonStr(result));
        } catch (Exception e) {
            log.error("调用退款接口失败：入参为：{}", JSONUtil.toJsonStr(dto));
            log.error(e.getLocalizedMessage());
            e.printStackTrace();
        }
    }


    @Override
    public void closeExchangeOrder(List<Long> orderIds) {
        if (CollUtil.isNotEmpty(orderIds)) {
            baseMapper.closeExchangeOrder(orderIds, OrderStatusEnum.EXCHANGE_CANCEL_CLOSE);
        }
    }

    @Override
    public AfsOrder selectByOrderIdAndProductSkuId(Long orderId, Long productSkuId) {
        AfsOrder afsOrder = afsOrderMapper.selectByOrderIdAndProductSkuId(orderId, productSkuId,null);
        if (ObjectUtil.isNotNull(afsOrder)) {
            return afsOrder;
        } else {
            return null;
        }
    }

    @Override
    public Boolean checkAfsOrder(List<Long> orderIds) {
        if (CollUtil.isEmpty(orderIds)) {
            return Boolean.TRUE;
        }
        Integer size = afsOrderMapper.selectCountProgressByOrderIds(orderIds);
        return size < 1;
    }

    @Override
    public Boolean updateOrderStatus(List<Long> orderIds, OrderStatusEnum statusEnum) {
        if (orderIds.isEmpty()) {
            return Boolean.TRUE;
        }
        Order order = new Order();
        order.setStatus(statusEnum);
        baseMapper.update(order, new LambdaQueryWrapper<Order>().in(Order::getId, orderIds));
        return Boolean.TRUE;
    }

    @Override
    public Boolean updateWxDeliverStatus(String orderId, WxDeliverStatusEnum wxDeliverStatusEnum) {
        if (orderId.isEmpty()) {
            return Boolean.TRUE;
        }
        Order order = new Order();
        order.setWxDeliverStatusEnum(wxDeliverStatusEnum);
        baseMapper.update(order, new LambdaQueryWrapper<Order>().eq(Order::getId, orderId));
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateOrderPrintTime(Long orderId) {
        Order order = getById(orderId);
        Integer printTime = order.getPrintTime();
        order.setPrintTime(printTime+1);
        updateById(order);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void agreeOrderReturn(CloseOrderMessage message) {
        String tenantId = message.getTenantId();
        List<CloseOrderDto> list = message.getList();
        List<CloseExchangeOrderDto> closeExchangeOrderList = message.getCloseExchangeOrderList();
        TenantContextHolder.setTenantId(tenantId);

        //申请售后成功关闭之前的换货单
        if(closeExchangeOrderList!=null&&closeExchangeOrderList.size()>0){
            for (CloseExchangeOrderDto closeExchangeOrderDto : closeExchangeOrderList) {
                List<Long> orderIds = closeExchangeOrderDto.getOrderIds();
                closeExchangeOrder(orderIds);
            }
        }

        if(list!=null&&list.size()>0){
            //调用退款流程
            for (CloseOrderDto closeOrderDto : list) {
                closeOrder(closeOrderDto.getAfsId(),closeOrderDto.getRefundAmount(),closeOrderDto.getType(),closeOrderDto.getOrderId());
                Order order = this.getById(closeOrderDto.getOrderId());
                log.info("order is {}", JSONUtil.toJsonStr(order));
                if (OrderStatusEnum.isClose(order.getStatus())) {
                    closeReceipt(closeOrderDto.getOrderId(), closeOrderDto.getAfsId());
                }
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exchangeOrderReturn(CloseExchangeOrderMessage message) {
        String tenantId = message.getTenantId();
        List<CloseExchangeOrderDto> closeExchangeOrderList = message.getCloseExchangeOrderList();
        Long afsOrderId = message.getAfsOrderId();
        TenantContextHolder.setTenantId(tenantId);
        if(closeExchangeOrderList!=null&&closeExchangeOrderList.size()>0){
            for (CloseExchangeOrderDto closeExchangeOrderDto : closeExchangeOrderList) {
                List<Long> orderIds = closeExchangeOrderDto.getOrderIds();
                closeExchangeOrder(orderIds);
                for (Long orderId : orderIds) {
                    //关闭签收单
                    closeReceipt(orderId, afsOrderId);
                }
            }
        }

    }

    //订单移出发货单
    private void closeReceipt(Long orderId, Long afsId) {
        List<AfsOrder> afsOrderList = remoteAfsService.selectProgressByOrderIdAndIdNotIn(orderId, afsId);
        if (afsOrderList.isEmpty()) {
            AfsRemoveDeliverOrderMessage message = new AfsRemoveDeliverOrderMessage();
            message.setOrderId(orderId);
            sender.sendRemoveSendBillOrderMessage(message);
        }
    }

}
