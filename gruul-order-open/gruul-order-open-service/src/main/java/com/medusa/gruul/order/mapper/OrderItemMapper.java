package com.medusa.gruul.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.goods.api.param.OperateStockDto;
import com.medusa.gruul.order.api.entity.OrderItem;
import com.medusa.gruul.order.model.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 订单中所包含的商品 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019 -09-02
 */
public interface OrderItemMapper extends BaseMapper<OrderItem> {

    /**
     * selectListByOrderId
     *
     * @param orderId the order id
     * @return java.util.List<com.medusa.gruul.order.model.SimpleOrderItemVo> list
     * <AUTHOR>
     * @date 2019 /11/23 16:05
     */
    List<SimpleOrderItemVo> selectSimpleOrderItemVoByOrderId(@Param(value = "orderId") Long orderId);

    /**
     * 根据商品id，商品规格id获取代发货订单详情
     * @return
     */
    List<SimpleOrderItemVo>selectSimpleOrderItem(@Param(value = "param") BatchDeliveryProxyItemVo param);

    /**
     * selectByOrderId
     *
     * @param orderId the order id
     * @return java.util.List<com.medusa.gruul.order.api.entity.OrderItem> list
     * <AUTHOR>
     * @date 2019 /12/1 17:04
     */
    List<OrderItem> selectByOrderId(@Param(value = "orderId") Long orderId);

    /**
     * 根据订单ID查询订单包含的商品
     *
     * @param orderIds the order ids
     * @return java.util.List<com.medusa.gruul.order.api.model.ItemDto> list
     * <AUTHOR>
     * @date 2019 /11/28 21:06
     */
    List<OperateStockDto> selectItemDtoByOrderIds(@Param(value = "orderIds") List<Long> orderIds);

    /**
     * countSkuPurchased
     *
     * @param productSkuId the product sku id
     * @param userId       the user id
     * @return java.lang.Integer integer
     * <AUTHOR>
     * @date 2020 /2/13 21:56
     */
    Integer countSkuPurchased(@Param(value = "productSkuId") Long productSkuId, @Param(value = "userId") String userId);


    /**
     * 商品交易量排行
     * <AUTHOR>
     * @date 2022 /06/13 21:56
     * @return
     */
    List<ProductRankingDto> getProductVBRanking();
    /**
     * 商品交易额排行
     * <AUTHOR>
     * @date 2022 /06/14 21:56
     * @return
     */
    List<ProductRankingDto> getgvmSproductVBRanking();


    List<Long> getIdsByOrderHistory(String userId);

    /**
     * 根据订单id获取发货信息
     * @param orderId
     * @return
     */
    List<OrderItemDeliveryVo> getOrderItemDelivery(String orderId);

    /**
     * 分页查询未发货订单商品列表
     * @param param
     * @return
     */
    List<UnShippedOrderItemVo>getUnShippedOrderItem( @Param(value = "param")UnShippedOrderItemParam param);

    /**
     * 根据用户id，会员类型判断订单是否存在
     * @param shopUserId
     * @param memberTypeId
     * @return
     */
    Integer getCountByMemberType(@Param(value = "shopUserId")String shopUserId, @Param(value = "memberTypeId")Long memberTypeId,
                                 @Param(value = "statusList")List<Integer> statusList);
}
