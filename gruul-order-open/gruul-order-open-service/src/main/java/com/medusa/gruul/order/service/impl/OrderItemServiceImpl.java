package com.medusa.gruul.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.order.api.entity.OrderItem;
import com.medusa.gruul.order.api.enums.OrderStatusEnum;
import com.medusa.gruul.order.mapper.OrderItemMapper;
import com.medusa.gruul.order.model.ProductRankingDto;
import com.medusa.gruul.order.service.IOrderItemService;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.HashMap;
import java.util.List;

/**
 * <p>
 * 订单中所包含的商品 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019 -09-02
 */
@Service
public class OrderItemServiceImpl extends ServiceImpl<OrderItemMapper, OrderItem> implements IOrderItemService {


    @Override
    public HashMap<String,List<ProductRankingDto>> getProductRanking()  {
        //获取商品交易量排行
        List<ProductRankingDto> productVBRankingList= this.baseMapper.getProductVBRanking();
        //获取商品交易额排行
        List<ProductRankingDto> gvmSproductVBRankingList= this.baseMapper.getgvmSproductVBRanking();
        HashMap<String,List<ProductRankingDto>> map=new HashMap();
        map.put("productVBRankingList",productVBRankingList);
        map.put("gvmSproductVBRankingList",gvmSproductVBRankingList);
        return map;
    }

    @Override
    public List<Long> getIdsByOrderHistory(String userId) {

        return this.baseMapper.getIdsByOrderHistory(userId);
    }

    @Override
    public Integer getCountByMemberType(String userId, Long memberTypeId) {
        Integer count = this.baseMapper.getCountByMemberType(userId, memberTypeId, OrderStatusEnum.getPaidStatus());
        return count;
    }

}
