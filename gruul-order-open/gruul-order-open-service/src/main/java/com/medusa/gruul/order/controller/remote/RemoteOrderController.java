package com.medusa.gruul.order.controller.remote;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.medusa.gruul.afs.api.entity.AfsOrder;
import com.medusa.gruul.common.core.annotation.EscapeLogin;
import com.medusa.gruul.order.api.entity.*;
import com.medusa.gruul.order.api.enums.OrderStatusEnum;
import com.medusa.gruul.order.api.enums.WxDeliverStatusEnum;
import com.medusa.gruul.order.api.model.*;
import com.medusa.gruul.order.mapper.OrderProductEvaluateMapper;
import com.medusa.gruul.order.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Feign接口
 *
 * <AUTHOR>
 * @date 2019/12/16 19:41
 */
@RestController
@RequestMapping("/remote")
@Api(tags = "Feign接口")
public class RemoteOrderController {
    @Resource
    private IRemoteOrderService remoteOrderService;
    @Resource
    private IMiniOrderService miniOrderService;
    @Resource
    private IOrderDeliveryService orderDeliveryService;
    @Resource
    private IOrderItemService orderItemService;
    @Resource
    private IManageOrderService orderService;

    @Resource
    private IOrderProductEvaluateService orderProductEvaluateService;

    @Resource
    private IOrderOutStockService orderOutStockService;

    @Autowired
    private IOrderDeliveryProxyItemService orderDeliveryProxyItemService;
    /**
     * 订单详情
     *
     * @param orderId
     * @return com.medusa.gruul.common.core.util.Result<com.medusa.gruul.order.api.entity.Order>
     * <AUTHOR>
     * @Date 2019/9/9 23:06
     */
    @ApiOperation("订单详情")
    @EscapeLogin
    @GetMapping("/info")
    public OrderVo orderInfo(@RequestParam Long orderId) {
        return remoteOrderService.orderInfo(orderId);
    }

    /**
     * getOrderSetting
     *
     * @param
     * @return com.medusa.gruul.order.api.entity.OrderSetting
     * <AUTHOR>
     * @date 2020/8/17 22:46
     */
    @EscapeLogin
    @ApiOperation("获取订单设置信息")
    @GetMapping("/setting")
    public OrderSetting getOrderSetting() {
        return remoteOrderService.getOrderSetting();
    }

    /**
     * 获取商品评价
     *
     * <AUTHOR>
     * @date 2020/2/7 20:12
     */

    @EscapeLogin
    @ApiOperation("获取商品评价")
    @GetMapping("/product/rate")
    List<ProductRateVo> productRate(@RequestParam(value = "productIds") @NotNull List<Long> productIds) {
        if (productIds.isEmpty()) {
            return Collections.emptyList();
        }
        return remoteOrderService.productRate(productIds);
    }


    /**
     * 根据订单Id获取指定订单详情
     *
     * @param orderIds 订单集合
     * @return java.util.List<com.medusa.gruul.order.api.model.GetOrderListDto>
     * <AUTHOR>
     * @date 2019/12/12 20:53
     */
    @EscapeLogin
    @ApiOperation("根据订单Id获取指定订单详情")
    @GetMapping(value = "/get/orders")
    public List<GetOrderListDto> getOrderListByIds(@RequestParam Long[] orderIds) {
        GetOrderListParam param = new GetOrderListParam(Arrays.asList(orderIds));
        return remoteOrderService.getOrderListByIds(param);
    }


    @EscapeLogin
    @ApiOperation("物流订单发货")
    @GetMapping("/items/delivery")
    public int doLogisticsOrderDelivery(@RequestParam(value = "orderDeliveryDtoList") String orderDeliveryDtoList) {
        List<OrderDeliveryDto> orderDeliveryDtos = JSON.parseArray(orderDeliveryDtoList, OrderDeliveryDto.class);
        if (null == orderDeliveryDtos || 0 == orderDeliveryDtos.size()) {
            return 0;
        }
        return remoteOrderService.doLogisticsOrderDelivery(orderDeliveryDtos);
    }


    /**
     * 批量获取商品的最后购买人信息
     *
     * @param productIds
     * @return java.util.List<com.medusa.gruul.order.api.model.ProductBuyerVo>
     * <AUTHOR>
     * @date 2020/5/7 23:15
     */
    @EscapeLogin
    @ApiOperation("批量获取商品的最后购买人信息")
    @GetMapping("/product/last/buyers")
    List<ProductBuyerVo> getProductLastBuyers(@RequestParam(value = "productIds") String[] productIds) {
        return remoteOrderService.getProductLastBuyers(productIds);
    }


    /**
     * 用户订单概况，需要在header带上商户信息和用户信息
     *
     * @param
     * @return com.medusa.gruul.order.api.model.OrderOverviewVo
     * <AUTHOR>
     * @date 2020/5/13 20:54
     */
    @EscapeLogin
    @ApiOperation(value = "用户订单概况", notes = "用户订单概况")
    @GetMapping("/overview")
    public OrderOverviewVo orderOverview(@RequestParam(value = "userId") String userId) {
        OrderOverviewVo orderOverviewVo = miniOrderService.orderOverview(userId);
        return orderOverviewVo;
    }

    @EscapeLogin
    @ApiOperation(value = "查询待发货订单包含的商品ID")
    @GetMapping("/product/wait/send")
    public List<Long> waitSendProduct(@RequestParam(value = "sendBillId") String sendBillId) {
        if (StrUtil.isBlank(sendBillId)) {
            return CollUtil.newArrayList();
        }
        List<Long> productIds = remoteOrderService.waitSendProductList(sendBillId);
        return productIds;
    }


    /**
     * 批量查询多个订单详情
     *
     * @param orderIds
     * @return com.medusa.gruul.common.core.util.Result<com.medusa.gruul.order.api.entity.Order>
     * <AUTHOR>
     * @Date 2019/9/9 23:06
     */
    @EscapeLogin
    @ApiOperation(value = "批量查询多个订单详情")
    @GetMapping("/info/list")
    public List<OrderVo> orderInfoList(@RequestParam(value = "orderIds") List<Long> orderIds) {
        return remoteOrderService.orderInfoList(orderIds);
    }

    /**
     * createExchangeOrder
     *
     * @param dto
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2020/8/23 13:00
     */
    @EscapeLogin
    @ApiOperation(value = "创建换货单订单")
    @PostMapping("/exchange/order")
    public Long createExchangeOrder(@RequestBody ExchangeOrderDto dto) {
        return remoteOrderService.createExchangeOrder(dto);
    }

    /**
     * 订单退款
     *
     * @param refundAmount
     * @param orderId
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2020/8/23 12:56
     */
    @EscapeLogin
    @ApiOperation(value = "订单退款")
    @PutMapping("/close")
    public void closeOrder(
            @RequestParam(value = "afsId") Long afsId,
            @RequestParam(value = "refundAmount") BigDecimal refundAmount,
            @RequestParam(value = "type") Integer type,
            @RequestParam(value = "orderId") Long orderId) {
        remoteOrderService.closeOrder(afsId, refundAmount, type, orderId);

    }

    /**
     * 签收订单
     *
     * @param orderId
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2020/8/23 12:56
     */
    @EscapeLogin
    @ApiOperation(value = "签收订单")
    @PutMapping("/receipt")
    public void receiptOrder(
            @RequestParam(value = "orderId") Long orderId) {
        miniOrderService.receiptOrder(orderId, true);
    }

    /**
     * 申请售后成功关闭之前的换货单
     *
     * @param orderIds
     * @return void
     * <AUTHOR>
     * @date 2020/9/1 20:29
     */
    @EscapeLogin
    @ApiOperation(value = "申请售后成功关闭之前的换货单")
    @PutMapping("/close/exchange")
    public void closeExchangeOrder(@RequestBody List<Long> orderIds) {
        remoteOrderService.closeExchangeOrder(orderIds);

    }

    /**
     * 根据订单ID和SkuId查询指定商品有没有售后信息
     *
     * @param orderId
     * @param productSkuId
     * @return void
     * <AUTHOR>
     * @date 2020/10/5 10:02
     */
    @EscapeLogin
    @ApiOperation(value = "根据订单ID和SkuId查询指定商品有没有售后信息")
    @GetMapping("/{orderId}/{productSkuId}/afsOrder/")
    public AfsOrder selectByOrderIdAndProductSkuId(@PathVariable(value = "orderId") Long orderId,
                                                   @PathVariable(value = "productSkuId") Long productSkuId) {
        return remoteOrderService.selectByOrderIdAndProductSkuId(orderId, productSkuId);
    }


    /**
     * 检查订单是否有真正进行中的售后
     *
     * @param orderIds the order ids
     */
    @EscapeLogin
    @ApiOperation(value = "检查订单是否有真正进行中的售后")
    @GetMapping("/checkAfsOrder")
    public Boolean checkAfsOrder(@RequestParam(value = "orderIds") List<Long> orderIds) {
        return remoteOrderService.checkAfsOrder(orderIds);
    }

    /**
     * 更新订单到指定状态
     *
     * @param orderIds the order ids
     */
    @EscapeLogin
    @ApiOperation(value = "更新订单到指定状态")
    @PutMapping("/order/status")
    public Boolean updateOrderStatus(@RequestParam(value = "orderIds") List<Long> orderIds,
                                     @RequestParam(value = "statusEnum") OrderStatusEnum statusEnum) {
        return remoteOrderService.updateOrderStatus(orderIds, statusEnum);
    }

    /**
     * 更新订单打印次数
     * @param orderId
     * @return
     */
    @EscapeLogin
    @ApiOperation(value = "更新订单打印次数")
    @PutMapping("/order/printTime")
    public Boolean updateOrderPrintTime(@RequestParam(value = "orderId") Long orderId) {
        return remoteOrderService.updateOrderPrintTime(orderId);
    }

    /**
     *
     * @param orderId
     * @param wxDeliverStatusEnum
     * @return
     */
    @EscapeLogin
    @ApiOperation(value = "更新订单微信发货管理状态")
    @PutMapping("/order/wxDeliverStatus")
    public Boolean updateWxDeliverStatus(@RequestParam(value = "orderId") String orderId,
                                         @RequestParam(value = "statusEnum") WxDeliverStatusEnum wxDeliverStatusEnum) {
        return remoteOrderService.updateWxDeliverStatus(orderId, wxDeliverStatusEnum);
    }
    /**
     *
     *
     * @param orderId
     * @return OrderItem
     * <AUTHOR>
     * @date 2022 /5/31 15:56
     */
    @EscapeLogin
    @ApiOperation(value = "根据订单id查询订单明细")
    @PostMapping("/orderItemByOrderIds")
    public  List<OrderItem> orderItemByOrderIds(@RequestParam(value = "orderIds") Long orderId) {
        LambdaQueryWrapper<OrderItem> lambdaQueryWrapper=new LambdaQueryWrapper<OrderItem>();
        lambdaQueryWrapper.in(OrderItem::getOrderId,orderId);
        List<OrderItem> orderItemList=orderItemService.list(lambdaQueryWrapper);
        return orderItemList;
    }
    /**
     * 修改订单的仓库
     *
     * @param orderId
     * @return OrderItem
     * <AUTHOR>
     * @date 2022 /5/31 15:56
     */
    @EscapeLogin
    @ApiOperation(value = "修改订单的仓库")
    @PostMapping("/updateOrderWarehouse")
    Boolean updateOrderWarehouse(@RequestParam(value = "orderId") Long orderId,@RequestParam(value = "warehouseId") Long warehouseId){
        LambdaQueryWrapper<Order> lambdaQueryWrapper=new LambdaQueryWrapper<Order>();
        lambdaQueryWrapper.eq(Order::getId,orderId);
        Order order =remoteOrderService.getOne(lambdaQueryWrapper);
        order.setWarehouseId(warehouseId);
        return remoteOrderService.updateById(order);
    }
    /**
     * 根据订单id查询订单
     *
     * @param orderId
     * @return OrderItem
     * <AUTHOR>
     * @date 2022 /5/31 15:56
     */
    @EscapeLogin
    @ApiOperation(value = "根据订单id查询订单")
    @PostMapping("/getOrderById")
    Order getOrderById(@RequestBody Long orderId){
        Order order=orderService.getById(orderId);
        return order;
    }

    /**
     * 根据商品id查询是否有订单
     * @param ids
     * @return
     */
    @EscapeLogin
    @ApiOperation(value = "根据商品id查询是否有订单")
    @GetMapping("/getIsOrderByProductId")
    Boolean getIsOrderByProductId(@RequestParam("ids") Long[] ids){
        QueryWrapper<OrderItem> orderItemQueryWrapper = new QueryWrapper<>();
        orderItemQueryWrapper.lambda().in(OrderItem::getProductId,ids);
        int count = orderItemService.count(orderItemQueryWrapper);
        return count>0;
    }

    /**
     * 根据商品id查询是否有评论
     * @param ids
     * @return
     */
    @EscapeLogin
    @ApiOperation(value = "根据商品id查询是否有评论")
    @GetMapping("/getIsEvaluateByProductId")
    Boolean getIsEvaluateByProductId(@RequestParam("ids") Long[] ids){
        QueryWrapper<OrderProductEvaluate> orderProductEvaluateQueryWrapper = new QueryWrapper<>();
        orderProductEvaluateQueryWrapper.lambda().in(OrderProductEvaluate::getProductId,ids);
        int count = orderProductEvaluateService.count(orderProductEvaluateQueryWrapper);
        return count>0;
    }

    /**
     * 获取历史订单商品id
     * @return
     */
    @EscapeLogin
    @ApiOperation(value = "获取历史订单商品id")
    @GetMapping("/getIdsByOrderHistory")
    List<Long>getIdsByOrderHistory(@RequestParam("userId") String userId){
        return orderItemService.getIdsByOrderHistory(userId);
    }
    /**
     * 获取用户兑换商品数
     * @return
     */
    @EscapeLogin
    @ApiOperation(value = "获取用户兑换商品数")
    @GetMapping("/getUserBuyNumByUserId")
    BigDecimal getUserBuyNumByUserId(@RequestParam("userId")String userId,@RequestParam("skuId")String skuId,@RequestParam("productId")Long productId){
        return miniOrderService.getUserExchangeNumByUserId(userId,skuId,productId);
    }

    /**
     * 获取发货信息
     * @param orderId
     * @return
     */
    @EscapeLogin
    @ApiOperation(value = "获取发货信息")
    @GetMapping("/getOrderDelivery")
    OrderDelivery getOrderDelivery(@RequestParam("orderId") String orderId){
        return orderDeliveryService.getOrderDelivery(orderId);
    }
    /**
     * 获取未完成微信收货订单
     * @return
     */
    @EscapeLogin
    @ApiOperation(value = "获取未完成微信收货订单")
    @GetMapping("/getOrderByWxDeliverStatus")
    List<Order> getOrderByWxDeliverStatus(){
        return orderService.getOrderByWxDeliverStatus();
    }

    /**
     * 获取复购人数
     * @param startDate
     * @param endDate
     * @return
     */
    @EscapeLogin
    @ApiOperation(value = "获取复购人数")
    @GetMapping("/getAgainBuyCustom")
    Integer getAgainBuyCustom(@RequestParam("startDate")String startDate,@RequestParam("endDate")String endDate){
        return orderService.getAgainBuyCustom(startDate,endDate);
    }

    /**
     * 获取订单商品计算的佣金
     * @param orderId 订单id
     * @param type 0 上级佣金，1上上级佣金
     * @return
     */
    @EscapeLogin
    @ApiOperation(value = "获取订单商品计算的佣金")
    @GetMapping("/getCommissionByOrderId")
    BigDecimal getCommissionByOrderId(@RequestParam("orderId")Long orderId,@RequestParam("type")Integer type){
        return orderService.getCommissionByOrderId(orderId,type);
    }

    /**
     * 更新快递单号（显示）
     * @param orderDeliveryDto
     * @return
     */
    @EscapeLogin
    @ApiOperation(value = "更新快递单号（显示）")
    @PostMapping("/updateOrderDeliveryDeliverySnShow")
    Boolean updateOrderDeliveryDeliverySnShow(@RequestBody OrderDeliveryDto orderDeliveryDto){

        return orderDeliveryService.updateOrderDeliveryDeliverySnShow(orderDeliveryDto);
    }

    @EscapeLogin
    @ApiOperation(value = "插入出库单数据")
    @PostMapping("/addOrderOutStock")
    OrderOutStock addOrderOutStock(@RequestBody AddOrderOutStockDto addOrderOutStockDto){
        OrderOutStock orderOutStock =  orderOutStockService.addOrderOutStock(addOrderOutStockDto);
        return orderOutStock;
    }

    @EscapeLogin
    @ApiOperation(value = "通过门店classCode获取订单数量")
    @GetMapping("/getOrderCountByStoreFrontCode")
    Integer getOrderCountByStoreFrontCode(@RequestParam("storeFrontCode")String storeFrontCode){
        return orderService.getOrderCountByStoreFrontCode(storeFrontCode);
    }

    /**
     * 根据订单明细id获取订单明细详情
     * @param id
     * @return
     */
    @EscapeLogin
    @ApiOperation(value = "根据订单明细id获取订单明细详情")
    @GetMapping("/getOrderItemById")
    OrderItem getOrderItemById(@RequestParam("id")Long id){
        OrderItem orderItem = orderItemService.getById(id);
        return orderItem;
    }

    /**
     * 根据代发货订单明细id获取代发货订单明细
     * @param id
     * @return
     */
    @EscapeLogin
    @ApiOperation(value = "根据代发货订单明细id获取代发货订单明细")
    @GetMapping("/getOrderDeliveryProxyItem")
    OrderDeliveryProxyItem getOrderDeliveryProxyItem(@RequestParam("id") Long id){
        OrderDeliveryProxyItem orderDeliveryProxyItem = orderDeliveryProxyItemService.getById(id);
        return orderDeliveryProxyItem;
    }

    /**
     * 复购价判断
     * @param userId
     * @param memberTypeId
     * @return
     */
    @GetMapping("/getCountByMemberType")
    @EscapeLogin
    @ApiOperation(value = "复购价判断")
    Integer getCountByMemberType(@RequestParam("userId")String userId,
                                 @RequestParam("memberTypeId")Long memberTypeId){

       Integer count =  orderItemService.getCountByMemberType(userId,memberTypeId);
       return count;
    }

    @GetMapping("/getSumAmount")
    @EscapeLogin
    @ApiOperation(value = "获取团队订单业绩")
    BigDecimal getSumAmount(@RequestParam("startDate")String startDate,
                            @RequestParam("endDate")String endDate,
                            @RequestParam("skuIds")List<String> skuIds,
                            @RequestParam("shopUserIds")List<String> shopUserIds){
        BigDecimal amount = orderService.getSumAmount(startDate,endDate,skuIds,shopUserIds);
        return amount;
    }

    @GetMapping("/getOrderDeliveryById")
    @EscapeLogin
    @ApiOperation(value = "根据发货单id获取发货单")
    ManageOrderDeliveryVo getOrderDeliveryById(@RequestParam("id")Long id){
        ManageOrderDeliveryVo manageOrderDeliveryVo =  orderDeliveryService.getManageOrderDeliveryById(id);
        return manageOrderDeliveryVo;
    }

    @GetMapping("/getTeamsRegionAmount")
    @EscapeLogin
    @ApiOperation(value = "获取区域团队消费业绩")
    BigDecimal getTeamsRegionAmount(@RequestParam("upLevelTime")String upLevelTime,
                                    @RequestParam("regionType")Integer regionType,
                                    @RequestParam("agentRegionCode")String agentRegionCode,
                                    @RequestParam("teamTimes")Integer teamTimes){
        BigDecimal teamsRegionAmount = miniOrderService.getTeamsRegionAmount(upLevelTime,regionType,agentRegionCode,teamTimes);
        return teamsRegionAmount;
    }

    /**
     * 根据区域Code，成为区域会员时间获取订单数
     * @param agentRegionCode
     * @param upLevelTime
     * @return
     */
    @GetMapping("/getOrderCountByRegionCode")
    @EscapeLogin
    @ApiOperation(value = "根据区域Code，成为区域会员时间获取订单数")
    Integer getOrderCountByRegionCode(@RequestParam("agentRegionCode")String agentRegionCode,
                                      @RequestParam("upLevelTime")String upLevelTime,
                                      @RequestParam("regionType")Integer regionType){
        Integer orderCount = miniOrderService.getOrderCountByRegionCode(agentRegionCode,upLevelTime,regionType);
        return orderCount;
    }
    @GetMapping("/getUnDeliveryQuantity")
    @EscapeLogin
    @ApiOperation(value = "获取待发货数量")
    Integer getUnDeliveryQuantity(@RequestParam("userId")String userId,
                                  @RequestParam("memberTypeId")Long memberTypeId){
        Integer unDeliveryQuantity = miniOrderService.getUnDeliveryQuantity(userId,memberTypeId);
        return unDeliveryQuantity;
    }
}
