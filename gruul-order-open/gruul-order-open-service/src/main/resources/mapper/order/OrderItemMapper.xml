<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.order.mapper.OrderItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.order.api.entity.OrderItem">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="product_id" property="productId"/>
        <result column="product_pic" property="productPic"/>
        <result column="product_name" property="productName"/>
        <result column="product_sn" property="productSn"/>
        <result column="product_price" property="productPrice"/>
        <result column="product_original_price" property="productOriginalPrice"/>
        <result column="product_quantity" property="productQuantity"/>
        <result column="product_sku_id" property="productSkuId"/>
        <result column="product_sku_code" property="productSkuCode"/>
        <result column="promotion_amount" property="promotionAmount"/>
        <result column="coupon_amount" property="couponAmount"/>
        <result column="real_amount" property="realAmount"/>
        <result column="specs" property="specs"/>
        <result column="provider_id" property="providerId"/>
        <result column="refund_amount" property="refundAmount"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time,
        update_time,
        is_deleted,
        id, order_id, product_id, product_pic, product_name, product_sn, product_price,
        product_original_price, product_quantity, product_sku_id, product_sku_code, promotion_amount, coupon_amount,
         real_amount, specs,
        provider_id,  refund_amount,integral_product_id,price_type,member_type_id,un_delivery_quantity,product_quantity as can_afs_qty,return_quantity
    </sql>



    <resultMap id="ProductRankingDtoMap" type="com.medusa.gruul.order.model.ProductRankingDto">
        <result column="nick" property="nick"/>
        <result column="price" property="price"/>
        <result column="tradingVolume" property="tradingVolume"/>
        <result column="url" property="url"/>
    </resultMap>
    <resultMap id="UnShippedOrderItemMap" type="com.medusa.gruul.order.model.UnShippedOrderItemVo">
        <result column="product_id" property="productId"/>
        <result column="product_pic" property="productPic"/>
        <result column="product_name" property="productName"/>
        <result column="product_sku_id" property="productSkuId"/>
        <result column="product_sku_code" property="productSkuCode"/>
        <result column="product_quantity" property="productQuantity"/>
        <result column="un_delivery_quantity" property="unDeliveryQuantity"/>
        <result column="shop_id" property="shopId"/>
        <result column="shop_name" property="shopName"/>
        <result column="specs" property="specs"/>
    </resultMap>
    <!-- SimpleOrderItemVo查询映射结果 -->
    <resultMap id="SimpleOrderItemVoMap" type="com.medusa.gruul.order.model.SimpleOrderItemVo">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="product_id" property="productId"/>
        <result column="product_pic" property="productPic"/>
        <result column="product_name" property="productName"/>
        <result column="product_price" property="productPrice"/>
        <result column="product_original_price" property="productOriginalPrice"/>
        <result column="product_quantity" property="productQuantity"/>
        <result column="product_sku_id" property="productSkuId"/>
        <result column="real_amount" property="realAmount"/>
        <result column="specs" property="specs"/>
        <result column="integral" property="integral" />
        <result column="price_type" property="priceType" />
        <result column="un_delivery_quantity" property="unDeliveryQuantity" />
        <association column="{orderId=order_id,productSkuId=product_sku_id,priceType=price_type}" property="afs"
                     select="com.medusa.gruul.order.mapper.AfsOrderMapper.selectByOrderIdAndProductSkuId">
        </association>
    </resultMap>
    <resultMap id="OrderItemDeliveryMap" type="com.medusa.gruul.order.model.OrderItemDeliveryVo">
        <id column="id" property="id"/>
        <result column="product_name" property="productName"/>
        <result column="product_quantity" property="productQuantity"/>
        <result column="delivery_quantity" property="deliveryQuantity"/>
        <result column="un_delivery_quantity" property="unDeliveryQuantity"/>
        <result column="product_id" property="productId"/>
        <result column="product_sku_id" property="productSkuId"/>
        <result column="order_id" property="orderId"/>
    </resultMap>

    <select id="selectSimpleOrderItemVoByOrderId" resultMap="SimpleOrderItemVoMap">
        select
        id, order_id, product_id, product_pic, product_name, product_price, product_original_price, product_quantity,
         product_sku_id, real_amount, specs,integral,price_type,un_delivery_quantity
        from t_order_item
        where order_id = #{orderId}
    </select>

    <select id="selectSimpleOrderItem" resultMap="SimpleOrderItemVoMap">
        select
            t1.id, t1.order_id, t1.product_id,
            t1.product_pic, t1.product_name, t1.product_price,
            t1.product_original_price, t1.product_quantity,
            t1.product_sku_id, t1.real_amount, t1.specs,
            t1.integral,t1.price_type,t1.un_delivery_quantity
        from t_order_item t1
        left join t_order t2 on t1.order_id = t2.id
        where t1.is_deleted = 0 and t2.is_deleted = 0 and t2.status = 101
             and t1.product_sku_id = #{param.productSkuId} and t1.product_id = #{param.productId}
             and t2.user_id = #{param.userId}
        order by t2.create_time asc
    </select>


    <select id="selectItemDtoByOrderIds" resultType="com.medusa.gruul.goods.api.param.OperateStockDto">
        SELECT
        DISTINCT(product_sku_id) as sku_id,
        SUM(product_quantity) as number
        FROM
        `t_order_item`
        WHERE
        order_id IN
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        GROUP BY
        product_sku_id
    </select>
    <select id="selectByOrderId" resultType="com.medusa.gruul.order.api.entity.OrderItem">
        select
        <include refid="Base_Column_List"/>
        from t_order_item
        where order_id = #{orderId}
    </select>

    <resultMap id="OrderItemVoMap" type="com.medusa.gruul.order.api.model.OrderItemVo">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="order_id" property="orderId"/>
        <result column="product_id" property="productId"/>
        <result column="product_pic" property="productPic"/>
        <result column="product_name" property="productName"/>
        <result column="product_sn" property="productSn"/>
        <result column="product_price" property="productPrice"/>
        <result column="product_original_price" property="productOriginalPrice"/>
        <result column="product_quantity" property="productQuantity"/>
        <result column="product_sku_id" property="productSkuId"/>
        <result column="product_sku_code" property="productSkuCode"/>
        <result column="promotion_amount" property="promotionAmount"/>
        <result column="coupon_amount" property="couponAmount"/>
        <result column="real_amount" property="realAmount"/>
        <result column="specs" property="specs"/>
        <result column="provider_id" property="providerId"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="price_type" property="priceType"/>
        <result column="member_type_id" property="memberTypeId"/>
        <result column="can_afs_qty" property="canAfsQty"/>
        <result column="un_delivery_quantity" property="unDeliveryQuantity"/>
        <result column="return_quantity" property="returnQuantity"/>
        <association column="{orderId=order_id,productSkuId=product_sku_id,memberTypeId=member_type_id}" property="afs"
                     select="com.medusa.gruul.order.mapper.AfsOrderMapper.selectByOrderIdAndProductSkuId">
        </association>
    </resultMap>

    <select id="selectOrderItemVoByOrderId" resultMap="OrderItemVoMap">
        select
        <include refid="Base_Column_List"/>
        from t_order_item
        where order_id = #{orderId}
    </select>

    <!-- GoodsBean查询映射结果 -->
    <resultMap id="GoodsBeanMap" type="com.medusa.gruul.order.api.model.GoodsBean">
        <result column="product_id" property="goodId"/>
        <result column="product_pic" property="img"/>
        <result column="product_name" property="goodName"/>
        <result column="product_quantity" property="num"/>
        <result column="product_sku_id" property="goodSkuId"/>
        <result column="product_sku_code" property="goodSkuCode"/>
        <result column="product_price" property="realPrice"/>
        <result column="specs" property="goodSpecs"/>
    </resultMap>

    <select id="countSkuPurchased" resultType="java.lang.Integer">
        SELECT
            SUM( oi.product_quantity )
        FROM
            t_order_item as oi
            LEFT JOIN t_order as o ON o.id = oi.order_id
        WHERE
            oi.product_sku_id = #{productSkuId}
            AND o.user_id= #{userId}
            AND o.`status` &lt; 300
    </select>


    <select id="getProductVBRanking" resultMap="ProductRankingDtoMap">
        SELECT
            distinct pr.pic as url,
                     ( SELECT  min(	sku.price) FROM t_sku_stock AS sku  LEFT JOIN t_product as p ON p.id = sku.product_id WHERE p.id= oi.product_id) as price,
                     pr.name as nick,
                (select count(*) FROM t_order_item as o WHERE  o.product_id=oi.product_id ) as tradingVolume
        FROM
            t_order_item as oi
                LEFT JOIN t_product as pr ON pr.id = oi.product_id
        order by tradingVolume desc  limit 5
    </select>
    <select id="getgvmSproductVBRanking" resultMap="ProductRankingDtoMap">
        SELECT
            distinct pr.pic as url,
                     ( SELECT  min(	sku.price) FROM t_sku_stock AS sku  LEFT JOIN t_product as p ON p.id = sku.product_id WHERE p.id= oi.product_id) as price,
                     pr.name as nick,
                     (select sum(product_price)  FROM t_order_item as o WHERE  o.product_id=oi.product_id ) as tradingVolume
        FROM
            t_order_item as oi
                LEFT JOIN t_product as pr ON pr.id = oi.product_id
        order by tradingVolume desc  limit 5
    </select>

    <select id="getIdsByOrderHistory" resultType="java.lang.Long">
        select distinct (t2.product_id) from t_order_item t2
        left join t_order t1 on t1.id = t2.order_id
        where t1.user_id = #{userId}
        group by t2.product_id
        order by max(t2.create_time)
        limit 20
    </select>
    <select id="getOrderItemDelivery" resultMap="OrderItemDeliveryMap">
        SELECT
            id,
            product_name,
            product_quantity,
            (product_quantity-un_delivery_quantity) as delivery_quantity,
            un_delivery_quantity,
            product_id,
            product_sku_id,
            order_id
        FROM
            t_order_item
        where
            order_id = #{orderId}
    </select>
    <select id="getUnShippedOrderItem" resultMap="UnShippedOrderItemMap">
        SELECT
            t1.product_id,
            t3.pic AS product_pic,
            t3.name AS product_name,
            t1.product_sku_id,
            t1.product_sku_code,
            sum( t1.product_quantity ) AS product_quantity,
            sum( t1.un_delivery_quantity ) AS un_delivery_quantity,
            t2.shop_id,
            t4.name as shop_name,
            t5.specs
        FROM
            t_order_item t1
                LEFT JOIN t_order t2 ON t1.order_id = t2.id
                LEFT JOIN t_product t3 ON t1.product_id = t3.id
                LEFT JOIN t_shops_partner t4 ON t4.shop_id = t2.shop_id
                LEFT JOIN t_sku_stock t5 on t5.id = t1.product_sku_id
        WHERE
            t1.is_deleted = 0
          AND t2.is_deleted = 0
          AND t3.is_deleted = 0
          AND t1.un_delivery_quantity != 0
	      AND t2.status = 101
          AND t2.user_id = #{param.userId}
        <if test="param.memberTypeId!=null">
            AND t1.member_type_id = #{param.memberTypeId}
        </if>
        <if test="param.productName!=null  and param.productName!=''">
            AND t3.name LIKE concat('%',#{param.productName},'%')
        </if>
        <if test="param.productId!=null">
            AND t1.product_id = #{param.productId}
        </if>
        <if test="param.productSkuId!=null">
            AND t1.product_sku_id = #{param.productSkuId}
        </if>
        GROUP BY
            t1.product_id,
            t3.pic,
            t3.name,
            t1.product_sku_id,
            t1.product_sku_code,
            t2.shop_id,
            t4.name,
            t5.specs
    </select>

    <select id="getCountByMemberType" resultType="java.lang.Integer">
        SELECT
            count( t1.id )
        FROM
            t_order_item t1
                LEFT JOIN t_order t2 ON t1.order_id = t2.id
        WHERE
            t1.is_deleted = 0
          AND t2.is_deleted = 0
          AND t1.member_type_id = #{memberTypeId}
          AND t2.user_id = #{shopUserId}
        <if test="statusList!=null and statusList.size>0">
            AND t2.status in
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </select>
</mapper>
