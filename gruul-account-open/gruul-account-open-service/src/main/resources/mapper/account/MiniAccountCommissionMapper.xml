<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MiniAccountCommissionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.account.api.entity.MiniAccountCommission">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="update_time" property="updateTime"/>
        <result column="user_id" property="userId"/>
        <result column="order_id" property="orderId"/>
        <result column="commission_type" property="commissionType"/>
        <result column="amount" property="amount"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <resultMap id="UserCommissionVoMap" type="com.medusa.gruul.account.model.vo.UserCommissionVo">
        <result column="nike_name" property="nikeName"/>
        <result column="commission" property="commission"/>
        <result column="used_commission" property="usedCommission"/>
        <result column="current_commission" property="currentCommission"/>
        <result column="recommend_name" property="recommendName"/>
        <result column="reward_commission" property="rewardCommission"/>
        <result column="reward_royalty" property="rewardRoyalty"/>
    </resultMap>
    <resultMap id="DistributionOrderVoMap" type="com.medusa.gruul.account.model.vo.DistributionOrderVo">
        <result column="order_id" property="orderId"/>
        <result column="shop_name" property="shopName"/>
        <result column="buy_name" property="buyName"/>
        <result column="recommend_name" property="recommendName"/>
        <result column="amount" property="amount"/>
        <result column="create_time" property="createTime"/>
        <result column="avatar_url" property="avatarUrl"/>
        <result column="price" property="price"/>
    </resultMap>

    <resultMap id="MiniAccountRoyaltyDetMap" type="com.medusa.gruul.account.model.vo.MiniAccountRoyaltyDetVo">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="pay_time" property="payTime"/>
        <result column="nike_name" property="nikeName"/>
        <result column="phone" property="phone"/>
        <result column="amount" property="amount"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="remark" property="remark"/>
    </resultMap>


    <resultMap id="MiniAccountCommissionManageMap" type="com.medusa.gruul.account.model.vo.MiniAccountCommissionManageVo">
        <result column="nike_name" property="nikeName"/>
        <result column="phone" property="phone"/>
        <result column="commission" property="commission"/>
        <result column="way" property="way"/>
        <result column="amount" property="amount"/>
        <result column="last_commission" property="lastCommission"/>
        <result column="total_commission" property="totalCommission"/>
        <result column="source_nike_name" property="sourceNikeName"/>
        <result column="time" property="time"/>
        <result column="order_id" property="orderId"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <resultMap id="MiniAccountCommissionMap" type="com.medusa.gruul.account.model.vo.MiniAccountCommissionVo">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="source_shop_user_name" property="sourceShopUserName"/>
        <result column="create_time" property="createTime"/>
        <result column="order_id" property="orderId"/>
        <result column="commission_type" property="commissionType"/>
        <result column="amount" property="amount"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,create_time,is_deleted,update_time, user_id, order_id, commission_type, amount, remark
    </sql>

    <select id="getUserCommissionVo" resultMap="UserCommissionVoMap">
        SELECT
            t1.nike_name,
            ifnull(t1.commission,0) as commission,
            ifnull(t1.used_commission,0) as used_commission,
            ifnull(t1.current_commission,0) as current_commission,
            ifnull(t1.reward_commission,0) as reward_commission,
            ifnull(t1.reward_royalty,0) as reward_royalty,
            t2.nike_name as recommend_name
        FROM
            t_mini_account t1 left join t_mini_account t2 on t1.parent_id = t2.user_id
        where t1.user_id = #{userId}
    </select>
    <select id="getDistributionOrderVo" resultMap="DistributionOrderVoMap">
        SELECT
            t1.order_id,
            t3.name AS shop_name,
            t5.nike_name AS buy_name,
            t6.nike_name AS recommend_name,
            t1.amount,
            t1.create_time,
            t5.avatar_url,
            t2.pay_amount as price
        FROM
            t_mini_account_commission t1
                LEFT JOIN t_order t2 ON t1.order_id = t2.id
                LEFT JOIN t_shops_partner t3 ON t3.shop_id = t2.shop_id
                LEFT JOIN t_mini_account_extends t4 ON t4.shop_user_id = t1.source_shop_user_id
                LEFT JOIN t_mini_account t5 ON t4.user_id = t5.user_id
                LEFT JOIN t_mini_account t6 ON t6.user_id = t5.parent_id
        WHERE
            t1.is_deleted = 0
          and t1.user_id = #{paramMap.shopUserId}
          and ifnull(t1.order_id,'') != ''
        <if test="paramMap.searchValue!=null">
            and (
                t5.nike_name like concat('%',#{paramMap.searchValue},'%')
                or t3.name like concat('%',#{paramMap.searchValue},'%')
                or t1.order_id like concat('%',#{paramMap.searchValue},'%')
                or t6.nike_name like concat('%',#{paramMap.searchValue},'%')
                or t1.amount = #{paramMap.searchValue}
            )
        </if>
        order by t1.create_time desc
    </select>
    <select id="searchMiniAccountRoyaltyDet" resultMap="MiniAccountRoyaltyDetMap">
        SELECT
            t1.id,
            t1.order_id,
            DATE_FORMAT(t2.pay_time,'%Y-%m-%d') as pay_time,
            t4.nike_name,
            t4.phone,
            t1.amount,
            t2.pay_amount,
            t1.remark
        FROM
            t_mini_account_commission t1
                left join t_order t2 on t1.order_id = t2.id
                left join t_mini_account_extends t3 on t3.shop_user_id = t1.user_id
                left join t_mini_account t4 on t4.user_id = t3.user_id
        WHERE
            t1.is_deleted = 0
          AND t1.commission_type = '102'
        <if test="paramMap.phone!=null and paramMap.phone!='' ">
            AND t4.phone like concat('%',#{paramMap.phone},'%')
        </if>
        <if test="paramMap.nikeName!=null and paramMap.nikeName!='' ">
            AND t4.nike_name like concat('%',#{paramMap.nikeName},'%')
        </if>
        <if test="paramMap.orderId!=null and paramMap.orderId!='' ">
            AND t1.order_id like concat('%',#{paramMap.orderId},'%')
        </if>
        <if test="paramMap.startTime!=null and paramMap.startTime!='' and paramMap.endTime!=null and paramMap.endTime!=''">
            and t2.pay_time >= #{paramMap.startTime} and t2.pay_time &lt;= #{paramMap.endTime}
        </if>
        ORDER BY
            t1.create_time DESC
    </select>
    <select id="searchMiniAccountCommissionDet" resultMap="MiniAccountCommissionManageMap">
        SELECT
        t3.nike_name,
        t3.phone,
        t3.commission,
        t1.last_commission,
        t1.total_commission,
        t1.amount,
        CASE

        WHEN t1.amount > 0 THEN
        '增加' ELSE '减少'
        END AS way,
        t5.nike_name AS source_nike_name,
        DATE_FORMAT( t1.create_time, '%Y-%m-%d %H:%i:%s' ) as time,
        t1.order_id,
        t1.remark
        FROM
        t_mini_account_commission t1
        LEFT JOIN t_mini_account_extends t2 ON t1.user_id = t2.shop_user_id
        LEFT JOIN t_mini_account t3 ON t2.user_id = t3.user_id
        LEFT JOIN t_mini_account_extends t4 ON t1.source_shop_user_id = t4.shop_user_id
        LEFT JOIN t_mini_account t5 ON t4.user_id = t5.user_id
        where
        t1.is_deleted = 0
        <if test="paramMap.nikeName!=null and paramMap.nikeName!='' ">
            AND t3.nike_name like concat('%',#{paramMap.nikeName},'%')
        </if>
        <if test="paramMap.phone!=null and paramMap.phone!='' ">
            AND t3.phone like concat('%',#{paramMap.phone},'%')
        </if>
        <if test="paramMap.startTime!=null and paramMap.startTime!='' and paramMap.endTime!=null and paramMap.endTime!=''">
            and t1.create_time >= #{paramMap.startTime} and t1.create_time &lt;= #{paramMap.endTime}
        </if>
        <if test="paramMap.commissionType!=null and paramMap.commissionType!=0">
            AND t1.commission_type = #{paramMap.commissionType}
        </if>
        <if test="paramMap.remark!=null and paramMap.remark!='' ">
            AND t1.remark like concat('%',#{paramMap.remark},'%')
        </if>
        <if test="paramMap.way!=null and paramMap.way==1">
            AND t1.amount > 0
        </if>
        <if test="paramMap.way!=null and paramMap.way==2">
            AND t1.amount &lt; 0
        </if>
        ORDER BY
        t1.create_time DESC
    </select>
    <select id="pageMyCommission" resultMap="MiniAccountCommissionMap">
        SELECT
            t1.id,
            t1.user_id,
            t3.nike_name AS source_shop_user_name,
            DATE_FORMAT( t1.create_time, '%Y-%m-%d %H:%i:%s' ) AS create_time,
            t1.order_id,
            t1.commission_type,
            t1.amount,
            t1.remark
        FROM
            t_mini_account_commission t1
                LEFT JOIN t_mini_account_extends t2 ON t1.source_shop_user_id = t2.shop_user_id
                LEFT JOIN t_mini_account t3 ON t2.user_id = t3.user_id
        WHERE
            t1.is_deleted = 0
            <if test="paramMap.shopUserId!=null and paramMap.shopUserId!='' ">
                AND t1.user_id =  #{paramMap.shopUserId}
            </if>
            <if test="paramMap.startTime!=null and paramMap.startTime!='' ">
                AND t1.create_time  &gt;= #{paramMap.startTime}
            </if>
            <if test="paramMap.endTime!=null and paramMap.endTime!='' ">
                AND t1.create_time  &lt;= #{paramMap.endTime}
            </if>
            <if test="paramMap.keyword!=null and paramMap.keyword!='' ">
                AND (
                    t3.nike_name like concat('%',#{paramMap.keyword},'%')
                 or t1.remark like concat('%',#{paramMap.keyword},'%')
                    )
            </if>
        ORDER BY
        t1.create_time DESC
    </select>
</mapper>
