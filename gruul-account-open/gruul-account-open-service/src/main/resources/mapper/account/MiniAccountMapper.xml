<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MiniAccountMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.account.api.entity.MiniAccount">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="update_time" property="updateTime"/>
        <result column="user_id" property="userId"/>
        <result column="member_level_id" property="memberLevelId"/>
        <result column="country" property="country"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="language" property="language"/>
        <result column="nike_name" property="nikeName"/>
        <result column="avatar_url" property="avatarUrl"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="gender" property="gender"/>
        <result column="first_login_time" property="firstLoginTime"/>
        <result column="whether_authorization" property="whetherAuthorization"/>
        <result column="send_status" property="sendStatus"/>
        <result column="parent_id" property="parentId"/>
        <result column="above_parent_id" property="aboveParentId"/>
        <result column="used_integral" property="usedIntegral"/>
        <result column="current_integral" property="currentIntegral"/>
        <result column="integral" property="integral"/>
        <result column="commission" property="commission"/>
        <result column="used_commission" property="usedCommission"/>
        <result column="current_commission" property="currentCommission"/>
        <result column="user_name" property="userName"/>
        <result column="reward_commission" property="rewardCommission"/>
        <result column="reward_royalty" property="rewardRoyalty"/>
        <result column="common_integral" property="commonIntegral"/>
        <result column="sale_integral" property="saleIntegral"/>
        <result column="golden" property="golden"/>
        <result column="used_golden" property="usedGolden"/>
        <result column="current_golden" property="currentGolden"/>
        <result column="self_invite_code" property="selfInviteCode"/>
    </resultMap>

    <resultMap id="MyTeamMiniAccountVoMap" type="com.medusa.gruul.account.model.vo.MyTeamMiniAccountVo">
        <result column="name" property="name"/>
        <result column="create_time" property="createTime"/>
        <result column="recommend_name" property="recommendName"/>
        <result column="member_level" property="memberLevel"/>
        <result column="amount" property="amount"/>
        <result column="avatar_url" property="avatarUrl"/>
    </resultMap>
    <resultMap id="ChooseAccountMap" type="com.medusa.gruul.account.api.model.vo.ChooseAccountVo">
        <result column="user_id" property="userId"/>
        <result column="nike_name" property="nikeName"/>
        <result column="phone" property="phone"/>
        <result column="member_level" property="memberLevel"/>
        <result column="tag_name" property="tagName"/>
    </resultMap>
    <resultMap id="MiniAccountDetailMap" type="com.medusa.gruul.account.model.vo.MiniAccountDetailVo">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="card_number" property="cardNumber"/>
        <result column="nike_name" property="nikeName"/>
        <result column="member_level" property="memberLevel"/>
        <result column="first_login_time" property="firstLoginTime"/>
        <result column="phone" property="phone"/>
        <result column="parent_name" property="parentName"/>
        <result column="consume_num" property="consumeNum"/>
        <result column="consume_totle_money" property="consumeTotalMoney"/>
        <result column="member_money" property="memberMoney"/>
        <result column="age" property="age"/>
        <result column="gender" property="gender"/>
        <result column="user_name" property="userName"/>
        <result column="birthday" property="birthday"/>
        <result column="card" property="card"/>
        <result column="integral" property="integral"/>
        <result column="used_integral" property="usedIntegral"/>
        <result column="current_integral" property="currentIntegral"/>
        <result column="sale_integral" property="saleIntegral"/>
        <result column="all_integral" property="allIntegral"/>
        <result column="wx_account_url" property="wxAccountUrl"/>
        <result column="alipay_account_url" property="alipayAccountUrl"/>
        <result column="card_authorization" property="cardAuthorization"/>
        <result column="recommend_name" property="recommendName"/>
        <result column="shop_user_id" property="shopUserId"/>
    </resultMap>

    <resultMap id="UserListResultMap" type="com.medusa.gruul.account.model.dto.UserListDto">
        <result column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="member_number" property="memberNumber"/>
        <result column="rebate_bonus" property="rebateBonus"/>
        <result column="user_id" property="userId"/>
        <result column="nike_name" property="nikeName"/>
        <result column="avatar_url" property="avatarUrl"/>
        <result column="phone" property="phone"/>
        <result column="member_register_time" property="memberRegisterTime"/>
        <result column="last_deal_time" property="orderLastDealTime"/>
        <result column="first_login_time" property="firstLoginTime"/>
        <result column="consume_num" property="consumeNum"/>
        <result column="consume_totle_money" property="consumeTotleMoney"/>
        <result column="integral" property="integral"/>
        <result column="community_type" property="communityType"/>
        <result column="shop_user_id" property="shopUserId"/>
        <result column="card_number" property="cardNumber"/>
        <result column="integral" property="integral"/>
        <result column="balance" property="balance"/>
        <result column="commission" property="commission"/>
        <result column="currentCommission" property="currentCommission"/>
        <result column="send_status" property="sendStatus"/>
        <result column="one_team_num" property="oneTeamNum"/>
        <result column="two_team_num" property="TwoTeamNum"/>
        <result column="recommend_name" property="recommendName"/>
        <result column="recommend_user_id" property="recommendUserId"/>
        <result column="recommend_sale_flag" property="recommendSaleFlag"/>

        <result column="current_commission" property="currentCommission"/>
        <result column="department_code" property="departmentCode"/>
        <result column="employee_id" property="employeeId"/>
        <result column="level_code" property="levelCode"/>
        <result column="gender" property="gender"/>

        <result column="id_card_face_url" property="idCardFaceUrl"/>
        <result column="id_card_back_url" property="idCardBackUrl"/>
        <result column="card_authorization" property="cardAuthorization"/>
        <result column="audit_platform_user_id" property="auditPlatformUserId"/>
        <result column="audit_time" property="auditTime"/>
        <result column="card_audit_reason" property="cardAuditReason"/>
        <result column="card" property="card"/>
        <result column="user_name" property="userName"/>
        <result column="sale_user_id" property="saleUserId"/>
        <result column="sale_flag" property="saleFlag"/>
        <result column="self_invite_code" property="selfInviteCode"/>
        <result column="register_time" property="registerTime"/>
        <result column="golden" property="golden"/>
        <result column="used_golden" property="usedGolden"/>
        <result column="current_golden" property="currentGolden"/>
    </resultMap>

    <resultMap id="AreaAccountInfoDtoResultMap" type="com.medusa.gruul.account.api.model.AreaAccountInfoDto">
        <result column="nike_name" property="nikeName"/>
        <result column="avatar_url" property="avatarUrl"/>
        <result column="last_deal_time" property="lastDealTime"/>
        <result column="first_login_time" property="firstLoginTime"/>
        <result column="consume_num" property="consumeNum"/>
        <result column="consume_totle_money" property="consumeTotleMoney"/>
        <result column="shop_user_id" property="shopUserId"/>
    </resultMap>

    <resultMap id="MiniAccountRoyaltyMap" type="com.medusa.gruul.account.model.vo.MiniAccountRoyaltyVo">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="nike_name" property="nikeName"/>
        <result column="phone" property="phone"/>
        <result column="member_level_id" property="memberLevelId"/>
        <result column="member_level_name" property="memberLevelName"/>
        <result column="reward_royalty" property="rewardRoyalty"/>
    </resultMap>

    <resultMap id="UserIntegralMap" type="com.medusa.gruul.account.model.vo.UserIntegralVo">
        <result column="user_id" property="userId"/>
        <result column="integral" property="integral"/>
        <result column="used_integral" property="usedIntegral"/>
        <result column="current_integral" property="currentIntegral"/>
        <result column="sale_integral" property="saleIntegral"/>
        <result column="common_integral" property="commonIntegral"/>
    </resultMap>

    <resultMap id="MiniAccountChildVoMap" type="com.medusa.gruul.account.model.vo.MiniAccountChildVo">
        <result column="user_id" property="userId"/>
        <result column="nike_name" property="nikeName"/>
        <result column="phone" property="phone"/>
        <result column="member_level" property="memberLevel"/>
    </resultMap>
    <resultMap id="BindMiniAccountMap" type="com.medusa.gruul.account.model.vo.BindMiniAccountVo">
        <result column="user_id" property="userId"/>
        <result column="nike_name" property="nikeName"/>
        <result column="phone" property="phone"/>
        <result column="join_date" property="joinDate"/>
    </resultMap>
    <resultMap id="ApiMiniAccountCardMap" type="com.medusa.gruul.account.model.vo.ApiMiniAccountCardVo">
        <result column="id_card_face_url" property="idCardFaceUrl"/>
        <result column="id_card_back_url" property="idCardBackUrl"/>
        <result column="card_authorization" property="cardAuthorization"/>
        <result column="audit_platform_user_id" property="auditPlatformUserId"/>
        <result column="audit_time" property="auditTime"/>
        <result column="card_audit_reason" property="cardAuditReason"/>
        <result column="card" property="card"/>
        <result column="user_name" property="userName"/>
    </resultMap>
    <resultMap id="AccountSendWxBirthDayMessageMap" type="com.medusa.gruul.account.model.vo.AccountSendWxBirthDayMessageVo">
        <result column="user_id" property="userId"/>
        <result column="nike_name" property="nikeName"/>
        <result column="user_name" property="userName"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        member_level_id,
        create_time,
        is_deleted,
        update_time,
        user_id,
        country,
        province,
        city,
        `language`,
        nike_name,
        avatar_url,
        phone,
        email,
        gender,
        first_login_time,
        whether_authorization,
        parent_id,
        above_parent_id,
        used_integral,
        current_integral,
        integral,
        ifnull(commission,0) as commission,
        ifnull(used_commission,0) as used_commission,
        ifnull(current_commission,0) as current_commission,
        age,
        user_name,
        reward_commission,
        reward_royalty,
        common_integral,
        sale_integral,
        ifnull(golden,0) as golden,
        ifnull(used_golden,0) as used_golden,
        ifnull(current_golden,0) as current_golden,
        self_invite_code,
        register_time
    </sql>

    <select id="selectByUserList" resultMap="UserListResultMap">
        select
        DISTINCT
        tma.id,
        tma.user_id,
        tma.member_level_id,
        tma.avatar_url,
        tma.phone,
        tma.nike_name,
        tma.first_login_time,
        tmae.last_deal_time,
        tmae.consume_num,
        tmae.consume_totle_money,
        tmae.community_type,
        tmae.shop_user_id,
        tma.parent_id,
        tma.card_number,
        tma.integral,
        tma.balance,
        tma.send_status,
        ifnull(tma.commission,0) as commission,
        ifnull(tma.current_commission,0) as current_commission,
        ifnull(one_tma.num,0) as one_team_num,
        ifnull(two_tma.num,0) as two_team_num,
        tma2.nike_name AS recommend_name,
        tma2.user_id AS recommend_user_id,
        tma2.sale_flag AS recommend_sale_flag,
        tma.card,
        tma.user_name,
        tmae.id_card_face_url,
        tmae.id_card_back_url,
        ifnull( tmae.card_authorization, 0 ) AS card_authorization,
        tmae.audit_platform_user_id,
        DATE_FORMAT( tmae.audit_time, '%Y-%m-%d' ) AS audit_time,
        tmae.card_audit_reason,
        tma.sale_user_id,
        tma.sale_flag,
        tma.self_invite_code,
        tma.register_time,
        tma.golden,
        tma.used_golden,
        tma.current_golden
        from t_mini_account AS tma
        LEFT JOIN t_mini_account tma2 ON tma.parent_id = tma2.user_id
        AND tma2.is_deleted = 0
        left join t_mini_account_extends AS tmae on tmae.user_id = tma.user_id
        left join (select count(1) AS num,parent_id from t_mini_account where whether_authorization = 1 and
            is_deleted = 0 and parent_id is not null and parent_id != 0 group by parent_id) as one_tma on one_tma.parent_id =  tma.user_id
        left join (select count(1) AS num,above_parent_id from t_mini_account where whether_authorization = 1 and
            is_deleted = 0 and above_parent_id is not null and above_parent_id != 0 group by above_parent_id) as two_tma on two_tma.above_parent_id =  tma.user_id
        left join (SELECT user_id, GROUP_CONCAT(shop_id) AS shop_id FROM t_mini_account_shops GROUP BY user_id) AS shops on shops.user_id = tma.user_id
        <where>
            (tmae.is_blacklist = 0 or tmae.id is null) and tma.is_deleted = 0
            <if test="paramMap.tagId != null">
                and tmae.shop_user_id in (SELECT user_id
                FROM t_mini_account_tag_group
                where tag_id = #{paramMap.tagId}
                and is_deleted = 0)
            </if>
            <if test="paramMap.nikeName != null">
                and tma.nike_name like #{paramMap.nikeName}
            </if>
            <if test="paramMap.phone != null">
                and tma.phone like #{paramMap.phone}
            </if>
            <if test="paramMap.orderSuccessStartTime != null">
                and tmae.last_deal_time >= #{paramMap.orderSuccessStartTime}
            </if>
            <if test="paramMap.orderSuccessEndTime != null">
                and  <![CDATA[ tmae.last_deal_time <= #{paramMap.orderSuccessEndTime}
]]>
            </if>
            <if test="paramMap.userId != null">
                and tma.parent_id = #{paramMap.userId}
            </if>
            <if test="paramMap.parentId != null">
                and tma.parent_id = #{paramMap.parentId} and tma.whether_authorization = 1
            </if>
            <if test="paramMap.aboveParentId != null">
                and tma.above_parent_id = #{paramMap.aboveParentId} and tma.whether_authorization = 1
            </if>
            <if test="paramMap.shopId != null and paramMap.shopId!='' ">
                and shops.shop_id like CONCAT('%',#{paramMap.shopId},'%')
            </if>
            <if test="paramMap.shopIds!=null and  paramMap.shopIds.size() > 0">
                AND
                <foreach collection="paramMap.shopIds" item="shopId" open="(" separator="or" close=")">
                    shops.shop_id like CONCAT('%',#{shopId},'%')
                </foreach>
            </if>
            <if test="paramMap.cardAuthorization != null">
                and tmae.card_authorization = #{paramMap.cardAuthorization}
            </if>
            <if test="paramMap.expireDays!=null">
                and DATEDIFF(IFNULL(tmae.last_deal_time,tma.register_time),NOW()) = #{paramMap.expireDays}
            </if>

        </where>
        <if test="paramMap.sortType != null">
            order by
            <if test="paramMap.sortType == 1">
                tmae.consume_totle_money desc,tma.id
            </if>
            <if test="paramMap.sortType == 2">
                tmae.consume_totle_money asc
            </if>

        </if>
        <if test="paramMap.sortType == null">
            order by tmae.shop_user_id desc
        </if>
    </select>

    <select id="selectExternalByUserList" resultMap="UserListResultMap">
        select
        DISTINCT
        tma.id,
        tma.user_id,
        tma.member_level_id,
        tma.avatar_url,
        tma.phone,
        tma.nike_name,
        tma.first_login_time,
        tmae.last_deal_time,
        tmae.consume_num,
        tmae.consume_totle_money,
        tmae.community_type,
        tmae.shop_user_id,
        tma.parent_id,
        tma.card_number,
        tma.integral,
        tma.balance,
        tma.department_code,
        tpai.employee_id,
        tml.level_code,
        tma.gender,
        tma.register_time
        from t_mini_account AS tma
        left join t_mini_account_extends AS tmae on tmae.user_id = tma.user_id
        left join t_member_level as tml on tml.id = tma.member_level_id
        left join t_platform_account_info tpai on tpai.id = tma.platform_account_id
        <where>
            tma.is_deleted = 0 and tmae.is_blacklist = 0 and IFNULL(tma.send_status, 0) != 1
            and tma.whether_authorization = 1
            and ifnull(tma.platform_account_id,'')!=''
            <if test="paramMap.tagId != null">
                and tmae.shop_user_id in (SELECT user_id
                FROM t_mini_account_tag_group
                where tag_id = #{paramMap.tagId}
                and is_deleted = 0)
            </if>
            <if test="paramMap.nikeName != null">
                and tma.nike_name like #{paramMap.nikeName}
            </if>
            <if test="paramMap.orderSuccessStartTime != null">
                and tmae.last_deal_time >= #{paramMap.orderSuccessStartTime}
            </if>
            <if test="paramMap.orderSuccessEndTime != null">
                and  <![CDATA[ tmae.last_deal_time <= #{paramMap.orderSuccessEndTime}
                ]]>
            </if>
            <if test="paramMap.userId != null">
                and tma.parent_id = #{paramMap.userId}
            </if>
        </where>
        <if test="paramMap.sortType != null">
            order by
            <if test="paramMap.sortType == 1">
                tmae.consume_totle_money desc,tma.id
            </if>
            <if test="paramMap.sortType == 2">
                tmae.consume_totle_money asc
            </if>

        </if>
        <if test="paramMap.sortType == null">
            order by tmae.register_time desc
        </if>
    </select>

    <update id="updateSendStatus">
        update t_mini_account set send_status=#{sendStatus} where id in
        <foreach collection="accountIds" item="accountId" open="(" separator="," close=")">
            #{accountId}
        </foreach>
    </update>

    <select id="getUserParent" resultMap="UserListResultMap">
        select
        DISTINCT
        tma.id,
        tma.user_id,
        tma.member_level_id,
        tma.avatar_url,
        tma.phone,
        tma.nike_name,
        tma.first_login_time,
        tmae.last_deal_time,
        tmae.consume_num,
        tmae.consume_totle_money,
        tmae.community_type,
        tmae.shop_user_id,
        tma.parent_id,
        tma.register_time
        from t_mini_account AS tma
        left join t_mini_account_extends AS tmae on tmae.user_id = tma.user_id
        <where>
            tmae.is_blacklist = 0
            and tma.whether_authorization = 1

            <if test="userId != null">
                and tma.id = #{userId}
            </if>
        </where>

    </select>

    <select id="selectByBlackListUser" resultType="com.medusa.gruul.account.model.dto.BlacklistUserDto">
        select tma.user_id,
        tma.avatar_url,
        tma.phone,
        tma.nike_name,
        tma.first_login_time,
        tmae.last_deal_time,
        tmae.consume_num,
        tmae.consume_totle_money,
        tmae.shop_user_id
        from t_mini_account as tma
        left join t_mini_account_extends as tmae on tmae.user_id = tma.user_id
        LEFT JOIN (SELECT user_id,
        any_value(restrict_type) AS restrict_type
        FROM t_mini_account_restrict
        GROUP BY user_id) AS mar on mar.user_id = tma.user_id
        <where>
            tmae.is_blacklist = 1

            <if test="paramMap.permission != null">
                AND tmae.shop_user_id IN (SELECT user_id
                FROM t_mini_account_restrict
                WHERE is_deleted = 0
                and restrict_type
                = #{paramMap.permission})
            </if>
            <if test="paramMap.fuzzy != null">
                and
                (
                tma.nike_name like #{paramMap.fuzzy}
                or
                tma.phone like #{paramMap.fuzzy}
                )
            </if>
        </where>
        order by tmae.join_blacklist_time desc
    </select>


    <resultMap id="MiniAccountExtDtoResultMap" type="com.medusa.gruul.account.api.model.MiniAccountExtDto">
        <result column="nike_name" property="nikeName"/>
        <result column="avatar_url" property="avatarUrl"/>
        <result column="shop_user_id" property="shopUserId"/>
    </resultMap>


    <select id="selectByShopUserIds" resultMap="MiniAccountExtDtoResultMap">
        select tma.avatar_url,
        tma.nike_name,
        tmae.shop_user_id
        from t_mini_account as tma
        left join t_mini_account_extends as tmae on tmae.user_id = tma.user_id
        where
        tmae.shop_user_id
        <foreach collection="shopUserIds" item="shopUserId" open="in(" separator="," close=")">
            #{shopUserId}
        </foreach>
    </select>




    <select id="getUserInfoByShopUserId" resultType="java.lang.String">
        select user_id
        from t_mini_account_extends t
        where t.is_deleted = 0
        <if test="shopUserId != null and shopUserId != ''">
            AND t.shop_user_id = #{shopUserId}
        </if>
    </select>

    <!--<select id="getUserInfoByShopUserId" resultType="java.lang.String">
        select user_id
        from t_mini_account_extends t
        where t.is_deleted = 0
        <if test="shopUserId != null and shopUserId != ''">
            AND t.shop_user_id = #{shopUserId}
        </if>
    </select>-->







    <update id="updateRebateBonus">
        update t_mini_account_extends t
        set t.rebate_bonus = #{amount}
        where t.is_deleted = 0
          and t.shop_user_id = #{userId}
    </update>

    <select id="selectByShopUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_mini_account
        where
        user_id = (
        select user_id from t_mini_account_extends where shop_user_id = #{shopUserId}
        )
    </select>


    <select id="getPowerAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_mini_account
        where
        user_id = (
        select user_id from t_mini_account_extends where shop_user_id = #{shopUserId}
        )
    </select>
    <select id="getUserInfoIntegral" resultMap="UserIntegralMap">
        select
            user_id,
            IFNULL(integral,0) as integral,
            IFNULL(used_integral,0) as used_integral,
            IFNULL(current_integral,0) as current_integral,
            IFNULL(common_integral,0) as common_integral,
            IFNULL(sale_integral,0) as sale_integral
        from
            t_mini_account
        where
            is_deleted = 0
          and user_id = #{userId}
    </select>
    <select id="getMyOneTeamMiniAccountVo" resultMap="MyTeamMiniAccountVoMap">
        SELECT
            t1.nike_name AS name,
            t1.create_time AS create_time,
            t2.nike_name AS recommend_name,
            t3.member_level,
            IFNULL( sum( t6.amount ), 0 ) AS amount,
            t1.avatar_url
        FROM
            t_mini_account t1
                LEFT JOIN t_mini_account t2 ON t1.parent_id = t2.user_id
                AND t2.is_deleted = 0
                LEFT JOIN t_member_level t3 ON t1.member_level_id = t3.id
                AND t3.is_deleted = 0
                LEFT JOIN t_mini_account_extends t4 ON t4.user_id = t1.parent_id
                AND t4.is_deleted = 0
                LEFT JOIN t_mini_account_extends t5 ON t5.user_id = t1.user_id
                AND t5.is_deleted = 0
                LEFT JOIN t_mini_account_commission t6 ON t6.user_id = t4.shop_user_id
                AND t6.is_deleted = 0
                AND t6.source_shop_user_id = t5.shop_user_id
        WHERE
            t1.is_deleted = 0
            AND t1.whether_authorization = 1
            AND t1.parent_id = #{paramMap.userId}
            <if test="paramMap.name!=null and paramMap.name!='' ">
                AND t1.nike_name like concat('%',#{paramMap.name},'%')
            </if>
            <if test="paramMap.phone!=null and paramMap.phone!='' ">
                AND t1.phone like concat('%',#{paramMap.phone},'%')
            </if>
            <if test="paramMap.memberLevel!=null and paramMap.memberLevel!='' ">
                AND t3.member_level like concat('%',#{paramMap.memberLevel},'%')
            </if>
        GROUP BY
            t1.nike_name,
            t1.create_time,
            t2.nike_name,
            t3.member_level,
            t3.level,
            t1.avatar_url
        <if test="paramMap.createTimeSort!=null and paramMap.createTimeSort==1">
            order by t1.create_time asc
        </if>
        <if test="paramMap.createTimeSort!=null and paramMap.createTimeSort==2">
            order by t1.create_time desc
        </if>
        <if test="paramMap.amountSort!=null and paramMap.amountSort==1">
            order by sum(t6.amount) asc
        </if>
        <if test="paramMap.amountSort!=null and paramMap.amountSort==2">
            order by sum(t6.amount) desc
        </if>
        <if test="paramMap.levelSort!=null and paramMap.levelSort==1">
            order by t3.level asc
        </if>
        <if test="paramMap.levelSort!=null and paramMap.levelSort==2">
            order by t3.level desc
        </if>
    </select>

    <select id="getMyTwoTeamMiniAccountVo" resultMap="MyTeamMiniAccountVoMap">
        SELECT
        t1.nike_name AS name,
        t1.create_time AS create_time,
        t2.nike_name AS recommend_name,
        t3.member_level,
        IFNULL( sum( t6.amount ), 0 ) AS amount,
        t1.avatar_url
        FROM
        t_mini_account t1
        LEFT JOIN t_mini_account t2 ON t1.parent_id = t2.user_id
        AND t2.is_deleted = 0
        LEFT JOIN t_member_level t3 ON t1.member_level_id = t3.id
        AND t3.is_deleted = 0
        LEFT JOIN t_mini_account_extends t4 ON t4.user_id = t1.above_parent_id
        AND t4.is_deleted = 0
        LEFT JOIN t_mini_account_extends t5 ON t5.user_id = t1.user_id
        AND t5.is_deleted = 0
        LEFT JOIN t_mini_account_commission t6 ON t6.user_id = t4.shop_user_id
        AND t6.is_deleted = 0
        AND t6.source_shop_user_id = t5.shop_user_id
        WHERE
        t1.is_deleted = 0
        AND t1.whether_authorization = 1
        AND t1.above_parent_id = #{paramMap.userId}
        <if test="paramMap.name!=null and paramMap.name!='' ">
            AND t1.nike_name like concat('%',#{paramMap.name},'%')
        </if>
        <if test="paramMap.phone!=null and paramMap.phone!='' ">
            AND t1.phone like concat('%',#{paramMap.phone},'%')
        </if>
        <if test="paramMap.memberLevel!=null and paramMap.memberLevel!='' ">
            AND t3.member_level like concat('%',#{paramMap.memberLevel},'%')
        </if>
        GROUP BY
        t1.nike_name,
        t1.create_time,
        t2.nike_name,
        t3.member_level,
        t3.level,
        t1.avatar_url
        <if test="paramMap.createTimeSort!=null and paramMap.createTimeSort==1">
            order by t1.create_time asc
        </if>
        <if test="paramMap.createTimeSort!=null and paramMap.createTimeSort==2">
            order by t1.create_time desc
        </if>
        <if test="paramMap.amountSort!=null and paramMap.amountSort==1">
            order by sum(t6.amount) asc
        </if>
        <if test="paramMap.amountSort!=null and paramMap.amountSort==2">
            order by sum(t6.amount) desc
        </if>
        <if test="paramMap.levelSort!=null and paramMap.levelSort==1">
            order by t3.level asc
        </if>
        <if test="paramMap.levelSort!=null and paramMap.levelSort==2">
            order by t3.level desc
        </if>
    </select>

    <select id="getMiniAccountChildVo" resultMap="MiniAccountChildVoMap">

        SELECT
            a.user_id,
            a.nike_name,
            a.phone,
            b.member_level
        FROM
            t_mini_account a
                LEFT JOIN t_member_level b ON a.member_level_id = b.id
        WHERE
            a.is_deleted = 0
            AND a.whether_authorization = 1
            AND a.parent_id = #{userId}
    </select>
    <select id="getMiniAccountParentList" resultMap="MiniAccountChildVoMap">
        SELECT
            a.user_id,
            a.nike_name,
            a.phone,
            b.member_level
        FROM
            t_mini_account a
                LEFT JOIN t_member_level b ON a.member_level_id = b.id
        WHERE
            a.is_deleted = 0
          AND a.whether_authorization = 1
          AND a.user_id !=#{paramMap.userId}
        <if test="paramMap.phone!=null and paramMap.phone!='' ">
            AND a.phone like concat('%',#{paramMap.phone},'%')
        </if>
        <if test="paramMap.nikeName!=null and paramMap.nikeName!='' ">
            AND a.nike_name like concat('%',#{paramMap.nikeName},'%')
        </if>
        <if test="paramMap.memberLevel!=null and paramMap.memberLevel!='' ">
            AND b.member_level like concat('%',#{paramMap.memberLevel},'%')
        </if>
    </select>
    <select id="getMiniAccountChildList" resultMap="MiniAccountChildVoMap">
        SELECT
        a.user_id,
        a.nike_name,
        a.phone,
        b.member_level
        FROM
        t_mini_account a
        LEFT JOIN t_member_level b ON a.member_level_id = b.id
        WHERE
        a.is_deleted = 0
        AND a.whether_authorization = 1
        AND a.user_id !=#{paramMap.userId}
        and ( a.parent_id is null or a.parent_id = '' or (a.parent_id = #{paramMap.parentId}))
        <if test="paramMap.phone!=null and paramMap.phone!='' ">
            AND a.phone like concat('%',#{paramMap.phone},'%')
        </if>
        <if test="paramMap.nikeName!=null and paramMap.nikeName!='' ">
            AND a.nike_name like concat('%',#{paramMap.nikeName},'%')
        </if>
        <if test="paramMap.memberLevel!=null and paramMap.memberLevel!='' ">
            AND b.member_level like concat('%',#{paramMap.memberLevel},'%')
        </if>
        <if test="paramMap.tAboveParentId!=null and paramMap.tAboveParentId!='' ">
            AND a.user_id !=#{paramMap.tAboveParentId}
        </if>
        <if test="paramMap.tParentId!=null and paramMap.tParentId!='' ">
            AND a.user_id !=#{paramMap.tParentId}
        </if>
    </select>
    <select id="getBindMiniAccount" resultMap="BindMiniAccountMap">
        SELECT
            t1.user_id,
            t1.nike_name,
            t1.phone,
            DATE_FORMAT( t1.first_login_time, '%Y-%m-%d' ) AS join_date
        FROM
            t_mini_account t1
        WHERE
            t1.is_deleted = 0
          AND t1.whether_authorization = 1
          AND (ifnull(t1.account_id,'') = '' or ifnull(t1.account_id,'') = #{paramMap.accountId})
        <if test="paramMap.nikeName!=null and paramMap.nikeName!='' ">
            AND t1.nike_name like concat('%',#{paramMap.nikeName},'%')
        </if>
        <if test="paramMap.phone!=null and paramMap.phone!='' ">
            AND t1.phone like concat('%',#{paramMap.phone},'%')
        </if>
    </select>
    <select id="searchMiniAccountDetail" resultMap="MiniAccountDetailMap">
        SELECT
            t1.id,
            t1.user_id,
            t1.nike_name,
            t1.phone,
            IFNULL( t1.common_integral, 0 ) integral,
            IFNULL( t1.used_integral, 0 ) used_integral,
            IFNULL( t1.current_integral, 0 ) current_integral,
            IFNULL( t1.sale_integral, 0 ) sale_integral,
            IFNULL( t1.integral, 0 ) all_integral,
            t1.card_number,
            t1.first_login_time,
            t3.nike_name as recommend_name,
            t4.shop_user_id
        FROM
            t_mini_account t1
                left join t_member_level t2 on t1.member_level_id = t2.id
                left join t_mini_account t3 on t1.parent_id = t3.user_id
                left join t_mini_account_extends t4 on t4.user_id = t1.user_id
        WHERE
            t1.is_deleted = 0 and t1.whether_authorization = 1
        <if test="paramMap.phone!=null and paramMap.phone!='' ">
            AND t1.phone like concat('%',#{paramMap.phone},'%')
        </if>
        <if test="paramMap.nikeName!=null and paramMap.nikeName!='' ">
            AND t1.nike_name like concat('%',#{paramMap.nikeName},'%')
        </if>
    </select>
    <select id="getMiniAccountDetail" resultMap="MiniAccountDetailMap">
        SELECT
            t1.id,
            t1.user_id,
            t1.card_number,
            t1.nike_name,
            t2.member_level,
            t1.first_login_time,
            t1.phone,
            t3.nike_name as parent_name,
            t4.consume_num,
            IFNULL( t4.consume_num, 0 ) consume_num,
            IFNULL( t4.consume_totle_money, 0 ) consume_totle_money,
            IFNULL( t4.member_money, 0 ) member_money,
            t1.age,
            t1.gender,
            t1.user_name,
            DATE_FORMAT(t1.birthday, '%Y-%m-%d') as birthday,
            t1.card,
            t4.wx_account_url,
            t4.alipay_account_url,
            ifnull(t4.card_authorization,0) as card_authorization
        FROM
            t_mini_account t1
                LEFT JOIN t_member_level t2 ON t2.id = t1.member_level_id and t2.is_deleted = 0
                left join t_mini_account t3 on t1.parent_id = t3.user_id
                left join t_mini_account_extends t4 on t4.user_id = t1.user_id
        where t4.shop_user_id = #{userId}
    </select>
    <select id="getAccountSendWxBirthDayMessage" resultMap="AccountSendWxBirthDayMessageMap">
        SELECT
            t1.user_id,
            t1.nike_name,
            t1.user_name,
            t1.tenant_id
        FROM
            t_mini_account t1
        WHERE
            t1.is_deleted = 0
            AND DATE_FORMAT( t1.birthday, '%m-%d' ) = DATE_FORMAT( NOW(), '%m-%d' );
    </select>
    <select id="getAllShopUserId" resultType="java.lang.String">
        SELECT
            t2.shop_user_id
        FROM
            t_mini_account t1
                LEFT JOIN t_mini_account_extends t2 ON t1.user_id = t2.user_id
        WHERE
            t1.is_deleted = 0
          AND t1.whether_authorization = 1
          AND t2.is_blacklist = 0
    </select>
    <select id="searchMiniAccountRoyalty" resultMap="MiniAccountRoyaltyMap">
        SELECT
            t1.id,
            t1.user_id,
            t1.nike_name,
            t1.phone,
            t1.member_level_id,
            t2.member_level AS member_level_name,
            ifnull(t1.reward_royalty,0) as reward_royalty
        FROM
            t_mini_account t1
        LEFT JOIN
            t_member_level t2 ON t1.member_level_id = t2.id
        WHERE
            t1.is_deleted = 0
          AND t1.whether_authorization = 1
        <if test="paramMap.phone!=null and paramMap.phone!='' ">
            AND t1.phone like concat('%',#{paramMap.phone},'%')
        </if>
        <if test="paramMap.nikeName!=null and paramMap.nikeName!='' ">
            AND t1.nike_name like concat('%',#{paramMap.nikeName},'%')
        </if>
        <if test="paramMap.rewardRoyaltyFlag!=null and paramMap.rewardRoyaltyFlag == true">
            AND ifnull(t1.reward_royalty,0) !=0
        </if>
        ORDER BY
            t1.first_login_time DESC
    </select>
    <select id="getChooseAccount" resultMap="ChooseAccountMap">
        SELECT
            t1.user_id,
            t1.nike_name,
            t1.phone,
            t2.consume_totle_money,
            t3.member_level
        FROM
            t_mini_account t1
        left join
            t_mini_account_extends t2 on t1.user_id = t2.user_id
        left join
            t_member_level t3 on t1.member_level_id = t3.id
        WHERE
            t1.is_deleted = 0 AND t1.whether_authorization = 1
        <if test="param.nikeName!=null and param.nikeName!='' ">
            AND t1.nike_name like concat('%',#{param.nikeName},'%')
        </if>
        <if test="param.accountIds!=null and  param.accountIds.size() > 0">
            AND t1.user_id NOT IN
            <foreach collection="param.accountIds" item="accountId" open="(" separator="," close=")">
                #{accountId}
            </foreach>
        </if>
        <if test="param.phone!=null and param.phone!='' ">
            AND t1.phone like concat('%',#{param.phone},'%')
        </if>
        <if test="param.memberLevel!=null and param.memberLevel!='' ">
            AND t3.member_level like concat('%',#{param.memberLevel},'%')
        </if>
        <if test="param.tagName!=null and param.tagName!='' ">
            AND (t2.shop_user_id in
            (
            SELECT
            t4.user_id
            FROM
            t_mini_account_tag_group t4
            LEFT JOIN t_mini_account_tag t5 ON t4.tag_id = t5.id and t5.is_deleted = 0
            where t4.is_deleted = 0 and t5.tag_name like concat('%',#{param.tagName},'%'))
            )
        </if>
    </select>

    <select id="getChooseAccountByUserId" resultMap="ChooseAccountMap">
        SELECT
            t1.user_id,
            t1.nike_name,
            t1.phone,
            t2.consume_totle_money,
            t3.member_level
        FROM
            t_mini_account t1
                left join
            t_mini_account_extends t2 on t1.user_id = t2.user_id
                left join
            t_member_level t3 on t1.member_level_id = t3.id
        WHERE
            t1.is_deleted = 0 AND t1.whether_authorization = 1 and t1.user_id = #{userId}
    </select>
    <select id="getApiMiniAccountCard" resultMap="ApiMiniAccountCardMap">
        SELECT
            t1.user_name,
            t1.card,
            t2.id_card_face_url,
            t2.id_card_back_url,
            ifnull( t2.card_authorization, 0 ) AS card_authorization,
            t2.audit_platform_user_id,
            DATE_FORMAT( t2.audit_time, '%Y-%m-%d' ) AS audit_time,
            t2.card_audit_reason
        FROM
            t_mini_account t1
                LEFT JOIN t_mini_account_extends t2 ON t1.user_id = t2.user_id
        WHERE
            t1.is_deleted = 0
          AND t2.is_deleted = 0
          AND t1.user_id = #{userId}
    </select>
    <update id="updateSaleFlag">
        update t_mini_account set sale_flag=#{saleFlag} where id in
        <foreach collection="accountIds" item="accountId" open="(" separator="," close=")">
            #{accountId}
        </foreach>
    </update>
    <select id="getDirectMemberCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            t_mini_account t1
                LEFT JOIN t_member_level_relation t2 ON t1.user_id = t2.user_id
                LEFT JOIN t_member_level t3 ON t2.member_level_id = t3.id
        WHERE
            t1.is_deleted = 0
          AND t2.is_deleted = 0
          AND t3.is_deleted = 0
          AND t1.whether_authorization = 1
          and t3.member_flag = 1
          and t2.member_type_id = #{memberTypeId}
          and t1.parent_id = #{userId}
    </select>
    <select id="getInDirectMemberCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            t_mini_account t1
                LEFT JOIN t_member_level_relation t2 ON t1.user_id = t2.user_id
                LEFT JOIN t_member_level t3 ON t2.member_level_id = t3.id
        WHERE
            t1.is_deleted = 0
          AND t2.is_deleted = 0
          AND t3.is_deleted = 0
          AND t1.whether_authorization = 1
          and t3.member_flag = 1
          and t2.member_type_id = #{memberTypeId}
          and t1.above_parent_id = #{userId}
    </select>
    <select id="getRegionMemberCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            t_mini_account t1
                LEFT JOIN t_member_level_relation t2 ON t1.user_id = t2.user_id
        WHERE
            t1.is_deleted = 0
          AND t2.is_deleted = 0
          AND t2.member_type_id = #{memberTypeId}
          AND t1.parent_id = #{userId}
    </select>
    <select id="getDirectMemberCountByMemberTypeIds" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            t_mini_account t1
                LEFT JOIN t_member_level_relation t2 ON t1.user_id = t2.user_id
        WHERE
            t1.is_deleted = 0
          AND t1.parent_id = #{userId}
        <if test="memberLevelIds!=null and  memberLevelIds.size() > 0">
            AND t1.user_id IN (
                select
                  t2.user_id
                from
                     t_member_level_relation t2
                where
                    t2.is_deleted = 0
                    and t2.member_level_id in
                    <foreach collection="memberLevelIds" item="memberLevelId" open="(" separator="," close=")">
                        #{memberLevelId}
                    </foreach>
                )
        </if>
    </select>

    <!-- 销售汇总表查询映射结果 -->
    <resultMap id="SalesReportResultMap" type="com.medusa.gruul.account.model.vo.MemberSalesReportVo">
        <result column="user_id" property="userId"/>
        <result column="member_type_id" property="memberTypeId"/>
        <result column="member_type_name" property="memberTypeName"/>
        <result column="member_level_id" property="memberLevelId"/>
        <result column="member_level_name" property="memberLevelName"/>
        <result column="user_name" property="userName"/>
        <result column="user_phone" property="userPhone"/>
        <result column="purchase_count" property="purchaseCount"/>
        <result column="purchase_amount" property="purchaseAmount"/>
        <result column="purchase_quantity" property="purchaseQuantity"/>
        <result column="repurchase_count" property="repurchaseCount"/>
        <result column="repurchase_amount" property="repurchaseAmount"/>
        <result column="repurchase_quantity" property="repurchaseQuantity"/>
    </resultMap>

    <select id="getSalesReport" resultMap="SalesReportResultMap">
        SELECT
            mlr.member_type_id,
            mt.name AS member_type_name,
            mlr.member_level_id,
            ml.member_level AS member_level_name,
            mae.user_id,
            ma.nike_name AS user_name,
            ma.phone AS user_phone,
            COUNT(DISTINCT o.id) AS purchase_count,
            SUM(o.pay_amount) AS purchase_amount,
            SUM(oi.product_quantity) AS purchase_quantity,
            COUNT(DISTINCT CASE WHEN oi.buy_type=2 THEN o.id END) AS repurchase_count,
            SUM(CASE WHEN oi.buy_type=2 THEN oi.product_price * oi.product_quantity ELSE 0 END) AS repurchase_amount,
            SUM(CASE WHEN oi.buy_type=2 THEN oi.product_quantity ELSE 0 END) AS repurchase_quantity
        FROM
            t_mini_account ma
        LEFT JOIN t_member_level_relation mlr ON ma.user_id = mlr.user_id
        LEFT JOIN t_member_level ml ON mlr.member_level_id = ml.id
        LEFT JOIN t_member_type mt ON mlr.member_type_id = mt.id
        LEFT JOIN t_mini_account_extends mae ON ma.user_id = mae.user_id
        LEFT JOIN t_order o ON mae.shop_user_id = o.user_id
        LEFT JOIN t_order_item oi ON o.id = oi.order_id
        WHERE
            o.status = 105 and o.pay_amount > 0
        <if test="paramMap.userName != null and paramMap.userName != ''">
            AND ma.nike_name LIKE CONCAT('%', #{paramMap.userName}, '%')
        </if>
        <if test="paramMap.userPhone != null and paramMap.userPhone != ''">
            AND ma.phone LIKE CONCAT('%', #{paramMap.userPhone}, '%')
        </if>
        <if test="paramMap.memberTypeId != null and paramMap.memberTypeId.size > 0"> 
            AND mlr.member_type_id IN
            <foreach collection="paramMap.memberTypeId" item="typeId" open="(" separator="," close=")">
                #{typeId}
            </foreach>
        </if>
        <if test="paramMap.memberLevelId != null and paramMap.memberLevelId.size > 0">
            AND mlr.member_level_id IN
            <foreach collection="paramMap.memberLevelId" item="levelId" open="(" separator="," close=")">
                #{levelId}
            </foreach>
        </if>
        <if test="paramMap.startTime != null">
            AND o.pay_time &gt;= #{paramMap.startTime}
        </if>
        <if test="paramMap.endTime != null">
            AND o.pay_time &lt;= #{paramMap.endTime}
        </if>
        GROUP BY
            mlr.member_type_id, mlr.member_level_id, mae.user_id, ma.id
        ORDER BY
            ma.id DESC
    </select>

    <!-- 会员类型汇总查询映射结果 -->
    <resultMap id="MemberTypeReportResultMap" type="com.medusa.gruul.account.model.vo.MemberTypeReportVo">
        <result column="member_type_id" property="memberTypeId"/>
        <result column="member_type_name" property="memberTypeName"/>
        <result column="member_count" property="memberCount"/>
        <result column="total_amount" property="totalAmount"/>
    </resultMap>

    <select id="getMemberTypeReport" resultMap="MemberTypeReportResultMap">
        SELECT
            mlr.member_type_id,
            mt.name AS member_type_name,
            COUNT(DISTINCT ma.user_id) AS member_count,
            COALESCE(SUM(o.pay_amount), 0) AS total_amount
        FROM
            t_mini_account ma
        LEFT JOIN t_member_level_relation mlr ON ma.user_id = mlr.user_id
        LEFT JOIN t_member_type mt ON mlr.member_type_id = mt.id
        LEFT JOIN t_mini_account_extends mae ON ma.user_id = mae.user_id
        LEFT JOIN t_order o ON mae.shop_user_id = o.user_id
        WHERE
            o.status = 105 and o.pay_amount > 0
        <if test="paramMap.userName != null and paramMap.userName != ''">
            AND ma.nike_name LIKE CONCAT('%', #{paramMap.userName}, '%')
        </if>
        <if test="paramMap.userPhone != null and paramMap.userPhone != ''">
            AND ma.phone LIKE CONCAT('%', #{paramMap.userPhone}, '%')
        </if>
        <if test="paramMap.memberTypeIds != null and paramMap.memberTypeIds.size > 0">
            AND mlr.member_type_id IN
            <foreach collection="paramMap.memberTypeIds" item="typeId" open="(" separator="," close=")">
                #{typeId}
            </foreach>
        </if>
        <if test="paramMap.registerStartTime != null">
            AND ma.create_time &gt;= #{paramMap.registerStartTime}
        </if>
        <if test="paramMap.registerEndTime != null">
            AND ma.create_time &lt;= #{paramMap.registerEndTime}
        </if>
        GROUP BY
            mlr.member_type_id, mt.name
        ORDER BY
            mlr.member_type_id DESC
    </select>

    <!-- 会员等级汇总查询映射结果 -->
    <resultMap id="MemberLevelReportResultMap" type="com.medusa.gruul.account.model.vo.MemberLevelReportVo">
        <result column="member_level_id" property="memberLevelId"/>
        <result column="member_level_name" property="memberLevelName"/>
        <result column="member_count" property="memberCount"/>
        <result column="total_amount" property="totalAmount"/>
    </resultMap>

    <select id="getMemberLevelReport" resultMap="MemberLevelReportResultMap">
        SELECT
            mlr.member_level_id,
            ml.member_level AS member_level_name,
            COUNT(DISTINCT ma.user_id) AS member_count,
            COALESCE(SUM(o.pay_amount), 0) AS total_amount
        FROM
            t_mini_account ma
        LEFT JOIN t_member_level_relation mlr ON ma.user_id = mlr.user_id
        LEFT JOIN t_member_level ml ON mlr.member_level_id = ml.id
        LEFT JOIN t_member_type mt ON mlr.member_type_id = mt.id
        LEFT JOIN t_mini_account_extends mae ON ma.user_id = mae.user_id
        LEFT JOIN t_order o ON mae.shop_user_id = o.user_id
        WHERE
            o.status = 105 and o.pay_amount > 0
        <if test="paramMap.memberTypeId != null and paramMap.memberTypeId != ''">
            AND mlr.member_type_id = #{paramMap.memberTypeId}
        </if>
        <if test="paramMap.userName != null and paramMap.userName != ''">
            AND ma.nike_name LIKE CONCAT('%', #{paramMap.userName}, '%')
        </if>
        <if test="paramMap.userPhone != null and paramMap.userPhone != ''">
            AND ma.phone LIKE CONCAT('%', #{paramMap.userPhone}, '%')
        </if>
        <if test="paramMap.registerStartTime != null">
            AND ma.create_time &gt;= #{paramMap.registerStartTime}
        </if>
        <if test="paramMap.registerEndTime != null">
            AND ma.create_time &lt;= #{paramMap.registerEndTime}
        </if>
        GROUP BY
            mlr.member_level_id, ml.member_level
        ORDER BY
            ma.id DESC
    </select>
</mapper>
