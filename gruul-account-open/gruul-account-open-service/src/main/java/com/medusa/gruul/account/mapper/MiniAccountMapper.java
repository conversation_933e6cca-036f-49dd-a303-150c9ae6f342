package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.account.api.entity.MiniAccount;
import com.medusa.gruul.account.api.model.MiniAccountExtDto;
import com.medusa.gruul.account.api.model.param.ChooseAccountParam;
import com.medusa.gruul.account.api.model.param.MemberSalesReportParam;
import com.medusa.gruul.account.api.model.vo.ChooseAccountVo;
import com.medusa.gruul.account.model.dto.BlacklistUserDto;
import com.medusa.gruul.account.model.dto.UserListDto;
import com.medusa.gruul.account.model.param.*;
import com.medusa.gruul.account.model.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 小程序用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-18
 */
@Mapper
public interface MiniAccountMapper extends BaseMapper<MiniAccount> {

    /**
     * PC端获取用户列表
     *
     * @param userListDtoPage 分页数据
     * @param paramMap        查询条件参数
     * @return com.medusa.gruul.account.model.dto.UserListDto
     */
    IPage<UserListDto> selectByUserList(Page<UserListDto> userListDtoPage, @Param("paramMap") Map<String, Object> paramMap);

    /**
     * 外部系统获取用户列表
     *
     * @param userListDtoPage 分页数据
     * @param paramMap        查询条件参数
     * @return com.medusa.gruul.account.model.dto.UserListDto
     */
    IPage<UserListDto> selectExternalByUserList(Page<UserListDto> userListDtoPage, @Param("paramMap") Map<String, Object> paramMap);

    /**
     * 批量修改用户发送状态
     *
     * @param accountIds the order ids
     * @param    sendStatus
     * @return void
     * <AUTHOR>
     * @date 2022 /05/24 09:12
     */
    void updateSendStatus(@Param(value = "accountIds")List<String> accountIds,@Param(value = "sendStatus")String sendStatus);

    /**
     * PC端获取父级的父级Id
     *
     *
     * @param userId        查询条件参数
     *
     */
    UserListDto getUserParent(Integer userId);

    /**
     * 获取黑名单列表
     *
     * @param blacklistUserDtoPage 分页数据
     * @param paramMap             查询条件参数
     * @return com.medusa.gruul.account.model.dto.BlacklistUserDto
     */
    IPage<BlacklistUserDto> selectByBlackListUser(Page<BlacklistUserDto> blacklistUserDtoPage, @Param("paramMap") Map<String, Object> paramMap);

    /**
     * 根据用户店铺id查询用户基本信息
     *
     * @param shopUserIds 用户店铺id
     * @return
     */
    List<MiniAccountExtDto> selectByShopUserIds(@Param("shopUserIds") List<String> shopUserIds);



    /**
     * 获取指定用户的唯一id
     *
     * @param shopUserId 店铺用户id
     * @return 唯一id
     */
    String getUserInfoByShopUserId( @Param("shopUserId") String shopUserId);

    /**
     * 获取指定用户的积分值
     *
     * @param shopUserId 店铺用户id
     * @return 积分值
     */
    BigDecimal getIntegralByShopUserId( @Param("shopUserId") String shopUserId);


    /**
     * 根据用户更新用户返利余额
     *
     * @param userId   店铺用户id
     * @param amount   余额
     * @return 0 or 1
     */
    int updateRebateBonus( @Param("userId") String userId, @Param("amount") BigDecimal amount);

    /**
     * 根据店铺用户id查询用户所在店铺唯一id
     *
     * @param shopUserId 店铺用户id
     * @return com.medusa.gruul.account.api.entity.MiniAccount
     */
    MiniAccount selectByShopUserId(@Param("shopUserId") String shopUserId);

    /**
     * 获取会员权益
     *
     *
     * @return java.lang.Integer
     */
    List<MemberLevelRightsVo> getPowerAll();

    /**
     * 根据用户id获取用户积分信息
     * @param userId
     * @return
     */
    UserIntegralVo getUserInfoIntegral(String userId);

    /**
     * 我的一级团队
     * @param myTeamMiniAccountVoPage
     * @param myTeamMiniAccountParam
     * @return
     */
    IPage<MyTeamMiniAccountVo>getMyOneTeamMiniAccountVo(Page<MyTeamMiniAccountVo> myTeamMiniAccountVoPage, @Param("paramMap")MyTeamMiniAccountParam myTeamMiniAccountParam);

    /**
     * 我的二级
     * @param myTeamMiniAccountVoPage
     * @param myTeamMiniAccountParam
     * @return
     */
    IPage<MyTeamMiniAccountVo>getMyTwoTeamMiniAccountVo(Page<MyTeamMiniAccountVo> myTeamMiniAccountVoPage, @Param("paramMap")MyTeamMiniAccountParam myTeamMiniAccountParam);

    /**
     * 获取用户下级用户信息
     * @param userId
     * @return
     */
    List<MiniAccountChildVo> getMiniAccountChildVo(@Param("userId")String userId);

    /**
     * 获取可添加用户上级列表
     * @param miniAccountChildVoPage
     * @param miniAccountChildParam
     * @return
     */
    IPage<MiniAccountChildVo> getMiniAccountParentList(Page<MiniAccountChildVo> miniAccountChildVoPage, @Param("paramMap") MiniAccountChildParam miniAccountChildParam);

    /**
     * 获取可添加用户下级列表
     * @param miniAccountChildVoPage
     * @param miniAccountChildParam
     * @return
     */
    IPage<MiniAccountChildVo> getMiniAccountChildList(Page<MiniAccountChildVo> miniAccountChildVoPage, @Param("paramMap") MiniAccountChildParam miniAccountChildParam);

    /**
     * 分页查询可添加的小程序客户
     * @param page
     * @param paramMap
     * @return
     */
    IPage<BindMiniAccountVo> getBindMiniAccount(Page<BindMiniAccountVo>page, @Param("paramMap")BindMiniAccountParam paramMap);

    /**
     * 获取用户详情
     * @param userId
     * @return
     */
    MiniAccountDetailVo getMiniAccountDetail(@Param("userId")String userId);

    /**
     * 获取发送会员生日祝福会员信息
     * @return
     */
    List<AccountSendWxBirthDayMessageVo> getAccountSendWxBirthDayMessage();

    /**
     * 获取所以注册会员信息shopUserId
     * @return
     */
    List<String> getAllShopUserId();

    /**
     * 分页查客户明细
     * @param page
     * @param paramMap
     * @return
     */
    IPage<MiniAccountDetailVo> searchMiniAccountDetail(Page<MiniAccountDetailVo>page, @Param("paramMap") MiniAccountDetailParam paramMap);

    /**
     * 分页查询提成汇总
     * @param page
     * @param param
     * @return
     */
    IPage<MiniAccountRoyaltyVo>searchMiniAccountRoyalty(Page<MiniAccountRoyaltyVo>page, @Param("paramMap")MiniAccountRoyaltyParam param);

    /**
     * 分页获取可选择客户
     * @param page
     * @param param
     * @return
     */
    IPage<ChooseAccountVo>getChooseAccount(Page<ChooseAccountVo>page, @Param("param") ChooseAccountParam param);

    /**
     * 根据userId获取可添加客户详情
     * @param userId
     * @return
     */
    ChooseAccountVo getChooseAccountByUserId(@Param("userId") String userId);

    /**
     * 获取小程序用户身份证认证信息
     * @param userId
     * @return
     */
    ApiMiniAccountCardVo getApiMiniAccountCard(@Param("userId") String userId);

    /**
     * 批量修改业务员标识
     * @param accountIds
     * @param saleFlag
     */
    void updateSaleFlag(@Param(value = "accountIds")List<String> accountIds,@Param(value = "saleFlag")Integer saleFlag);

    /**
     * 查询出用户id直推该会员类型下会员等级属于会员体系的会员数量
     * @param memberTypeId
     * @param userId
     * @return
     */
    Integer getDirectMemberCount(@Param(value = "memberTypeId")Long memberTypeId, @Param(value = "userId")String userId);

    /**
     * 查询出用户id间推该会员类型下会员等级属于会员体系的会员数量
     * @param memberTypeId
     * @param userId
     * @return
     */
    Integer getInDirectMemberCount(@Param(value = "memberTypeId")Long memberTypeId, @Param(value = "userId")String userId);

    /**
     * 查询直推会员属于区域类型会员体系会员数
     * @param memberTypeId
     * @param userId
     * @return
     */
    Integer getRegionMemberCount(@Param(value = "memberTypeId")String memberTypeId, @Param(value = "userId")String userId);

    /**
     * 查询用户直推会员等级数
     * @param memberLevelIds
     * @param userId
     * @return
     */
    Integer getDirectMemberCountByMemberTypeIds(@Param(value = "memberLevelIds")List<String> memberLevelIds, @Param(value = "userId")String userId);

    /**
     * 获取销售汇总表数据
     */
    IPage<MemberSalesReportVo> getSalesReport(Page<MemberSalesReportVo> page, @Param("paramMap") MemberSalesReportParam param);

    /**
     * 获取会员类型汇总数据
     */
    IPage<MemberTypeReportVo> getMemberTypeReport(Page<MemberTypeReportVo> page, @Param("paramMap") MemberReportParam param);

    /**
     * 获取会员等级汇总数据
     */
    IPage<MemberLevelReportVo> getMemberLevelReport(Page<MemberLevelReportVo> page, @Param("paramMap") MemberReportParam param);
}
