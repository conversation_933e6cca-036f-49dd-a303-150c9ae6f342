package com.medusa.gruul.account.model.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:43 2025/7/3
 */
@Data
@ApiModel(value = "新增或修改用户金豆明细DTO")
public class MiniAccountGoldenDto {

    @ApiModelProperty(value = "小程序用户id")
    private String userId;

    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "佣金类型:99->金豆划转;100->消费;104->订单消费")
    private Integer commissionType;

    @ApiModelProperty(value = "金豆")
    private BigDecimal amount;

    @ApiModelProperty(value = "订单类型:1->快递订单;2->充值订单;3->手动修改")
    private Integer orderType;

    @ApiModelProperty(value = "奖励活动id")
    private String rewardId;

    @ApiModelProperty(value = "奖励活动明细id")
    private String rewardDetId;

    @ApiModelProperty(value = "接收人用户ID")
    private String receiveUserId;

    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 金豆来源用户id
     */
    @ApiModelProperty(value = "金豆来源用户id")
    private String sourceShopUserId;

    /**
     * 数据来源，0-系统，1-后台
     */
    @ApiModelProperty(value = "数据来源，0-系统，1-后台")
    private Integer source;
}
