package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MiniAccount;
import com.medusa.gruul.account.api.entity.MiniAccountIntegral;
import com.medusa.gruul.account.api.model.param.MiniAccountPassTicketCodeParam;
import com.medusa.gruul.account.model.dto.MiniAccountIntegralDto;
import com.medusa.gruul.account.model.param.IntegralRankingParam;
import com.medusa.gruul.account.model.param.MiniAccountIntegralManageParam;
import com.medusa.gruul.account.model.param.MiniAccountIntegralParam;
import com.medusa.gruul.account.model.vo.AccountIntegralVo;
import com.medusa.gruul.account.model.vo.IntegralRankingVo;
import com.medusa.gruul.account.model.vo.MiniAccountIntegralManageVo;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.order.api.model.OrderVo;

import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: 用户积分服务类
 * @Date: Created in 15:36 2023/8/22
 */
public interface IMiniAccountIntegralService extends IService<MiniAccountIntegral> {

    /**
     * 添加用户积分
     * @param miniAccountIntegralDto
     */
    void addMiniAccountIntegral(MiniAccountIntegralDto miniAccountIntegralDto);

    /**
     * 查询用户积分明细
     * @param miniAccountIntegralParam
     * @return
     */
    PageUtils<AccountIntegralVo> searchAccountIntegral(MiniAccountIntegralParam miniAccountIntegralParam);


    /**
     * 用户授权成功添加积分
     * @param miniAccount
     */
    void loginAddIntegral(MiniAccount miniAccount);

    /**
     * 用户每天登录获取积分
     */
    MiniAccount loginAddIntegralDay(MiniAccount miniAccount);

    /**
     * 处理订单类型积分
     */
    void handleIntegral(OrderVo orderVo);

    /**
     * 扣减用户积分
     * @param orderVo
     */
    void deductionIntegral(OrderVo orderVo);

    /**
     * 修改用户积分
     * @param miniAccountIntegralDto
     */
    void updateIntegral(MiniAccountIntegralDto miniAccountIntegralDto);

    /**
     * 分页查询积分排行
     * @param param
     * @return
     */
    PageUtils<IntegralRankingVo>searchIntegralRanking(IntegralRankingParam param);

    /**
     * 导出积分排行
     * @param param
     */
    void exportIntegralRanking(IntegralRankingParam param);

    /**
     * 分页查询积分明细列表
     * @param param
     * @return
     */
    PageUtils<MiniAccountIntegralManageVo> searchMiniAccountIntegralManage(MiniAccountIntegralManageParam param);

    /**
     * 导出积分明细
     * @param param
     */
    void exportMiniAccountIntegralManage(MiniAccountIntegralManageParam param);


    /**
     * 积分回退
     * @param orderId
     */
    void rollback(Long orderId);
}
