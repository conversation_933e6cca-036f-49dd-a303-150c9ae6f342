package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.account.api.entity.MiniAccountCommission;
import com.medusa.gruul.account.model.param.DistributionOrderParam;
import com.medusa.gruul.account.model.param.MiniAccountCommissionManageParam;
import com.medusa.gruul.account.model.param.MiniAccountCommissionParam;
import com.medusa.gruul.account.model.param.MiniAccountRoyaltyDetParam;
import com.medusa.gruul.account.model.vo.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;


/**
 * <AUTHOR> by rbw
 * @date created in 2023/08/25
 */
@Repository
public interface MiniAccountCommissionMapper extends BaseMapper<MiniAccountCommission> {

    /**
     * 获取用户佣金信息
     * @param userId
     * @return
     */
    UserCommissionVo getUserCommissionVo(@Param(value = "userId") String userId);

    /**
     * 获取用户分销列表
     * @param page
     * @param distributionOrderParam
     * @return
     */
    IPage<DistributionOrderVo>getDistributionOrderVo(Page<DistributionOrderVo> page,@Param("paramMap")DistributionOrderParam distributionOrderParam);

    /**
     * 分页查询提成明细
     * @param page
     * @param param
     * @return
     */
    IPage<MiniAccountRoyaltyDetVo>searchMiniAccountRoyaltyDet(Page<MiniAccountRoyaltyDetVo> page,@Param("paramMap") MiniAccountRoyaltyDetParam param);

    /**
     * 分页查询佣金明细
     * @param page
     * @param param
     * @return
     */
    IPage<MiniAccountCommissionManageVo> searchMiniAccountCommissionDet(Page<MiniAccountCommissionManageVo> page, @Param("paramMap") MiniAccountCommissionManageParam param);

    /**
     * 分页查询小程序佣金明细
     * @param page
     * @param param
     * @return
     */
    IPage<MiniAccountCommissionVo> pageMyCommission(Page<MiniAccountCommissionVo> page, @Param("paramMap")MiniAccountCommissionParam param);
}
