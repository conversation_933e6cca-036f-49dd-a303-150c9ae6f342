package com.medusa.gruul.account.web.controller;

import com.medusa.gruul.account.api.model.param.MiniAccountPassTicketCodeParam;
import com.medusa.gruul.account.model.dto.MiniAccountIntegralDto;
import com.medusa.gruul.account.model.param.IntegralRankingParam;
import com.medusa.gruul.account.model.param.MiniAccountIntegralManageParam;
import com.medusa.gruul.account.model.param.MiniAccountIntegralParam;
import com.medusa.gruul.account.model.vo.*;
import com.medusa.gruul.account.service.IMiniAccountIntegralService;
import com.medusa.gruul.account.service.IMiniAccountService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:28 2023/8/22
 */
@RestController
@RequestMapping("/mini-account-integral")
@Api(tags = "用户积分接口")
public class MiniAccountIntegralController {

    @Autowired
    private IMiniAccountService miniAccountService;
    @Autowired
    private IMiniAccountIntegralService miniAccountIntegralService;

    /**
     *
     * @return
     */
    @GetMapping("/getUserIntegral")
    @ApiOperation(value = "获取用户积分信息")
    public Result<UserIntegralVo> getUserIntegral(){
        UserIntegralVo userInfoIntegral = miniAccountService.getUserInfoIntegral();
        return Result.ok(userInfoIntegral);
    }

    /**
     * 获取用户积分明细
     * @param miniAccountIntegralParam
     * @return
     */
    @PostMapping("/getAccountIntegral")
    @ApiOperation(value = "获取用户积分明细")
    public Result<PageUtils<AccountIntegralVo>> getAccountIntegral(@RequestBody @Validated MiniAccountIntegralParam miniAccountIntegralParam){
        PageUtils<AccountIntegralVo> accountIntegralVoPageUtils = miniAccountIntegralService.searchAccountIntegral(miniAccountIntegralParam);
        return Result.ok(accountIntegralVoPageUtils);
    }

    /**
     * 修改用户积分
     * @param miniAccountIntegralDto
     * @return
     */
    @PostMapping("/updateIntegral")
    @ApiOperation(value = "修改用户积分")
    public Result updateIntegral(@RequestBody MiniAccountIntegralDto miniAccountIntegralDto){
        miniAccountIntegralService.updateIntegral(miniAccountIntegralDto);
        return Result.ok("修改成功");
    }

    /**
     * 分页查询积分排行
     * @param param
     * @return
     */
    @PostMapping("/searchIntegralRanking")
    @ApiOperation(value = "分页查询积分排行")
    public Result<PageUtils<IntegralRankingVo>> searchIntegralRanking(@RequestBody IntegralRankingParam param){
        PageUtils<IntegralRankingVo> pageUtils = miniAccountIntegralService.searchIntegralRanking(param);
        return Result.ok(pageUtils);
    }

    /**
     * 导出积分排行
     * @param param
     */
    @RequestMapping(value = "/exportIntegralRanking", method = RequestMethod.POST)
    @ApiOperation(value = "导出积分排行")
    public void exportIntegralRanking(@RequestBody IntegralRankingParam param){
        this.miniAccountIntegralService.exportIntegralRanking(param);
    }

    /**
     * 积分明细变更列表
     * @param param
     * @return
     */
    @RequestMapping(value = "/searchMiniAccountIntegralManage", method = RequestMethod.POST)
    @ApiOperation(value = "积分明细变更列表")
    public Result<PageUtils<MiniAccountIntegralManageVo>> searchMiniAccountIntegralManage(@RequestBody MiniAccountIntegralManageParam param){
        PageUtils<MiniAccountIntegralManageVo> pageUtils = miniAccountIntegralService.searchMiniAccountIntegralManage(param);
        return Result.ok(pageUtils);
    }

    /**
     * 导出积分明细
     * @param param
     */
    @PostMapping("/exportMiniAccountIntegralManage")
    @ApiOperation(value = "导出积分明细")
    public void exportMiniAccountIntegralManage(@RequestBody MiniAccountIntegralManageParam param) {
        miniAccountIntegralService.exportMiniAccountIntegralManage(param);
    }
}
