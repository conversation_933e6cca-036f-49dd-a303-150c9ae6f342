package com.medusa.gruul.account.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 会员等级汇总视图对象
 */
@Data
@ApiModel(value = "会员等级汇总数据")
public class MemberLevelReportVo {
    
    @ApiModelProperty(value = "会员等级ID")
    private String memberLevelId;
    
    @ApiModelProperty(value = "会员等级名称")
    private String memberLevelName;
    
    @ApiModelProperty(value = "会员数量")
    private Integer memberCount;
    
    @ApiModelProperty(value = "消费总金额")
    private BigDecimal totalAmount;
}
