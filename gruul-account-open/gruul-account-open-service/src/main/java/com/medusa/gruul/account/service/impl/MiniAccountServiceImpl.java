package com.medusa.gruul.account.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import cn.binarywang.wx.miniapp.util.crypt.WxMaCryptUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.*;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.*;
import com.medusa.gruul.account.api.enums.*;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.api.model.*;
import com.medusa.gruul.account.api.model.message.AccountReturnBalanceMessage;
import com.medusa.gruul.account.api.model.message.UpgradeMemberLevelMessage;
import com.medusa.gruul.account.api.model.param.ChooseAccountParam;
import com.medusa.gruul.account.api.model.param.MemberSalesReportParam;
import com.medusa.gruul.account.api.model.vo.ChooseAccountVo;
import com.medusa.gruul.account.conf.AccountRedis;
import com.medusa.gruul.account.conf.SnowflakeProperty;
import com.medusa.gruul.account.constant.RedisConstant;
import com.medusa.gruul.account.mapper.MiniAccountMapper;
import com.medusa.gruul.account.model.dto.*;
import com.medusa.gruul.account.model.param.*;
import com.medusa.gruul.account.model.vo.*;
import com.medusa.gruul.account.model.vo.UserInfoVo;
import com.medusa.gruul.account.mq.Sender;
import com.medusa.gruul.account.service.*;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.RegexConstants;
import com.medusa.gruul.common.core.constant.TimeConstants;
import com.medusa.gruul.common.core.constant.enums.*;
import com.medusa.gruul.common.core.encrypt.AESUtil;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.*;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.common.dto.CurMiniUserInfoDto;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.enums.SalesmanFlagEnum;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.vo.manager.ProductVo;
import com.medusa.gruul.order.api.feign.RemoteOrderService;
import com.medusa.gruul.order.api.model.OrderVo;
import com.medusa.gruul.order.api.model.RevertMiniAccountBalanceMessage;
import com.medusa.gruul.platform.api.entity.AccountInfo;
import com.medusa.gruul.platform.api.entity.SpecialSetting;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.platform.api.model.dto.LoginDto;
import com.medusa.gruul.platform.api.model.dto.ShopInfoDto;
import com.medusa.gruul.platform.api.model.dto.WxSendMessageDto;
import com.medusa.gruul.platform.api.model.vo.*;
import com.medusa.gruul.shops.api.entity.ShopsPartner;
import com.medusa.gruul.shops.api.enums.PromotionStatusEnum;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import com.medusa.gruul.shops.api.model.PrizeMemberMessage;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.bean.WxMpUserQuery;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import me.chanjar.weixin.mp.bean.result.WxMpUserList;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <p>
 * 小程序用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-18
 */
@Service
@Slf4j
public class MiniAccountServiceImpl extends ServiceImpl<MiniAccountMapper, MiniAccount> implements IMiniAccountService {

    @Autowired
    private SnowflakeProperty snowflakeProperty;
    @Autowired
    private IMiniAccountOauthsService miniAccountOauthsService;
    @Autowired
    private IMiniAccountExtendsService miniAccountExtendsService;
    @Autowired
    private IMiniAccountTagService miniAccountTagService;
    @Autowired
    private IMiniAccountTagGroupService miniAccountTagGroupService;
    @Autowired
    private IMiniAccountAddressService iMiniAccountAddressService;
    @Autowired
    private IMiniAccountRestrictService miniAccountRestrictService;
    @Autowired
    private IMemberLevelService memberLevelService;
    @Autowired
    private IMemberTypeService memberTypeService;
    @Autowired
    private IMemberLevelRelationService memberLevelRelationService;
    @Resource
    private RemoteMiniInfoService remoteMiniInfoService;
    @Autowired
    private RemoteGoodsService remoteGoodsService;
    @Autowired
    private RemoteShopsService remoteShopsService;
    @Autowired
    private IMiniAccountIntegralService miniAccountIntegralService;
    @Autowired
    private ISendCodeService sendCodeService;
    @Autowired
    private IMiniAccountCommissionService miniAccountCommissionService;
    @Autowired
    private IMiniAccountGoldenService miniAccountGoldenService;
    @Autowired
    private IMemberLevelRuleService memberLevelRuleService;
    @Autowired
    private IMemberLevelRuleMessageService memberLevelRuleMessageService;
    @Autowired
    private IMemberActiveSettingService memberActiveSettingService;
    @Autowired
    private IMemberActiveProductService memberActiveProductService;
    @Resource
    private Sender sender;
    @Autowired
    private RemoteOrderService remoteOrderService;
    @Autowired
    private RemoteMiniAccountService remoteMiniAccountService;
    @Autowired
    private IMiniAccountShopsService miniAccountShopsService;

    @Autowired
    private IMiniAccountPassTicketService miniAccountPassTicketService;
    @Autowired
    private IMiniAccountPackageGoodsService miniAccountPackageGoodsService;
    @Autowired
    private IMiniAccountPackageGoodsCodeService miniAccountPackageGoodsCodeService;
    @Autowired
    private IMiniAccountPackageOrderService miniAccountPackageOrderService;

    @Autowired
    private IMiniAccountAddressService miniAccountAddressService;
    /**
     * 封装小程序信息
     *
     * @return
     */
    public WxMaService getWxMaService() {
        WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
        MiniInfoVo miniInfoVo = remoteMiniInfoService.getShopConfigMini();
        config.setAppid(miniInfoVo.getAppId());
        config.setSecret(miniInfoVo.getAppSecret());
        config.setMsgDataFormat("JSON");
        WxMaService wxMaService = new WxMaServiceImpl();
        wxMaService.setWxMaConfig(config);
        return wxMaService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginBaseInfoVo login(String code, String inviteCode) {
        log.info("登录方法，传进来的邀请码：{}", inviteCode);
        LocalDateTime currentDateTime = LocalDateTime.now();
        final WxMaService wxService = getWxMaService();
        if (StrUtil.isBlank(code)) {
            throw new ServiceException("当前code不存在");
        }
        WxMaJscode2SessionResult session = null;
        try {
            session = wxService.getUserService().getSessionInfo(code);
            if (ObjectUtil.isEmpty(session)) {
                throw new ServiceException("login handler error");
            }
        } catch (WxErrorException e) {
            e.printStackTrace();
        }
        LoginDto login = new LoginDto();
        login.setOpenId(session.getOpenid());
        login.setUnionId(session.getUnionid());
        login.setSessionKey(session.getSessionKey());
        if(StrUtil.isNotBlank(inviteCode)){
            // 解密
            try {
                inviteCode = URLDecoder.decode(inviteCode, "UTF-8");
                log.info("登录方法，传进来的邀请码，解码后：{}", inviteCode);
                String decodeCode = AESUtil.decryptDataBase64MD5Key(inviteCode, null);
                log.info("登录方法，传进来的邀请码，解密后：{}", decodeCode);
                login.setInviteCode(decodeCode);
            } catch (Exception exception) {
                exception.printStackTrace();
            }
        }
        if (login == null || login.getOpenId() == null) {
            throw new ServiceException("登陆失败,调用错误", SystemCode.DATA_EXISTED.getCode());
        }
        String loginKey = RedisConstant.LOGIN_KEY.concat(":")
                .concat(OauthTypeEnum.WX_MINI.getType().toString()).concat(":").concat(login.getOpenId());
        AccountRedis accountRedis = new AccountRedis();
        //获取缓存中用户数据
        String loginBase = accountRedis.get(loginKey);
        MiniAccountOauthsDto miniAccountOauthsDto = null;
        MiniAccount account = null;
        if (StrUtil.isNotEmpty(loginBase)) {
            miniAccountOauthsDto = JSON.parseObject(loginBase, MiniAccountOauthsDto.class);
        } else {
            MiniAccountOauths miniAccountOauths = null;
            if (StrUtil.isNotBlank(login.getUnionId())) {
                miniAccountOauths = miniAccountOauthsService.getByUnionIdAndType(login.getUnionId(), OauthTypeEnum.WX_MINI);
            }
            //小程序和微信授权数据都不存在，则根据openId查询小程序用户数据
            if (miniAccountOauths == null) {
                miniAccountOauths = miniAccountOauthsService.getByOpenId(login.getOpenId(), OauthTypeEnum.WX_MINI.getType());
                //UNiconId不相同或不一致则更新
                if (StrUtil.isNotBlank(login.getUnionId()) && miniAccountOauths != null && !login.getUnionId().equals(miniAccountOauths.getUnionId())) {
                    miniAccountOauths.setUnionId(login.getUnionId());
                    miniAccountOauthsService.updateById(miniAccountOauths);

                }
            }
            //存在授权信息（可能是小程序授权信息也可能是公众号授权信息）
            if (miniAccountOauths != null) {
                account = this.getByUserId(miniAccountOauths.getUserId());
                //如果授权信息是公众号的则表示第一次进入小程序，生成小程序相关授权信息,并提供数据为小程序最新数据
                if (miniAccountOauths.getOauthType().equals(OauthTypeEnum.WX_MP.getType())) {
                    miniAccountOauths = new MiniAccountOauths();
                    miniAccountOauths.setOauthType(OauthTypeEnum.WX_MINI.getType());
                    miniAccountOauths.setUserId(account.getUserId());
                    miniAccountOauths.setOpenId(login.getOpenId());
                    miniAccountOauths.setUnionId(login.getUnionId());
                    miniAccountOauthsService.save(miniAccountOauths);

                }
                //缓存用户数据
                MiniAccountExtends accountExtends = miniAccountExtendsService.findByCurrentStatus(miniAccountOauths.getUserId());
                miniAccountOauthsDto = accuontOauthsCache(loginKey, miniAccountOauths, account, accountExtends);
            }
        }
        //新用户,创建新数据
        if (miniAccountOauthsDto == null) {
            miniAccountOauthsDto = initMiniInfo(currentDateTime, login, loginKey, false, OauthTypeEnum.WX_MINI, null);
        }else{
            // 非新用户，如果未认证，且邀请码非空，则更新邀请码
            if(!getUserWhetherAuthorization(miniAccountOauthsDto.getUserId()) && StrUtil.isNotBlank(login.getInviteCode()) && null != account){
                // 登录用户与分享邀请码的用户不是同一个人，才执行下面的逻辑
                if(!account.getUserId().equals(login.getInviteCode())){
                    account.setParentId(login.getInviteCode());
                    // 查询上上级会员
                    LambdaQueryWrapper<MiniAccount> aboveMiniAccountWrapper = new LambdaQueryWrapper<>();
                    aboveMiniAccountWrapper.select(MiniAccount::getParentId).eq(MiniAccount::getUserId, login.getInviteCode());
                    MiniAccount aboveAccount = this.getOne(aboveMiniAccountWrapper);
                    account.setAboveParentId(null == aboveAccount ? null : aboveAccount.getParentId());
                    this.update(new UpdateWrapper<MiniAccount>()
                            .set("parent_id", account.getParentId()).set("above_parent_id", account.getAboveParentId()).eq("id", account.getId()));
                }

            }
        }

        String userId = miniAccountOauthsDto.getUserId();
        MiniAccountExtends accountExtends = miniAccountExtendsService.findByCurrentStatus(userId);
        CompletableFuture.runAsync(() -> {
            //异步更新最后登陆时间
            miniAccountExtendsService.update(new UpdateWrapper<MiniAccountExtends>()
                    .set("last_login_time", currentDateTime).eq("shop_user_id", accountExtends.getShopUserId()));
        }).exceptionally(throwable -> {
            throwable.printStackTrace();
            return null;
        });

        //封装返回对象
        LoginBaseInfoVo vo = new LoginBaseInfoVo();
        Integer integer = applyShopByUserId(userId,accountExtends.getShopUserId());
        vo.setApplyShopFlag(integer);
        vo.setSessionKey(login.getSessionKey());
        vo.setToken(RedisConstant.ACCOUNT_KEY.concat(miniAccountOauthsDto.getToken()));
        vo.setWhetherAuthorization(getUserWhetherAuthorization(miniAccountOauthsDto.getUserId()));

        return vo;
    }


    public Integer applyShopByUserId(String userId,String shopUserId) {
        Integer flag = -1;
        //是否能申请
        Boolean  applyShopPartner  = memberLevelRuleService.checkApplyShopByUserId(userId);
        if (applyShopPartner!=null && applyShopPartner){
            //是否 已经申请
             /*   MiniAccountExtends accountExtends = miniAccountExtendsService.findByShopUserId(account.getUserId());
                String shopUserId = accountExtends.getShopUserId();*/
            Integer checked = remoteShopsService.checkByShopUserId(shopUserId);

            flag = checked;
        }
        return flag;
    }
    /**
     * 初始化新用户数据
     *
     * @param currentDateTime 当前时间
     * @param login           com.medusa.gruul.platform.api.model.dto.LoginDto
     * @param loginKey        ket
     * @return com.medusa.gruul.account.model.dto.MiniAccountOauthsDto
     */
    private MiniAccountOauthsDto initMiniInfo(LocalDateTime currentDateTime, LoginDto login, String loginKey, boolean authorizationFlag, OauthTypeEnum oauthType, AppRegisterDto appRegisterDto) {
        log.info("进入initMiniInfo".concat(login.toString()));
        log.debug("进入initMiniInfo".concat(login.toString()));
        MiniAccountOauthsDto miniAccountOauthsDto = null;
        Snowflake snowflake = IdUtil.createSnowflake(snowflakeProperty.getWorkerId(), snowflakeProperty.getDatacenterId());
        String userId = snowflake.nextId() + "";
        //生成会员卡号
        Snowflake newSnowflake = IdUtil.createSnowflake(snowflakeProperty.getWorkerId(), snowflakeProperty.getDatacenterId());
        String number = newSnowflake.nextId() + "";
        MiniAccount account = new MiniAccount();
        account.setUserId(userId);
        account.setFirstLoginTime(currentDateTime);
        account.setWhetherAuthorization(authorizationFlag);
        account.setCardNumber(number);
        account.setParentId(login.getInviteCode());
        if(null != appRegisterDto){
            account.setPhone(appRegisterDto.getPhone());
            String salt = RandomUtil.randomString(6);
            account.setSalt(salt);
            account.setPasswd(SecureUtil.md5(appRegisterDto.getPassword().concat(salt)));
            account.setNikeName(appRegisterDto.getNickname());
        }
        if(StrUtil.isNotBlank(login.getInviteCode())){
            // 查询上上级会员
            LambdaQueryWrapper<MiniAccount> aboveMiniAccountWrapper = new LambdaQueryWrapper<>();
            aboveMiniAccountWrapper.select(MiniAccount::getParentId).eq(MiniAccount::getUserId, login.getInviteCode());
            MiniAccount aboveAccount = this.getOne(aboveMiniAccountWrapper);
            if(null!= aboveAccount){
                account.setAboveParentId(aboveAccount.getParentId());
            }
        }


        //邀请码存在取邀请码对应的部门，职员，不存在取默认部门对应的部门，职员
        //获取主店铺
        ShopsPartner shopsPartner = remoteShopsService.getShopsPartnerMain();
        //获取主店铺特殊配置
        List<SpecialSetting> SpecialSettingList = remoteMiniInfoService.getSpecialSettingByShopId(shopsPartner.getShopId());

        SpecialSetting specialSetting = SpecialSettingList.get(0);

        Integer authRegisterBrowseFlag = specialSetting.getAuthRegisterBrowseFlag();
        if(authRegisterBrowseFlag == CommonConstants.NUMBER_ZERO){
            RelationInfoVo relationInfoVo = null;
            if(StringUtil.isNotEmpty(login.getInviteCode())){
                LambdaQueryWrapper<MiniAccount> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MiniAccount::getUserId,login.getInviteCode());
                MiniAccount miniAccount = this.getOne(queryWrapper);
                if(miniAccount!=null){
                    relationInfoVo = remoteMiniInfoService.getRelationInfoByMiniAccountId(miniAccount.getUserId());
                    if(StrUtil.isBlank(relationInfoVo.getEmployeeId()) && StrUtil.isNotBlank(miniAccount.getParentId())){
                        // 取上上级所属的职员、部门、门店
                        relationInfoVo = remoteMiniInfoService.getRelationInfoByMiniAccountId(miniAccount.getParentId());
                    }
                }else{
                    relationInfoVo = remoteMiniInfoService.getRelationInfoDefaultDepartment();
                }
            }else{
                relationInfoVo = remoteMiniInfoService.getRelationInfoDefaultDepartment();
            }
            account.setEmployeeId(relationInfoVo.getEmployeeId());
            account.setEmployeeOutId(relationInfoVo.getEmployeeOutId());
            account.setDepartmentId(relationInfoVo.getDepartmentId());
            account.setDepartmentCode(relationInfoVo.getDepartmentCode());
            account.setStoreFrontId(relationInfoVo.getStoreFrontId());
            account.setStoreFrontCode(relationInfoVo.getStoreFrontCode());
            account.setPlatformAccountId(relationInfoVo.getAccountId());
        }

        //查询默认会员等级，设置用户默认会员等级
//        LambdaQueryWrapper<MemberLevel> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(MemberLevel::getDefaultLevel, CommonConstants.NUMBER_ONE);
//        List<MemberLevel> memberLevelList = this.memberLevelService.list(wrapper);
//        if(CollectionUtil.isNotEmpty(memberLevelList)){
//            account.setMemberLevelId(memberLevelList.get(0).getId());
//        }

        //上级用户id是否存在
        if(StringUtils.isNotEmpty(account.getParentId())){
            LambdaQueryWrapper<MiniAccount>miniAccountWrapper = new LambdaQueryWrapper<>();
            miniAccountWrapper.eq(MiniAccount::getUserId,account.getParentId());
            miniAccountWrapper.eq(MiniAccount::getDeleted,CommonConstants.NUMBER_ZERO);
            miniAccountWrapper.eq(MiniAccount::getWhetherAuthorization,CommonConstants.NUMBER_ONE);
            MiniAccount parentMiniAccount = this.getOne(miniAccountWrapper);
            if(parentMiniAccount!=null){
                if(StringUtils.isNotEmpty(parentMiniAccount.getSaleUserId())){
                    account.setSaleUserId(parentMiniAccount.getSaleUserId());
                }else{
                    if(parentMiniAccount.getSaleFlag() == SalesFlagEnum.YES.getType()){
                        account.setSaleUserId(parentMiniAccount.getUserId());
                    }
                }
            }

        }
        this.getBaseMapper().insert(account);

        //新增会员等级关联数据
        //1.查询会员默认类型
        LambdaQueryWrapper<MemberType>memberTypeWrapper = new LambdaQueryWrapper<>();
        memberTypeWrapper.eq(MemberType::getDefaultType,DefaultTypeEnum.YES.getStatus());
        List<MemberType> memberTypeList = this.memberTypeService.list(memberTypeWrapper);

        // 直推会员 抽奖机会Message
        PrizeMemberMessage prizeMemberMessage = new PrizeMemberMessage();
        prizeMemberMessage.setTenantId(TenantContextHolder.getTenantId()).setShopId(shopsPartner.getShopId());
        if(CollectionUtil.isNotEmpty(memberTypeList)){
            String memberTypeId = memberTypeList.get(0).getId();
            //2.查询会员默认等级
            LambdaQueryWrapper<MemberLevel> memberLevelWrapper = new LambdaQueryWrapper<>();
            memberLevelWrapper.eq(MemberLevel::getMemberTypeId,memberTypeId);
            memberLevelWrapper.eq(MemberLevel::getDefaultLevel, CommonConstants.NUMBER_ONE);
            List<MemberLevel> memberLevelList = this.memberLevelService.list(memberLevelWrapper);
            if(CollectionUtil.isNotEmpty(memberLevelList)){
                MemberLevelRelation memberLevelRelation = new MemberLevelRelation();
                memberLevelRelation.setUserId(account.getUserId());
                memberLevelRelation.setMemberLevelId(memberLevelList.get(0).getId());
                memberLevelRelation.setMemberTypeId(Long.valueOf(memberTypeId));
                memberLevelRelation.setUpLevelTime(LocalDateTime.now());
                memberLevelRelationService.save(memberLevelRelation);

                prizeMemberMessage.setMemberLeverId(Long.valueOf(memberLevelRelation.getMemberLevelId()));
            }else{
                throw new ServiceException("平台尚未设置默认会员等级，保存失败！");
            }
        }else{
            throw new ServiceException("平台尚未设置默认会员等级，保存失败！");
        }
        //用户第三方表
        MiniAccountOauths miniAccountOauths = new MiniAccountOauths();
        miniAccountOauths.setOauthType(oauthType.getType());
        miniAccountOauths.setUserId(account.getUserId());
        miniAccountOauths.setOpenId(login.getOpenId());
        miniAccountOauths.setUnionId(login.getUnionId());
        miniAccountOauthsService.save(miniAccountOauths);
        //用户信息扩展
        MiniAccountExtends miniAccountExtends = new MiniAccountExtends();
        miniAccountExtends.setUserId(account.getUserId());
        miniAccountExtends.setIsBlacklist(0);
        miniAccountExtends.setConsumeNum(0);
        miniAccountExtends.setCardAuthorization(CardAuthorizationEnum.NO.getStatus());
        miniAccountExtends.setCurrentStatus(CommonConstants.NUMBER_ONE);
        miniAccountExtends.setConsumeTotleMoney(BigDecimal.valueOf(0.0));
        miniAccountExtends.setShopUserId(snowflake.nextId() + "");
        miniAccountExtends.setStatus(MiniAccountExtendsStatusEnum.YES.getStatus());
        miniAccountExtendsService.save(miniAccountExtends);
        if(StrUtil.isNotBlank(loginKey)){
            miniAccountOauthsDto = accuontOauthsCache(loginKey, miniAccountOauths, account, miniAccountExtends);
        }

        // 发送 赠送抽奖机会消息
        if (authorizationFlag &&prizeMemberMessage.getMemberLeverId()!=null && miniAccountExtends.getShopUserId() !=null ){
            prizeMemberMessage.setUserId(Long.valueOf(miniAccountExtends.getShopUserId()));
            sender.sendPrizeMemberUpMessage(prizeMemberMessage);
        }

        return miniAccountOauthsDto;
    }


    /**
     * 缓存登录授权信息
     *
     * @param loginKey       rediskey
     * @param accountOauths  认证基础数据
     * @param account        用户基础数据
     * @param accountExtends 用户扩展数据
     * @return com.medusa.gruul.account.model.dto.MiniAccountOauthsDto
     */
    private MiniAccountOauthsDto accuontOauthsCache(String loginKey, MiniAccountOauths accountOauths, MiniAccount account, MiniAccountExtends accountExtends) {
        MiniAccountOauthsDto miniAccountOauthsDto = null;
        AccountRedis accountRedis = new AccountRedis();
        String cacheUserInfo = accountRedis.get(loginKey);
        //判断用户是否token是否过期是否重新生成token信息
        if (StrUtil.isNotEmpty(cacheUserInfo)) {
            miniAccountOauthsDto = JSONObject.parseObject(cacheUserInfo, MiniAccountOauthsDto.class);
            //更新请求用户信息对象缓存
            curUserCache(miniAccountOauthsDto, account, accountExtends);
        } else {
            miniAccountOauthsDto = new MiniAccountOauthsDto();
            BeanUtils.copyProperties(accountOauths, miniAccountOauthsDto);
            String jwtToken = new JwtUtils("gruul-mini-account").createJwtToken("mini-account");
            miniAccountOauthsDto.setToken(jwtToken);
            accountRedis.setNxPx(loginKey, JSON.toJSONString(miniAccountOauthsDto), TimeConstants.ONE_HOUR * 2);
            //存入请求用户信息对象缓存
            curUserCache(miniAccountOauthsDto, account, accountExtends);
        }
        return miniAccountOauthsDto;
    }

    /**
     * 获取用户是否授权过
     *
     * @param userId 用户id
     * @return 授权过  trun  未授权 false
     */
    private Boolean getUserWhetherAuthorization(String userId) {
        MiniAccount account = this.getByUserId(userId);
        return account.getWhetherAuthorization();
    }


    /**
     * 请求用户信息对象缓存
     *
     * @param miniAccountOauthsDto com.medusa.gruul.account.model.dto.MiniAccountOauthsDto
     * @param account              用户基础数据
     * @param accountExtends       用户扩展数据
     */
    private void curUserCache(MiniAccountOauthsDto miniAccountOauthsDto, MiniAccount account, MiniAccountExtends accountExtends) {
        AccountRedis allRedis = new AccountRedis(CommonConstants.MINI_ACCOUNT_REDIS_KEY);
        CurMiniUserInfoDto miniUserInfoDto = new CurMiniUserInfoDto();
        miniUserInfoDto.setUserId(account.getUserId());
        miniUserInfoDto.setNikeName(account.getNikeName());
        miniUserInfoDto.setAvatarUrl(account.getAvatarUrl());
        miniUserInfoDto.setGender(account.getGender());
        miniUserInfoDto.setPhone(account.getPhone());
        miniUserInfoDto.setShopUserId(accountExtends.getShopUserId());
        miniUserInfoDto.setOpenId(miniAccountOauthsDto.getOpenId());
        miniUserInfoDto.setSelfInviteCode(account.getSelfInviteCode());
        CurUserDto curUserDto = new CurUserDto();

//        1-微信小程序,2-公众号
        if (miniAccountOauthsDto.getOauthType().equals(CommonConstants.NUMBER_ONE)) {
            curUserDto.setUserType(CommonConstants.NUMBER_ZERO);
            miniUserInfoDto.setTerminalType(LoginTerminalEnum.MINI);
        }
        if (miniAccountOauthsDto.getOauthType().equals(CommonConstants.NUMBER_TWO)) {
            curUserDto.setUserType(CommonConstants.NUMBER_TWO);
            miniUserInfoDto.setTerminalType(LoginTerminalEnum.H5);
        }
        allRedis.del(miniAccountOauthsDto.getToken());
        allRedis.setNxPx(miniAccountOauthsDto.getToken(), JSON.toJSONString(miniUserInfoDto), TimeConstants.SIX_HOURS * 2);
        // curUserDto 缓存
        curUserDto.setNikeName(account.getNikeName());
        curUserDto.setAvatarUrl(account.getAvatarUrl());
        curUserDto.setGender(account.getGender());
        curUserDto.setUserId(accountExtends.getShopUserId());
        curUserDto.setShopType(0);
        curUserDto.setVersion("");
        curUserDto.setOpenId(miniAccountOauthsDto.getOpenId());
        curUserDto.setPhone(account.getPhone());
        curUserDto.setSelfInviteCode(account.getSelfInviteCode());
        AccountRedis accountRedis = new AccountRedis();
        accountRedis.del(miniAccountOauthsDto.getToken());
        accountRedis.setNxPx(miniAccountOauthsDto.getToken(), JSON.toJSONString(curUserDto), TimeConstants.SIX_HOURS * 2);
    }


    @Override
    public MiniAccount getByUserId(String userId) {
        String key = RedisConstant.ACCOUNT_DB_KEY.concat(userId);
        AccountRedis accountRedis = new AccountRedis();
        String accountJson = accountRedis.get(key);
        MiniAccount account = null;
        if (StrUtil.isNotEmpty(accountJson)) {
            account = JSONObject.parseObject(accountJson, MiniAccount.class);
        } else {
            account = this.baseMapper.selectOne(new QueryWrapper<MiniAccount>().eq("user_id", userId));
            accountCache(account);
        }
        return account;
    }


    @Override
    public String decodePhoneInfo(DecodePhoneInfo decodePhoneInfo) {
        String json = WxMaCryptUtils.decrypt(decodePhoneInfo.getSessionKey(), decodePhoneInfo.getEncryptedData(), decodePhoneInfo.getIvStr());
        JSONObject jsonObject = JSON.parseObject(json);
        String phoneNumber = jsonObject.getString("purePhoneNumber");
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        MiniAccount miniAccount = this.getByShopUserId(curUser.getUserId());
        if (miniAccount == null) {
            throw new ServiceException("数据错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //判断用户手机之前是否存在
        LambdaQueryWrapper<MiniAccount>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccount::getPhone, phoneNumber).ne(MiniAccount::getId, miniAccount.getId());
        List<MiniAccount> oldMiniAccountList = this.baseMapper.selectList(wrapper);
        if(CollectionUtil.isNotEmpty(oldMiniAccountList)){
            throw new ServiceException("手机号码已存在！", SystemCode.DATA_NOT_EXIST_CODE);
        }

        //通过手机号查询用户，如果手机号对应的用户已经存在，没有认证，且与当前用户不是同一条记录（目前出现这种现象是该记录是由外部系统推送过来的），将该手机号对应的用户
        //的信息覆盖当前用户
        LambdaQueryWrapper<MiniAccount> phoneWrapper = new LambdaQueryWrapper<>();
        phoneWrapper.eq(MiniAccount::getPhone, phoneNumber).ne(MiniAccount::getId, miniAccount.getId())
                .eq(MiniAccount::getWhetherAuthorization, Boolean.FALSE);
        List<MiniAccount> miniAccountList = this.baseMapper.selectList(phoneWrapper);
        if(CollectionUtil.isNotEmpty(miniAccountList)){
            MiniAccount phoneAccount = miniAccountList.get(0);
            miniAccount.setMemberLevelId(phoneAccount.getMemberLevelId());
            miniAccount.setCardNumber(phoneAccount.getCardNumber());
            miniAccount.setGender(phoneAccount.getGender());
            miniAccount.setSendStatus(phoneAccount.getSendStatus());
            miniAccount.setOutApiId(phoneAccount.getOutApiId());
            //将数据删掉
            List<Long> dbIdList = miniAccountList.stream().map(MiniAccount::getId).collect(Collectors.toList());
            this.removeByIds(dbIdList);
        }

        miniAccount.setPhone(phoneNumber);
        //设置为未发送状态
        miniAccount.setSendStatus(ExternalAccountEnum.NOT_ISSUED.getStatus());
        this.updateById(miniAccount);
        accountCache(miniAccount);
        return phoneNumber;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserBaseInfo(UpdateUserBaseInfoDto updateUserBaseInfoDto) {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        MiniAccount miniAccount = this.getByShopUserId(curUser.getUserId());
        if (miniAccount == null) {
            throw new ServiceException("数据错误！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //获取主店铺
        ShopsPartner shopsPartner = remoteShopsService.getShopsPartnerMain();
        //获取主店铺特殊配置
        List<SpecialSetting> SpecialSettingList = remoteMiniInfoService.getSpecialSettingByShopId(shopsPartner.getShopId());

        SpecialSetting specialSetting = SpecialSettingList.get(0);

        Integer authRegisterBrowseFlag = specialSetting.getAuthRegisterBrowseFlag();



        if(authRegisterBrowseFlag == CommonConstants.NUMBER_ONE&&!miniAccount.getWhetherAuthorization()){
            AccountRedis accountRedis = new AccountRedis();
            String redisKey = RedisConstant.PHONE_KEY.concat(updateUserBaseInfoDto.getType().toString()).concat(":").concat(updateUserBaseInfoDto.getPhone());
            //判断姓名是否为空
            if(StringUtils.isEmpty(updateUserBaseInfoDto.getNickName())){
                throw new ServiceException("姓名不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
            }
            //判断手机号必须是11位类型的手机号
            if (updateUserBaseInfoDto.getPhone() == null || !ReUtil.isMatch(RegexConstants.REGEX_MOBILE_EXACT, updateUserBaseInfoDto.getPhone())) {
                throw new ServiceException("手机号长度必须是11位类型的手机号！", SystemCode.DATA_NOT_EXIST.getCode());
            }
            //判断用户手机唯一性
            LambdaQueryWrapper<MiniAccount>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MiniAccount::getPhone, updateUserBaseInfoDto.getPhone()).ne(MiniAccount::getId, miniAccount.getId());
            List<MiniAccount> oldMiniAccountList = this.baseMapper.selectList(wrapper);
            if(CollectionUtil.isNotEmpty(oldMiniAccountList)){
                throw new ServiceException("手机号已存在，请输入其他手机号码！", SystemCode.DATA_NOT_EXIST_CODE);
            }
            //判断验证码是否存在
            if(StringUtils.isEmpty(updateUserBaseInfoDto.getSmsCode())){
                throw new ServiceException("手机验证码不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
            }
            //验证验证码正确性
            String code = accountRedis.get(redisKey);

            if(code == null || !code.equals(updateUserBaseInfoDto.getSmsCode())){
                throw new ServiceException("验证码不正确，请重新输入！", SystemCode.DATA_NOT_EXIST_CODE);
            }

            //验证邀请码是否存在
            if(StringUtils.isEmpty(updateUserBaseInfoDto.getInviterInviteCode())){
                throw new ServiceException("邀请码不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
            }
            String inviterInviteCode = updateUserBaseInfoDto.getInviterInviteCode();
            //判断是否是业务员邀请码
            Result<ShopInfoDto> shopInfo = remoteMiniInfoService.getShopInfo();
            String salesmanInviteCode = "";
            if (shopInfo.getCode() == SystemCode.SUCCESS_CODE) {
                salesmanInviteCode = shopInfo.getData().getSalesmanInviteCode();
            }
            //如果不是业务员邀请码，判断是否是会员邀请码
            if(StringUtils.isEmpty(salesmanInviteCode)||!salesmanInviteCode.equals(inviterInviteCode)){
                LambdaQueryWrapper<MiniAccount>miniAccountWrapper = new LambdaQueryWrapper<>();
                miniAccountWrapper.eq(MiniAccount::getSelfInviteCode,inviterInviteCode);
                List<MiniAccount> list = this.list(miniAccountWrapper);
                if(list!=null&&list.size()>0){
                    if(list.size() == 1){
                        //邀请的用户
                        MiniAccount inviteMiniAccount = list.get(0);
                        miniAccount.setParentId(inviteMiniAccount.getUserId());
                        //判断邀请的用户是否存在上级用户
                        if(StringUtils.isNotEmpty(inviteMiniAccount.getParentId())){
                            miniAccount.setAboveParentId(inviteMiniAccount.getParentId());
                        }
                    }else{
                        throw new ServiceException("邀请码重复了，请立即联系服务商处理！", SystemCode.DATA_NOT_EXIST_CODE);
                    }
                }else{
                    throw new ServiceException("邀请码不存在，请重新输入！", SystemCode.DATA_NOT_EXIST_CODE);
                }
            }

            //生成自身邀请码
            String selfInviteCode = "";
            int count = 0;
            while (count<10){
                String newSelfInviteCode = RandomUtil.randomNumbers(CommonConstants.NUMBER_SEVEN);
                LambdaQueryWrapper<MiniAccount>queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MiniAccount::getSelfInviteCode,newSelfInviteCode);
                int SelfInviteCodeCount = this.count(queryWrapper);
                if(SelfInviteCodeCount == 0){
                    selfInviteCode = newSelfInviteCode;
                    break;
                }
                count++;
            }
            if(StringUtils.isEmpty(selfInviteCode)){
                throw new ServiceException("生成自身邀请码失败，请重新注册！", SystemCode.DATA_NOT_EXIST_CODE);
            }
            miniAccount.setSelfInviteCode(selfInviteCode);
            miniAccount.setInviterInviteCode(inviterInviteCode);
            miniAccount.setRegisterId(miniAccount.getUserId());

            accountRedis.del(redisKey);
        }
        BeanUtil.copyProperties(updateUserBaseInfoDto, miniAccount, CopyOptions.create().ignoreNullValue());
        if (StrUtil.isEmpty(updateUserBaseInfoDto.getNikeName())) {
            miniAccount.setNikeName(updateUserBaseInfoDto.getNickName());
        }
        //用户授权成功添加积分
        if(!miniAccount.getWhetherAuthorization()){
            miniAccountIntegralService.loginAddIntegral(miniAccount);
        }
        // 设置为已认证状态
        if(miniAccount.getRegisterTime() == null){
            miniAccount.setRegisterTime(LocalDateTime.now());
        }
        miniAccount.setWhetherAuthorization(Boolean.TRUE);
        this.updateById(miniAccount);
        if(StrUtil.isNotBlank(miniAccount.getParentId())){
            // 根据上级会员查询关联的平台用户，得到平台用户所属的商家，将会员与商家关联起来

            LambdaQueryWrapper<MiniAccount> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MiniAccount::getUserId, miniAccount.getParentId());
            MiniAccount parentMiniAccount = this.getOne(queryWrapper);
            if(parentMiniAccount!=null){
                RelationInfoVo relationInfoVo = remoteMiniInfoService.getRelationInfoByMiniAccountId(parentMiniAccount.getUserId());
                if(StrUtil.isNotBlank(relationInfoVo.getAccountId())){
                    String shopId = "";
                    // 查询记录是否存在，不存在重新插入
                    String oldShopId = ShopContextHolder.getShopId();
                    ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
                    LambdaQueryWrapper<MiniAccountShops> queryWrapper1 = new LambdaQueryWrapper<>();
                    queryWrapper1.eq(MiniAccountShops::getUserId, miniAccount.getUserId())
                            .eq(MiniAccountShops::getShopId, shopId);
                    // 查询数量
                    long count = miniAccountShopsService.count(queryWrapper1);
                    if(count == 0){
                        MiniAccountShops  miniAccountShops = new MiniAccountShops();
                        miniAccountShops.setUserId(miniAccount.getUserId());
                        miniAccountShops.setShopId(shopId);
                        miniAccountShopsService.save(miniAccountShops);
                    }
                    ShopContextHolder.setShopId(oldShopId);
                }
            }

        }

        // 判断权益包分享入参
        if(StrUtil.isNotBlank(updateUserBaseInfoDto.getPackageCodeShareParam())){
            try {
                log.error("权益包分享入参：{}", updateUserBaseInfoDto.getPackageCodeShareParam());
                JSONObject paramJson = JSONObject.parseObject(updateUserBaseInfoDto.getPackageCodeShareParam());
                if(paramJson != null && !paramJson.isEmpty()){
                    Long miniAccountPackageGoodsId = paramJson.getLong("miniAccountPackageGoodsId");
                    Long miniAccountPackageGoodsCodeId = paramJson.getLong("miniAccountPackageGoodsCodeId");
                    if(miniAccountPackageGoodsId != null && miniAccountPackageGoodsCodeId != null){
                        MiniAccountPackageGoods miniAccountPackageGoods = miniAccountPackageGoodsService.getById(miniAccountPackageGoodsId);
                        MiniAccountPackageGoodsCode miniAccountPackageGoodsCode = miniAccountPackageGoodsCodeService.getById(miniAccountPackageGoodsCodeId);
                        if(miniAccountPackageGoods != null && miniAccountPackageGoodsCode != null
                                && miniAccountPackageGoodsCode.getMiniAccountPackageGoodsId().longValue() == miniAccountPackageGoods.getId().longValue()){
                            //会员权益包商品记录与益包商品验证码记录能对应上
                            MiniAccountPackageOrder miniAccountPackageOrder = miniAccountPackageOrderService.getById(miniAccountPackageGoods.getMainId());
                            // 如果已经领取过，点击进来则不再领取
                            LambdaQueryWrapper<MiniAccountPackageGoods> queryWrapper = new LambdaQueryWrapper<>();
                            queryWrapper.eq(MiniAccountPackageGoods::getSourceMiniAccountPackageGoodsId, miniAccountPackageGoods.getId())
                                    .eq(MiniAccountPackageGoods::getUserId, curUser.getUserId());
                            long count = miniAccountPackageGoodsService.count(queryWrapper);
                            // 判断如果是购买权益包本人点击分享链接进来，则不作处理
                            if(!curUser.getUserId().equals(miniAccountPackageOrder.getUserId().toString()) && count == 0){
                                MiniAccountPackageGoods miniAccountPackageGoodsSelf = new MiniAccountPackageGoods();
                                miniAccountPackageGoodsSelf.setShopId(miniAccountPackageGoods.getShopId());
                                miniAccountPackageGoodsSelf.setMainId(miniAccountPackageGoods.getMainId());
                                miniAccountPackageGoodsSelf.setProductId(miniAccountPackageGoods.getProductId());
                                miniAccountPackageGoodsSelf.setProductName(miniAccountPackageGoods.getProductName());
                                miniAccountPackageGoodsSelf.setSkuId(miniAccountPackageGoods.getSkuId());
                                miniAccountPackageGoodsSelf.setSkuName(miniAccountPackageGoods.getSkuName());
                                miniAccountPackageGoodsSelf.setAllTimes(1);
                                miniAccountPackageGoodsSelf.setAlreadyTimes(0);
                                miniAccountPackageGoodsSelf.setStatus(PackageStatusEnum.UN_USE.getStatus());
                                miniAccountPackageGoodsSelf.setOldStatus(PackageStatusEnum.UN_USE.getStatus());
                                miniAccountPackageGoodsSelf.setStartTime(miniAccountPackageGoods.getStartTime());
                                miniAccountPackageGoodsSelf.setEndTime(miniAccountPackageGoods.getEndTime());
                                // 不设置互斥id，否则核销时会将源权益包记录的已使用次数置为购买次数
                                //miniAccountPackageGoodsSelf.setMutexGoodId(miniAccountPackageGoods.getMutexGoodId());
                                //miniAccountPackageGoodsSelf.setMutexFlag(miniAccountPackageGoods.getMutexFlag());
                                miniAccountPackageGoodsSelf.setPrice(miniAccountPackageGoods.getPrice());
                                miniAccountPackageGoodsSelf.setOriginalPrice(miniAccountPackageGoods.getOriginalPrice());
                                miniAccountPackageGoodsSelf.setAmount(miniAccountPackageGoods.getAmount());
                                miniAccountPackageGoodsSelf.setNotTerm(miniAccountPackageGoods.getNotTerm());
                                miniAccountPackageGoodsSelf.setNotTime(miniAccountPackageGoods.getNotTime());
                                miniAccountPackageGoodsSelf.setPackageId(miniAccountPackageGoods.getPackageId());
                                miniAccountPackageGoodsSelf.setCostPrice(miniAccountPackageGoods.getCostPrice());
                                miniAccountPackageGoodsSelf.setAdjustmentPrice(miniAccountPackageGoods.getAdjustmentPrice());
                                miniAccountPackageGoodsSelf.setOrderId(miniAccountPackageGoods.getOrderId());
                                miniAccountPackageGoodsSelf.setDaysRate(miniAccountPackageGoods.getDaysRate());
                                miniAccountPackageGoodsSelf.setSourceMiniAccountPackageGoodsId(miniAccountPackageGoods.getId());
                                miniAccountPackageGoodsSelf.setSourceUserId(miniAccountPackageOrder.getUserId());
                                miniAccountPackageGoodsSelf.setUserId(curUser.getUserId());
                                this.miniAccountPackageGoodsService.save(miniAccountPackageGoodsSelf);
                            }

                        }
                    }
                }
            } catch (Exception e) {
                log.error("权益包分享解析错误", e);
            }
        }

        //更新用户登录信息
        miniAccount = accountLoginCache(miniAccount);
        //缓存用户最新的基础信息
        accountCache(miniAccount);


        //发送升级会员等级消息
        UpgradeMemberLevelMessage upgradeMemberLevelMessage = new UpgradeMemberLevelMessage();
        upgradeMemberLevelMessage.setTenantId(TenantContextHolder.getTenantId());
        upgradeMemberLevelMessage.setMessageType(UpgradeTypeEnum.MEMBERREGISTER.getType());
        upgradeMemberLevelMessage.setUserId(Long.valueOf(miniAccount.getUserId()));
        sender.sendUpgradeMemberLevelMessage(upgradeMemberLevelMessage);

    }


    /**
     * 代注册，帮别人注册
     * @param updateUserBaseInfoDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void helpOtherRegister(UpdateUserBaseInfoDto updateUserBaseInfoDto) {

        //代注册人信息
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        MiniAccount miniAccount = this.getByShopUserId(curUser.getUserId());
        if (miniAccount == null) {
            throw new ServiceException("数据错误！", SystemCode.DATA_NOT_EXIST_CODE);
        }

        //判断姓名是否为空
        if(StringUtils.isEmpty(updateUserBaseInfoDto.getNickName())){
            throw new ServiceException("姓名不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //判断手机号必须是11位类型的手机号
        if (updateUserBaseInfoDto.getPhone() == null || !ReUtil.isMatch(RegexConstants.REGEX_MOBILE_EXACT, updateUserBaseInfoDto.getPhone())) {
            throw new ServiceException("手机号长度必须是11位类型的手机号！", SystemCode.DATA_NOT_EXIST.getCode());
        }
        //判断用户手机唯一性
        LambdaQueryWrapper<MiniAccount>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccount::getPhone, updateUserBaseInfoDto.getPhone()).ne(MiniAccount::getId, miniAccount.getId());
        List<MiniAccount> oldMiniAccountList = this.baseMapper.selectList(wrapper);
        if(CollectionUtil.isNotEmpty(oldMiniAccountList)){
            throw new ServiceException("手机号已存在，请输入其他手机号码！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //判断验证码是否存在
        if(StringUtils.isEmpty(updateUserBaseInfoDto.getSmsCode())){
            throw new ServiceException("手机验证码不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //验证验证码正确性
        AccountRedis accountRedis = new AccountRedis();
        String redisKey = RedisConstant.PHONE_KEY.concat(updateUserBaseInfoDto.getType().toString()).concat(":").concat(updateUserBaseInfoDto.getPhone());
        String code = accountRedis.get(redisKey);

        if(code == null || !code.equals(updateUserBaseInfoDto.getSmsCode())){
            throw new ServiceException("验证码不正确，请重新输入！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //验证邀请码是否存在
        if(StringUtils.isEmpty(updateUserBaseInfoDto.getInviterInviteCode())){
            throw new ServiceException("邀请码不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        String inviterInviteCode = updateUserBaseInfoDto.getInviterInviteCode();
        //判断是否是业务员邀请码
        Result<ShopInfoDto> shopInfo = remoteMiniInfoService.getShopInfo();
        String salesmanInviteCode = "";
        if (shopInfo.getCode() == SystemCode.SUCCESS_CODE) {
            salesmanInviteCode = shopInfo.getData().getSalesmanInviteCode();
        }

        MiniAccount account = new MiniAccount();
        BeanUtil.copyProperties(updateUserBaseInfoDto, account, CopyOptions.create().ignoreNullValue());
        if (StrUtil.isEmpty(updateUserBaseInfoDto.getNikeName())) {
            account.setNikeName(updateUserBaseInfoDto.getNickName());
        }
        //如果不是业务员邀请码，判断是否是会员邀请码
        if(StringUtils.isEmpty(salesmanInviteCode)||!salesmanInviteCode.equals(inviterInviteCode)){
            LambdaQueryWrapper<MiniAccount>miniAccountWrapper = new LambdaQueryWrapper<>();
            miniAccountWrapper.eq(MiniAccount::getSelfInviteCode,inviterInviteCode);
            List<MiniAccount> list = this.list(miniAccountWrapper);
            if(list!=null&&list.size()>0){
                if(list.size() == 1){
                    //邀请的用户
                    MiniAccount inviteMiniAccount = list.get(0);
                    account.setParentId(inviteMiniAccount.getUserId());
                    //判断邀请的用户是否存在上级用户
                    if(StringUtils.isNotEmpty(inviteMiniAccount.getParentId())){
                        account.setAboveParentId(inviteMiniAccount.getParentId());
                    }
                }else{
                    throw new ServiceException("邀请码重复了，请立即联系服务商处理！", SystemCode.DATA_NOT_EXIST_CODE);
                }
            }else{
                throw new ServiceException("邀请码不存在，请重新输入！", SystemCode.DATA_NOT_EXIST_CODE);
            }
        }

        //生成自身邀请码
        String selfInviteCode = "";
        int count = 0;
        while (count<10){
            String newSelfInviteCode = RandomUtil.randomNumbers(CommonConstants.NUMBER_SEVEN);
            LambdaQueryWrapper<MiniAccount>queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MiniAccount::getSelfInviteCode,newSelfInviteCode);
            int SelfInviteCodeCount = this.count(queryWrapper);
            if(SelfInviteCodeCount == 0){
                selfInviteCode = newSelfInviteCode;
                break;
            }
            count++;
        }
        if(StringUtils.isEmpty(selfInviteCode)){
            throw new ServiceException("生成自身邀请码失败，请重新注册！", SystemCode.DATA_NOT_EXIST_CODE);
        }

        //1.插入用户信息表
        LocalDateTime currentDateTime = LocalDateTime.now();
        Snowflake snowflake = IdUtil.createSnowflake(snowflakeProperty.getWorkerId(), snowflakeProperty.getDatacenterId());
        String userId = snowflake.nextId() + "";
        //生成会员卡号
        Snowflake newSnowflake = IdUtil.createSnowflake(snowflakeProperty.getWorkerId(), snowflakeProperty.getDatacenterId());
        String number = newSnowflake.nextId() + "";


        account.setUserId(userId);
        account.setFirstLoginTime(currentDateTime);
        account.setWhetherAuthorization(false);
        account.setCardNumber(number);
        account.setSelfInviteCode(selfInviteCode);

        account.setSelfInviteCode(selfInviteCode);
        account.setInviterInviteCode(inviterInviteCode);
        account.setRegisterId(miniAccount.getUserId());
        account.setRegisterTime(LocalDateTime.now());

        //上级用户存在取邀请码对应的部门，职员，不存在取默认部门对应的部门，职员
        RelationInfoVo relationInfoVo = null;
        if(StringUtils.isNotEmpty(account.getParentId())){
            LambdaQueryWrapper<MiniAccount> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MiniAccount::getUserId,account.getParentId());
            MiniAccount parentMiniAccount = this.getOne(queryWrapper);
            if(parentMiniAccount!=null){
                relationInfoVo = remoteMiniInfoService.getRelationInfoByMiniAccountId(parentMiniAccount.getUserId());
                if(StrUtil.isBlank(relationInfoVo.getEmployeeId()) && StrUtil.isNotBlank(parentMiniAccount.getParentId())){
                    // 取上上级所属的职员、部门、门店
                    relationInfoVo = remoteMiniInfoService.getRelationInfoByMiniAccountId(parentMiniAccount.getParentId());
                }
            }else{
                relationInfoVo = remoteMiniInfoService.getRelationInfoDefaultDepartment();
            }
        }else{
            relationInfoVo = remoteMiniInfoService.getRelationInfoDefaultDepartment();
        }
        account.setEmployeeId(relationInfoVo.getEmployeeId());
        account.setEmployeeOutId(relationInfoVo.getEmployeeOutId());
        account.setDepartmentId(relationInfoVo.getDepartmentId());
        account.setDepartmentCode(relationInfoVo.getDepartmentCode());
        account.setStoreFrontId(relationInfoVo.getStoreFrontId());
        account.setStoreFrontCode(relationInfoVo.getStoreFrontCode());
        account.setPlatformAccountId(relationInfoVo.getAccountId());
        //上级用户id是否存在
        if(StringUtils.isNotEmpty(account.getParentId())){
            LambdaQueryWrapper<MiniAccount>miniAccountWrapper = new LambdaQueryWrapper<>();
            miniAccountWrapper.eq(MiniAccount::getUserId,account.getParentId());
            miniAccountWrapper.eq(MiniAccount::getDeleted,CommonConstants.NUMBER_ZERO);
            miniAccountWrapper.eq(MiniAccount::getWhetherAuthorization,CommonConstants.NUMBER_ONE);
            MiniAccount parentMiniAccount = this.getOne(miniAccountWrapper);
            if(parentMiniAccount!=null){
                if(StringUtils.isNotEmpty(parentMiniAccount.getSaleUserId())){
                    account.setSaleUserId(parentMiniAccount.getSaleUserId());
                }else{
                    if(parentMiniAccount.getSaleFlag() == SalesFlagEnum.YES.getType()){
                        account.setSaleUserId(parentMiniAccount.getUserId());
                    }
                }
            }

        }
        this.getBaseMapper().insert(account);

        //新增会员等级关联数据
        //1.查询会员默认类型
        LambdaQueryWrapper<MemberType>memberTypeWrapper = new LambdaQueryWrapper<>();
        memberTypeWrapper.eq(MemberType::getDefaultType,DefaultTypeEnum.YES.getStatus());
        List<MemberType> memberTypeList = this.memberTypeService.list(memberTypeWrapper);
        if(CollectionUtil.isNotEmpty(memberTypeList)){
            String memberTypeId = memberTypeList.get(0).getId();
            //2.查询会员默认等级
            LambdaQueryWrapper<MemberLevel> memberLevelWrapper = new LambdaQueryWrapper<>();
            memberLevelWrapper.eq(MemberLevel::getMemberTypeId,memberTypeId);
            memberLevelWrapper.eq(MemberLevel::getDefaultLevel, CommonConstants.NUMBER_ONE);
            List<MemberLevel> memberLevelList = this.memberLevelService.list(memberLevelWrapper);
            if(CollectionUtil.isNotEmpty(memberLevelList)){
                MemberLevelRelation memberLevelRelation = new MemberLevelRelation();
                memberLevelRelation.setUserId(account.getUserId());
                memberLevelRelation.setMemberLevelId(memberLevelList.get(0).getId());
                memberLevelRelation.setMemberTypeId(Long.valueOf(memberTypeId));
                memberLevelRelation.setUpLevelTime(LocalDateTime.now());
                memberLevelRelationService.save(memberLevelRelation);
            }else{
                throw new ServiceException("平台尚未设置默认会员等级，保存失败！");
            }
        }else{
            throw new ServiceException("平台尚未设置默认会员等级，保存失败！");
        }
        //用户信息扩展
        MiniAccountExtends miniAccountExtends = new MiniAccountExtends();
        miniAccountExtends.setUserId(account.getUserId());
        miniAccountExtends.setIsBlacklist(0);
        miniAccountExtends.setConsumeNum(0);
        miniAccountExtends.setCardAuthorization(CardAuthorizationEnum.NO.getStatus());
        miniAccountExtends.setCurrentStatus(CommonConstants.NUMBER_ONE);
        miniAccountExtends.setConsumeTotleMoney(BigDecimal.valueOf(0.0));
        miniAccountExtends.setShopUserId(snowflake.nextId() + "");
        miniAccountExtends.setStatus(MiniAccountExtendsStatusEnum.YES.getStatus());
        miniAccountExtendsService.save(miniAccountExtends);
        accountRedis.del(redisKey);

    }

    /**
     * 用户认证登录
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void userLoginAndAuth(UserLoginDto dto) {

        //当前登录小程序用户
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        MiniAccount miniAccount = this.getByShopUserId(curUser.getUserId());
        if (miniAccount == null) {
            throw new ServiceException("数据错误！", SystemCode.DATA_NOT_EXIST_CODE);
        }

        //判断手机号必须是11位类型的手机号
        if (dto.getPhone() == null || !ReUtil.isMatch(RegexConstants.REGEX_MOBILE_EXACT, dto.getPhone())) {
            throw new ServiceException("手机号长度必须是11位类型的手机号！", SystemCode.DATA_NOT_EXIST.getCode());
        }
        //判断验证码是否存在
        if(StringUtils.isEmpty(dto.getSmsCode())){
            throw new ServiceException("手机验证码不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //验证验证码正确性
        AccountRedis accountRedis = new AccountRedis();
        String redisKey = RedisConstant.PHONE_KEY.concat(dto.getType().toString()).concat(":").concat(dto.getPhone());
        String code = accountRedis.get(redisKey);

        if(code == null || !code.equals(dto.getSmsCode())){
            throw new ServiceException("验证码不正确，请重新输入！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        accountRedis.del(redisKey);


        //判断用户手机唯一性
        LambdaQueryWrapper<MiniAccount>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccount::getPhone, dto.getPhone());
        List<MiniAccount> list = this.list(wrapper);
        if(list!=null&&list.size()>0){
            if(list.size()>1){
                throw new ServiceException("手机号重复了，请立即联系服务商处理！", SystemCode.DATA_NOT_EXIST_CODE);
            }
        }else{
            throw new ServiceException("该手机号尚未注册，请先注册！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //代注册用户
        MiniAccount account = list.get(0);
        if(account.getWhetherAuthorization()){
            throw new ServiceException("该账号已认证，无法更换登录", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //当前用户小程序信息记录
        MiniAccountOauths miniAccountOauths = miniAccountOauthsService.getByUserId(OauthTypeEnum.WX_MINI.getType(), miniAccount.getUserId());
        //当前用户扩展表记录
        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(miniAccount.getUserId());



        account.setWhetherAuthorization(Boolean.TRUE);
        this.updateById(account);
        //更新当前用户小程序信息记录表userId
        miniAccountOauths.setUserId(account.getUserId());
        miniAccountOauthsService.updateById(miniAccountOauths);
        //删除当前用户记录，用户扩展记录
        this.removeById(miniAccount.getId());
        this.miniAccountExtendsService.removeById(miniAccountExtends.getId());

        //删除当前用户redis
        String key = RedisConstant.ACCOUNT_DB_KEY.concat(miniAccount.getUserId());
        accountRedis.del(key);

        String loginKey = RedisConstant.LOGIN_KEY.concat(":")
                .concat(OauthTypeEnum.WX_MINI.getType().toString()).concat(":").concat(miniAccountOauths.getOpenId());
        String cacheUserInfo = accountRedis.get(loginKey);
        MiniAccountOauthsDto miniAccountOauthsDto = JSONObject.parseObject(cacheUserInfo, MiniAccountOauthsDto.class);


        miniAccountOauthsDto.setUserId(miniAccountOauths.getUserId());
        accountRedis.del(loginKey);
        accountRedis.setNxPx(loginKey, JSON.toJSONString(miniAccountOauthsDto), TimeConstants.ONE_HOUR * 2);
        //更新用户登录信息
        account = accountLoginCache(account);
        //缓存用户最新的基础信息
        accountCache(account);

        //发送升级会员等级消息
        UpgradeMemberLevelMessage upgradeMemberLevelMessage = new UpgradeMemberLevelMessage();
        upgradeMemberLevelMessage.setTenantId(TenantContextHolder.getTenantId());
        upgradeMemberLevelMessage.setMessageType(UpgradeTypeEnum.MEMBERREGISTER.getType());
        upgradeMemberLevelMessage.setUserId(Long.valueOf(account.getUserId()));
        sender.sendUpgradeMemberLevelMessage(upgradeMemberLevelMessage);
    }

    @Override
    public void updateMemberLeve(Long memberId, String memberLevelId) {
        MiniAccount miniAccount =new MiniAccount();
        miniAccount.setId(memberId);
        miniAccount.setMemberLevelId(memberLevelId);
        this.baseMapper.updateById(miniAccount);
    }

    /**
     * 更新缓存
     *
     * @param miniAccount com.medusa.gruul.account.api.entity.MiniAccount
     */
    private void accountCache(MiniAccount miniAccount) {
        String key = RedisConstant.ACCOUNT_DB_KEY.concat(miniAccount.getUserId());
        AccountRedis accountRedis = new AccountRedis();
        accountRedis.set(key, JSON.toJSONString(miniAccount));
        CompletableFuture.runAsync(() -> {
            MiniAccountOauths miniAccountOauths = miniAccountOauthsService.getByUserId(OauthTypeEnum.WX_MINI.getType(), miniAccount.getUserId());
            String loginKey = RedisConstant.LOGIN_KEY.concat(":").concat(OauthTypeEnum.WX_MINI.getType().toString()).concat(":").concat(miniAccountOauths.getOpenId());
            MiniAccountExtends accountExtends = miniAccountExtendsService.findByCurrentStatus(miniAccountOauths.getUserId());
            accuontOauthsCache(loginKey, miniAccountOauths, miniAccount, accountExtends);
        });
    }

    private MiniAccount accountLoginCache(MiniAccount miniAccount) {
        String date = LocalDateTimeUtils.formatTime(LocalDateTime.now(), "yyyy-MM-dd");
        String key = RedisConstant.ACCOUNT_LOGIN_TIME.concat(miniAccount.getUserId()+":"+date);
        AccountRedis accountRedis = new AccountRedis();
        final long time = 2*24*3600*1000;
        String result = accountRedis.setNxPx(key, JSON.toJSONString(miniAccount), time);
        if(CommonConstants.REDIS_SUCCESS.equalsIgnoreCase(result)){
            log.info("添加登录获取积分");
            miniAccount = miniAccountIntegralService.loginAddIntegralDay(miniAccount);
        }
        return miniAccount;
    }


    @Override
    public UserInfoVo getUserInfo(Integer infoLevel) {
        UserInfoVo vo = new UserInfoVo();
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (curUser == null) {
            throw new ServiceException("token错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(curUser.getUserId());
        if (miniAccountExtends == null) {
            throw new ServiceException("数据错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        MiniAccount miniAccount = this.getByShopUserId(curUser.getUserId());
        BeanUtils.copyProperties(miniAccount, vo);

        vo.setShopUserId(curUser.getUserId());

        vo.setShareCode(miniAccount.getSelfInviteCode());
        // 设置分享码，加密后的字符串
//        try {
//            String shareCode = AESUtil.encryptDataBase64MD5Key(miniAccountExtends.getUserId(), null);
//            vo.setShareCode(URLEncoder.encode(shareCode, "UTF-8"));
//        } catch (Exception exception) {
//            throw new ServiceException("数据错误", SystemCode.DATA_SHARE_FAILED_CODE);
//        }
        if (infoLevel.equals(CommonConstants.NUMBER_TWO)) {
            UserInfoExtendsVo extendsVo = new UserInfoExtendsVo();
            BeanUtils.copyProperties(miniAccountExtends, extendsVo);
            //用户扩展信息
            vo.setInfoExtends(extendsVo);
        }
        if (infoLevel.equals(CommonConstants.NUMBER_THREE)) {
            UserBlacklistAstrictVo astrictVo = new UserBlacklistAstrictVo();
            List<Integer> restrictTypes = miniAccountRestrictService.getByUserId(curUser.getUserId());
            if (CollectionUtil.isNotEmpty(restrictTypes)) {
                for (Integer restrictType : restrictTypes) {
                    if (restrictType.equals(BlacklistEnum.REJECT_ORDER.getType())) {
                        astrictVo.setRejectOrder(true);
                    }

                    if (restrictType.equals(BlacklistEnum.REJECT_COMMENT.getType())) {
                        astrictVo.setRejectOrder(true);
                    }
                }

            }
            vo.setAstrictVo(astrictVo);
        }

        String userId = miniAccountExtends.getUserId();
        LambdaQueryWrapper<MemberLevelRelation>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberLevelRelation::getUserId,userId);
        List<MemberLevelRelation> list = memberLevelRelationService.list(wrapper);
        List<Long>memberTypeIds = new ArrayList<>();
        if(list!=null&&list.size()>0){
            for (MemberLevelRelation memberLevelRelation : list) {
                memberTypeIds.add(memberLevelRelation.getMemberTypeId());
            }
        }
        vo.setMemberTypeId(memberTypeIds);
        return vo;
    }

    @Autowired
    private IMiniAccountBankService miniAccountBankService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PageUtils<List<UserListVo>> userList(String nikeName, String phone, Long tagId,
                                                String orderSuccessStartTime, String orderSuccessEndTime,
                                                Integer page, Integer size, Integer sortType,Integer userId,Integer superiorAndSubordinate,
                                                Long parentId,Long aboveParentId, String shopIds,Integer expireDays) {

        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();

        //到期天数
        Integer activeDays = null;
        MemberActiveSettingVo memberActiveSetting = memberActiveSettingService.getMemberActiveSetting();

        MemberType memberType = memberTypeService.getDefaultMemberType();

        if(memberActiveSetting!=null
                &&memberActiveSetting.getStatus() == MemberActiveSettingStatusEnum.YES.getStatus()
                &&memberActiveSetting.getActiveDays()!=null){

            activeDays = memberActiveSetting.getActiveDays();
            LambdaQueryWrapper<MiniAccountExtends>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MiniAccountExtends::getDeleted,CommonConstants.NUMBER_ZERO);
            List<MiniAccountExtends> list = miniAccountExtendsService.list(wrapper);
            if(list!=null&&list.size()>0){
                for (MiniAccountExtends miniAccountExtends : list) {
                    MemberLevelRelationParam param = new MemberLevelRelationParam();
                    param.setMemberTypeId(Long.valueOf(memberType.getId()));
                    param.setUserId(miniAccountExtends.getUserId());
                    String memberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(param);
                    if(StringUtils.isNotEmpty(memberLevelId)){
                        MemberLevel memberLevel = memberLevelService.getById(memberLevelId);
                        if(memberLevel.getMemberFlag() == MemberFlagEnum.YES.getStatus()){
                            LocalDateTime lastDealTime = miniAccountExtends.getLastDealTime();
                            LocalDateTime now = LocalDateTime.now();
                            //最后支付时间不存在，取用户注册时间
                            if(lastDealTime==null){
                                MiniAccount miniAccount = this.getByShopUserId(miniAccountExtends.getShopUserId());
                                lastDealTime = miniAccount.getRegisterTime();
                            }
                            //存在最后支付时间，用户注册时间不存在，设置用户为冻结状态
                            if(lastDealTime!=null){
                                LocalDateTime endLocalDateTime = lastDealTime.plusDays(memberActiveSetting.getActiveDays());
                                long day = Duration.between(now, endLocalDateTime).toDays();
                                if(day<0){
                                    miniAccountExtends.setStatus(MiniAccountExtendsStatusEnum.NO.getStatus());
                                    miniAccountExtendsService.updateById(miniAccountExtends);
                                }
                            }else{
                                miniAccountExtends.setStatus(MiniAccountExtendsStatusEnum.NO.getStatus());
                                miniAccountExtendsService.updateById(miniAccountExtends);
                            }
                        }
                    }
                }
            }
        }

        Map<String, Object> paramMap = new HashMap<>(8);
        if (tagId != null) {
            paramMap.put("tagId", tagId);
        }
        if (StrUtil.isNotEmpty(orderSuccessStartTime)) {
            paramMap.put("orderSuccessStartTime", orderSuccessStartTime);
        }
        if (StrUtil.isNotEmpty(orderSuccessEndTime)) {
            paramMap.put("orderSuccessEndTime", orderSuccessEndTime);
        }
        if (StrUtil.isNotBlank(nikeName)) {
            AtomicReference<String> name = new AtomicReference<>("%");
            nikeName.chars().mapToObj(obj -> (char) obj).forEach(obj -> {
                name.set(name.get().concat(obj.toString()).concat("%"));
            });
            paramMap.put("nikeName", name.toString());
        }
        if (StrUtil.isNotBlank(phone)) {
            AtomicReference<String> name = new AtomicReference<>("%");
            phone.chars().mapToObj(obj -> (char) obj).forEach(obj -> {
                name.set(name.get().concat(obj.toString()).concat("%"));
            });
            paramMap.put("phone", name.toString());
        }
        if (sortType != null) {
            paramMap.put("sortType", sortType);
        }

        int number=2;
        if( superiorAndSubordinate !=null && superiorAndSubordinate==number ){
            UserListDto userParent=this.baseMapper.getUserParent(userId);
            userId=Integer.parseInt(userParent.getParentId());
        }
        if(userId!=null){
            paramMap.put("userId", userId);
        }

        if(parentId!=null){
            MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(parentId+"");
            paramMap.put("parentId", miniAccountExtends.getUserId());
        }
        if(aboveParentId!=null){
            MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(aboveParentId+"");
            paramMap.put("aboveParentId", miniAccountExtends.getUserId());
        }
        List<String> shopIdList = new ArrayList<>();
        if(StrUtil.isNotBlank(shopIds)){
            // 用英文间隔的shopIds 转换成  List
            shopIdList = Arrays.asList(shopIds.split(","));
            paramMap.put("shopIds", shopIdList);
        }
        String sourceShopId = ShopContextHolder.getShopId();
        ShopsPartner shopsPartner = remoteShopsService.getShopsPartnerMain();

        //ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        if(!(null != shopsPartner && shopsPartner.getShopId().equals(sourceShopId))){
            // 非主店铺登录用户，只能查出该店铺关联的会员数据
            shopIdList = new ArrayList<>();
            shopIdList.add(sourceShopId);
            paramMap.put("shopIds", shopIdList);
        }



        String cardAuthorization = request.getParameter("cardAuthorization");
        if (StrUtil.isNotBlank(cardAuthorization) && !cardAuthorization.equals("-2")){
            paramMap.put("cardAuthorization", cardAuthorization);
        }

        // 获取到期天数过滤参数
        if(expireDays!=null&&activeDays!=null){
            paramMap.put("expireDays", expireDays-activeDays);
        }



        IPage<UserListDto> iPage = this.baseMapper.selectByUserList(new Page<>(page, size), paramMap);

        //还原原来的店铺
        //ShopContextHolder.setShopId(sourceShopId);

        List<UserListDto> records = iPage.getRecords();
        if (CollectionUtil.isEmpty(records)) {
            return new PageUtils(new ArrayList(0), (int) iPage.getTotal(), (int) iPage.getSize(), (int) iPage.getCurrent());
        }
        List<UserListVo> vos = new LinkedList<>();
        List<String> userIdList = records.stream().map(UserListDto::getShopUserId).collect(Collectors.toList());
        List<String> memberLeveIdList = records.stream().filter(s-> s.getMemberLevelId() != null).map(UserListDto::getMemberLevelId).collect(Collectors.toList());

        //查询用户标签
        Map<String, List<UserTagVo>> userTag = MapUtil.newHashMap(userIdList.size());
        List<MiniAccountTagGroup> tagGroups = miniAccountTagGroupService.getByUserListId(userIdList);
        if (CollectionUtil.isNotEmpty(tagGroups)) {
            setAccountGroupTag(userTag, tagGroups);
        }
        //查询会员等级
//        Map<String, List<MemberLevelVo>> userLeve = MapUtil.newHashMap(userIdList.size());
//        List<MemberLevel> memberLeveGroups = memberLevelService.getByMemberLeveIdList(memberLeveIdList);
//        if (CollectionUtil.isNotEmpty(memberLeveGroups)) {
//            setAccountGroupLeve(userLeve, memberLeveGroups);
//        }

        //封装用户数据
        setPcAccountListVos(records, vos, userTag);
        return new PageUtils(vos, (int) iPage.getTotal(), (int) iPage.getSize(), (int) iPage.getCurrent());
    }

    @Override
    public MiniAccountDetailVo getMiniAccountDetail(String userId) {
        MiniAccountDetailVo miniAccountDetailVo = this.baseMapper.getMiniAccountDetail(userId);
        String phone = miniAccountDetailVo.getPhone();
        if(StringUtils.isNotEmpty(phone)){
            miniAccountDetailVo.setPhone(phone.substring(0,3)+"*****"+phone.substring(8,11));
        }
        if (miniAccountDetailVo.getUserId()!=null){
            // 银行卡
            List<MiniAccountBankVo> miniAccountBank = miniAccountBankService.getMiniAccountBank(miniAccountDetailVo.getUserId());
            if (CollectionUtil.isNotEmpty(miniAccountBank)){
                miniAccountDetailVo.setAccountBank(miniAccountBank);
            }
            // 身份证
            MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(miniAccountDetailVo.getUserId());
            if (miniAccountExtends != null && miniAccountExtends.getIdCardFaceUrl() != null && miniAccountExtends.getIdCardBackUrl() != null){
                miniAccountDetailVo.setIdCardFaceUrl(miniAccountExtends.getIdCardFaceUrl());
                miniAccountDetailVo.setIdCardBackUrl(miniAccountExtends.getIdCardBackUrl());
            }
        }


        return miniAccountDetailVo;
    }

    @Override
    public PageUtils<List<UserListVo>> externalUserList(Integer page, Integer size) {
        Map<String, Object> paramMap = new HashMap<>(8);

        IPage<UserListDto> iPage = this.baseMapper.selectExternalByUserList(new Page<>(page, size), paramMap);
        List<UserListDto> records = iPage.getRecords();
        if (CollectionUtil.isEmpty(records)) {
            return new PageUtils(new ArrayList(0), (int) iPage.getTotal(), (int) iPage.getSize(), (int) iPage.getCurrent());
        }
        List<UserListVo> vos = new LinkedList<>();
        List<String> userIdList = records.stream().map(UserListDto::getShopUserId).collect(Collectors.toList());
        List<String> memberLeveIdList = records.stream().filter(s-> s.getMemberLevelId() != null).map(UserListDto::getMemberLevelId).collect(Collectors.toList());

        //查询用户标签
        Map<String, List<UserTagVo>> userTag = MapUtil.newHashMap(userIdList.size());
        List<MiniAccountTagGroup> tagGroups = miniAccountTagGroupService.getByUserListId(userIdList);
        if (CollectionUtil.isNotEmpty(tagGroups)) {
            setAccountGroupTag(userTag, tagGroups);
        }
        //查询会员等级
//        Map<String, List<MemberLevelVo>> userLeve = MapUtil.newHashMap(memberLeveIdList.size());
//        List<MemberLevel> memberLeveGroups = memberLevelService.getByMemberLeveIdList(memberLeveIdList);
//        if (CollectionUtil.isNotEmpty(memberLeveGroups)) {
//            setAccountGroupLeve(userLeve, memberLeveGroups);
//        }

        //封装用户数据
        setPcAccountListVos(records, vos, userTag);

        if(vos!=null&&vos.size()>0){
            for (UserListVo vo : vos) {
                try {
                    vo.setNikeName(URLEncoder.encode(vo.getNikeName(), "UTF-8"));
                } catch (Exception exception) {
                    throw new ServiceException("数据错误", SystemCode.DATA_SHARE_FAILED_CODE);
                }
            }
        }
        //将发送的数据状态改为已发送
        List<String> idList=records.stream().map(UserListDto::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(idList)){
            updateSendStatus(idList, ExternalAccountEnum.ISSUED.getStatus()+"");
        }

        return new PageUtils(vos, (int) iPage.getTotal(), (int) iPage.getSize(), (int) iPage.getCurrent());
    }

    @Override
    public PageUtils<List<UserListVo>> getUserList(Integer page, Integer size) {

        Map<String, Object> paramMap = new HashMap<>(8);

        IPage<UserListDto> iPage = this.baseMapper.selectExternalByUserList(new Page<>(page, size), paramMap);
        List<UserListDto> records = iPage.getRecords();
        if (CollectionUtil.isEmpty(records)) {
            return new PageUtils(new ArrayList(0), (int) iPage.getTotal(), (int) iPage.getSize(), (int) iPage.getCurrent());
        }
        List<UserListVo> vos = new LinkedList<>();
        //封装用户数据
        setPcAccountListVosNoTagAndLevel(records, vos);

        List<String> idList=records.stream().map(UserListDto::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(idList)){
            updateSendStatus(idList, ExternalAccountEnum.ISSUED.getStatus()+"");
        }
        return new PageUtils(vos, (int) iPage.getTotal(), (int) iPage.getSize(), (int) iPage.getCurrent());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSendStatus(List<String> orderIds,String sendStatus) {
        baseMapper.updateSendStatus(orderIds,sendStatus);
    }

    /**
     * @param records 用户数据
     * @param vos     标签数据
     * @param userTag 用户标签
     *  @param userLeve 会员等级
     */
    private void setPcAccountListVos(List<UserListDto> records, List<UserListVo> vos, Map<String, List<UserTagVo>> userTag) {
        // 查询所有商家列表
        List<ShopsPartner> shopsPartnerList = this.remoteShopsService.getShopPartnerList();
        Map<String, ShopsPartner> shopsPartnerMap = new HashMap<>();
        if(!CollectionUtil.isEmpty(shopsPartnerList)){
            shopsPartnerMap = shopsPartnerList.stream().collect(Collectors.toMap(ShopsPartner::getShopId, v -> v));
        }

        for (UserListDto record : records) {
            UserListVo vo = new UserListVo();

            BeanUtils.copyProperties(record, vo);
            vo.setJoinDate(DateUtil.format(vo.getFirstLoginTime(), "yyyy-MM-dd"));
            //Todo 获取返利设置里会员返利金额是否可用,可用：返利+用户余额  不可用：用户余额
            vo.setBalance(record.getBalance());
            vo.setUserId(record.getShopUserId());
            List<UserTagVo> userTagVos = userTag.get(record.getShopUserId());



            String userId = record.getUserId();
            MemberLevelRelationParam memberLevelRelationParam = new MemberLevelRelationParam();
            memberLevelRelationParam.setUserId(userId);
            List<MemberLevelRelation> list = memberLevelRelationService.getMemberLevelRelationNotMemberLevelId(memberLevelRelationParam);
            String memberLevelName = "";

            LocalDateTime orderLastDealTime = record.getOrderLastDealTime() == null?record.getRegisterTime():record.getOrderLastDealTime();
            //到期时间
            String endTime = "";
            //到期时长
            String endDays = "";
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime now = LocalDateTime.now();
            if(orderLastDealTime != null){
                MemberActiveSettingVo memberActiveSetting = memberActiveSettingService.getMemberActiveSetting();
                if(memberActiveSetting!=null&&memberActiveSetting.getStatus() == MemberActiveSettingStatusEnum.YES.getStatus()){
                    if(memberActiveSetting.getActiveDays()!=null){
                        LocalDateTime endDateTime = orderLastDealTime.plusDays(memberActiveSetting.getActiveDays());
                        endTime = endDateTime.format(formatter);
                        long days = Duration.between(now, endDateTime).toDays();
                        if(days>=0){
                            endDays = days + "天";
                        }else{
                            endDays = "已到期";
                        }
                    }
                }
            }

            List<UserMemberLevelVo>userMemberLevelList = new ArrayList<>();
            if(list!=null&&list.size()>0){
                for (MemberLevelRelation memberLevelRelation : list) {
                    UserMemberLevelVo userMemberLevelVo = new UserMemberLevelVo();
                    Long memberTypeId = memberLevelRelation.getMemberTypeId();
                    String memberLevelId = memberLevelRelation.getMemberLevelId();
                    MemberType memberType = memberTypeService.getById(memberTypeId);
                    MemberLevel memberLevel = memberLevelService.getById(memberLevelId);
                    if(memberType!=null){
                        userMemberLevelVo.setMemberTypeName(memberType.getName());
                    }
                    if(memberLevel!=null){
                        userMemberLevelVo.setMemberLevelName(memberLevel.getMemberLevel());
                        //获取该用户的会员等级名字
                        if(memberLevelName.length()>0){
                            memberLevelName+="、";
                        }
                        memberLevelName += memberLevel.getMemberLevel();
                    }


                    userMemberLevelVo.setMemberFlag(memberLevel.getMemberFlag());
                    long days = Duration.between(memberLevelRelation.getUpLevelTime(), now).toDays();

                    userMemberLevelVo.setJoinDays("无");
                    userMemberLevelVo.setJoinTime("无");
                    userMemberLevelVo.setEndTime("无");
                    userMemberLevelVo.setEndDays("无");

                    if (StrUtil.isNotBlank(record.getUserName()) || record.getRegisterTime()!=null ){
                        if(memberLevel.getMemberFlag() == MemberFlagEnum.YES.getStatus()){
                            userMemberLevelVo.setJoinDays(days+"天");
                            userMemberLevelVo.setJoinTime(memberLevelRelation.getUpLevelTime().format(formatter));
                            userMemberLevelVo.setEndTime(endTime);
                            userMemberLevelVo.setEndDays(endDays);
                        }
                    }
                    userMemberLevelList.add(userMemberLevelVo);
                }
            }
            vo.setUserMemberLevelList(userMemberLevelList);
            vo.setMemberLevelName(memberLevelName);
//            List<MemberLevelVo> memberLevelVos=userLeve.get(record.getMemberLevelId());
//            if(memberLevelVos!=null && memberLevelVos.size()>0){
//                //获取该用户的会员等级名字
//                vo.setMemberLevelName(memberLevelVos.get(0).getMemberLevel());
//                vo.setMemberLevelCode(memberLevelVos.get(0).getLevelCode());
//                vo.setMemberLevelVos(memberLevelVos.get(0));
//            }
            vo.setUserTagVos(userTagVos);
            vo.setState(2);


            AccountInfo accountInfo = remoteMiniInfoService.getAccountInfo(record.getAuditPlatformUserId()+"");
            if(accountInfo!=null){
                vo.setAuditPlatformUserName(accountInfo.getNikeName());
            }

            if (record.getRecommendUserId()!= null){
                RelationInfoVo relationInfoVo = remoteMiniInfoService.getRelationInfoByMiniAccountId(record.getRecommendUserId());
                if (relationInfoVo != null && relationInfoVo.getEmployeeName() != null){
                    String employeeName = relationInfoVo.getEmployeeName();
                    vo.setRecommendEmployeeName(employeeName);
                }else {
                    MiniAccount recommendMiniAccount = this.getById(record.getRecommendUserId());
                    if (recommendMiniAccount!= null){
                        vo.setRecommendSaleFlag(recommendMiniAccount.getSaleFlag());
                    }
                }
            }


            vos.add(vo);
        }
    }
    private void setPcAccountListVosNoTagAndLevel(List<UserListDto> records, List<UserListVo> vos) {
        for (UserListDto record : records) {
            UserListVo vo = new UserListVo();
            BeanUtils.copyProperties(record, vo);
            vo.setJoinDate(DateUtil.format(vo.getFirstLoginTime(), "yyyy-MM-dd"));
            //Todo 获取返利设置里会员返利金额是否可用,可用：返利+用户余额  不可用：用户余额
            vo.setBalance(record.getBalance());
            vo.setUserId(record.getShopUserId());
            vo.setState(2);
            vos.add(vo);
        }
    }

    /**
     * 设置会员等级
     *
     * @param userLeve   会员等级
     * @param memberLeveGroups 用户相关的会员等级
     */
    private void setAccountGroupLeve(Map<String, List<MemberLevelVo>> userLeve, List<MemberLevel> memberLeveGroups) {
        List<String> memberLevelIdList = memberLeveGroups.stream().map(MemberLevel::getId).distinct().collect(Collectors.toList());
        List<MemberLevel> memberLevel = memberLevelService.getByIdList(memberLevelIdList);
        Map<String, MemberLevel> memberLevelMap = memberLevel.stream().collect(Collectors.toMap(MemberLevel::getId, v -> v));
        memberLeveGroups.stream().collect(Collectors.groupingBy(MemberLevel::getId)).forEach((k, v) -> {
            List<MemberLevelVo> userRightsVos = new ArrayList<>(v.size());
            for (MemberLevel miniAccountMemberLevel : v) {
                MemberLevel miniAccountLeve = memberLevelMap.get(miniAccountMemberLevel.getId());
                if (miniAccountLeve != null) {
                    MemberLevelVo userRightsVo = new MemberLevelVo();
                    userRightsVo.setId(miniAccountLeve.getId());
                    userRightsVo.setMemberLevel(miniAccountLeve.getMemberLevel());
                    userRightsVo.setIsSelected(1);
                    userRightsVo.setLevelCode(miniAccountLeve.getLevelCode());
                    userRightsVos.add(userRightsVo);
                }
            }
            userLeve.put(k, userRightsVos);
        });
    }


    /**
     * 设置用户标签
     *
     * @param userTag   标签
     * @param tagGroups 用户相关标签
     */
    private void setAccountGroupTag(Map<String, List<UserTagVo>> userTag, List<MiniAccountTagGroup> tagGroups) {
        List<Long> tagIdList = tagGroups.stream().map(MiniAccountTagGroup::getTagId).distinct().collect(Collectors.toList());
        List<MiniAccountTag> miniAccountTags = miniAccountTagService.getByIdList(tagIdList);
        Map<Long, MiniAccountTag> tagMap = miniAccountTags.stream().collect(Collectors.toMap(MiniAccountTag::getId, v -> v));
        tagGroups.stream().collect(Collectors.groupingBy(MiniAccountTagGroup::getUserId)).forEach((k, v) -> {
            List<UserTagVo> userTagVos = new ArrayList<>(v.size());
            for (MiniAccountTagGroup miniAccountTagGroup : v) {
                MiniAccountTag miniAccountTag = tagMap.get(miniAccountTagGroup.getTagId());
                if (miniAccountTag != null) {
                    UserTagVo userTagVo = new UserTagVo();
                    userTagVo.setTagId(miniAccountTag.getId());
                    userTagVo.setTagName(miniAccountTag.getTagName());
                    userTagVos.add(userTagVo);
                }
            }
            userTag.put(k, userTagVos);
        });
    }

    @Override
    public AccountInfoDto accountInfo(String shopUserId, List<Integer> infos) {
        AccountInfoDto accountInfoDto = new AccountInfoDto();

        for (Integer obj : infos) {
            switch (obj) {
                case 1:
                    MiniAccount miniAccount = this.getByShopUserId(shopUserId);
                    accountInfoDto.setMiniAccountunt(miniAccount);
                    break;
                case 2:
                    MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(shopUserId);
                    accountInfoDto.setMiniAccountExtends(miniAccountExtends);
                    List<Integer> restrictTypes = miniAccountRestrictService.getByUserId(shopUserId);
                    accountInfoDto.setRestrictTypes(restrictTypes);
                    break;
                case 3:
                    List<MiniAccountAddress> miniAccountAddresses = iMiniAccountAddressService.getUserAddress(shopUserId);
                    if (CollectionUtil.isNotEmpty(miniAccountAddresses)) {
                        //默认地址,排序在首位
                        CollectionUtil.sort(miniAccountAddresses, Comparator.comparing(MiniAccountAddress::getIsDefault).reversed());
                    }
                    accountInfoDto.setMiniAccountAddress(miniAccountAddresses);
                    break;
                case 4:
                    MiniAccountExtends ext = miniAccountExtendsService.findByShopUserId(shopUserId);
                    MiniAccountOauths miniAccountOauths = miniAccountOauthsService.getByUserId(OauthTypeEnum.WX_MINI.getType(), ext.getUserId());
                    accountInfoDto.setMiniAccountOauths(miniAccountOauths);
                    break;
                default:
            }
        }
        return accountInfoDto;
    }

    @Override
    public MiniAccountOauths getMiniAccountOauths(String userId) {
        MiniAccountOauths miniAccountOauths = miniAccountOauthsService.getByUserId(OauthTypeEnum.WX_MINI.getType(), userId);
        return miniAccountOauths;
    }

    @Override
    public PageUtils<List<BlacklistUserVo>> blacklist(Integer page, Integer size, Integer permission, String fuzzy) {
        Map<String, Object> paramMap = new HashMap<>(8);
        paramMap.put("permission", permission);
        if (StrUtil.isNotEmpty(fuzzy)) {
            paramMap.put("fuzzy", "%" + fuzzy + "%");
        }
        IPage<BlacklistUserDto> userDtoIpage = this.baseMapper.selectByBlackListUser(new Page<BlacklistUserDto>(page, size), paramMap);
        List<BlacklistUserDto> records = userDtoIpage.getRecords();
        if (CollectionUtil.isEmpty(records)) {
            return new PageUtils(new ArrayList(0), (int) userDtoIpage.getTotal(), (int) userDtoIpage.getSize(), (int) userDtoIpage.getCurrent());
        }
        List<BlacklistUserVo> vos = new ArrayList<>(records.size());

        List<String> userIds = records.stream().map(BlacklistUserDto::getShopUserId).collect(Collectors.toList());
        Map<String, List<MiniAccountRestrict>> accountRestrictMap = miniAccountRestrictService.getByUserIds(userIds).stream().collect(Collectors.groupingBy(MiniAccountRestrict::getUserId));

        for (BlacklistUserDto record : records) {
            BlacklistUserVo vo = new BlacklistUserVo();
            BeanUtils.copyProperties(record, vo);
            vo.setUserId(record.getShopUserId());
            List<Integer> blacklistTypeList = accountRestrictMap.get(vo.getUserId()).stream().map(MiniAccountRestrict::getRestrictType).collect(Collectors.toList());
            vo.setBlacklistType(blacklistTypeList);
            vos.add(vo);
        }

        return new PageUtils(vos, (int) userDtoIpage.getTotal(), (int) userDtoIpage.getSize(), (int) userDtoIpage.getCurrent());
    }


    @Override
    public List<MiniAccountExtDto> accountsInfoList(List<String> shopUserIds) {
        return this.baseMapper.selectByShopUserIds(shopUserIds);
    }


    @Override
    public String qrCode() {
        CurUserDto httpCurUser = CurUserUtil.getHttpCurUser();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("shopUserId", httpCurUser.getUserId());
        QrConfig qrConfig = QrConfig.create();
        BufferedImage generate = QrCodeUtil.generate(jsonObject.toJSONString(), qrConfig);
        byte[] bytes = QrCodeUtil.generatePng(jsonObject.toJSONString(), qrConfig);
        return Base64.encode(bytes);
    }


    @Override
    public MiniAccount getByShopUserId(String shopUserId) {
        return this.getBaseMapper().selectByShopUserId(shopUserId);
    }

    @Override
    public List<MemberLevelRightsVo> getPowerAll() {

        return this.getBaseMapper().getPowerAll();

    }

    /**
     * 保存外部系统对接的用户信息
     * @param userDto
     * @return
     */
    @Override
    public MiniAccount outSave(UserListDto userDto){
        //1.通过手机号判断用户是否存在 2，无记录，则为新增
        MiniAccount miniAccount = null;
        if(StrUtil.isNotEmpty(userDto.getPhone())){
            LambdaQueryWrapper<MiniAccount> phoneWrapper = new LambdaQueryWrapper<>();
            phoneWrapper.eq(MiniAccount::getPhone, userDto.getPhone());
            List<MiniAccount> miniAccountList = this.baseMapper.selectList(phoneWrapper);
            if(CollectionUtil.isNotEmpty(miniAccountList)){
                miniAccount = miniAccountList.get(0);
            }
        }
        LambdaQueryWrapper<MemberLevel> levelWrapper = new LambdaQueryWrapper<>();
        levelWrapper.eq(MemberLevel::getLevelCode, userDto.getMemberLevelCode());
        List<MemberLevel> levelList = this.memberLevelService.list(levelWrapper);
        String levelId = CollectionUtil.isEmpty(levelList) ? null : levelList.get(0).getId();
        if(null != miniAccount){
            //记录已经存在，更新
            miniAccount.setMemberLevelId(levelId);
            miniAccount.setSendStatus(ExternalAccountEnum.ISSUED.getStatus());
            miniAccount.setPhone(userDto.getPhone());
            miniAccount.setNikeName(userDto.getNikeName());
            miniAccount.setOutApiId(userDto.getOutApiId());
            this.updateById(miniAccount);
        }else {
            //记录不存在，新增
            miniAccount = new MiniAccount();
            BeanUtil.copyProperties(userDto, miniAccount);
            miniAccount.setPhone(userDto.getPhone());
            miniAccount.setSendStatus(ExternalAccountEnum.ISSUED.getStatus());
            miniAccount.setMemberLevelId(levelId);
            //miniAccount.setCardNumber(userDto.getCardNumber());
            miniAccount.setWhetherAuthorization(Boolean.FALSE);
            miniAccount.setOutApiId(userDto.getOutApiId());
            LocalDateTime firstLoginTime = StrUtil.isEmpty(userDto.getJoinDate()) ? null :
                    DateUtil.toLocalDateTime(DateUtil.parse(userDto.getJoinDate(), "yyyy-MM-dd"));
            miniAccount.setFirstLoginTime(firstLoginTime);
            this.save(miniAccount);
            miniAccount.setCardNumber(miniAccount.getId()+"");
            miniAccount.setSourceType(SourceTypeEnum.OTHER.getStatus());
            this.updateById(miniAccount);
        }
        return miniAccount;
    }

    @Override
    public UserIntegralVo getUserInfoIntegral()
    {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (curUser == null) {
            throw new ServiceException("token错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(curUser.getUserId());
        UserIntegralVo userInfoIntegral = this.baseMapper.getUserInfoIntegral(miniAccountExtends.getUserId());

        LambdaQueryWrapper<MiniAccount>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccount::getDeleted,CommonConstants.NUMBER_ZERO);
        wrapper.eq(MiniAccount::getWhetherAuthorization,CommonConstants.NUMBER_ONE);
        wrapper.orderByDesc(MiniAccount::getIntegral);
        List<MiniAccount> list = this.list(wrapper);
        for(int i =0;i<list.size();i++){
            MiniAccount miniAccount = list.get(i);
            if(miniAccount.getUserId().equals(miniAccountExtends.getUserId())){
                userInfoIntegral.setIntegralRanking(i+1);
                break;
            }
        }


        return userInfoIntegral;
    }

    @Override
    public UserIntegralVo getUserInfoIntegralByUserId(String userId) {
        return this.baseMapper.getUserInfoIntegral(userId);
    }

    @Override
    public PageUtils<MyTeamMiniAccountVo> getMyTeamMiniAccountVo(MyTeamMiniAccountParam myTeamMiniAccountParam) {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (curUser == null) {
            throw new ServiceException("token错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(curUser.getUserId());
        String userId = miniAccountExtends.getUserId();
        myTeamMiniAccountParam.setUserId(userId);
        if(myTeamMiniAccountParam.getType()==1){
            IPage<MyTeamMiniAccountVo> myOneTeamMiniAccountVo = this.baseMapper.getMyOneTeamMiniAccountVo(new Page<>(myTeamMiniAccountParam.getCurrent(), myTeamMiniAccountParam.getSize()), myTeamMiniAccountParam);
            return new PageUtils<MyTeamMiniAccountVo>(myOneTeamMiniAccountVo);

        }else{
            IPage<MyTeamMiniAccountVo> myTwoTeamMiniAccountVo = this.baseMapper.getMyTwoTeamMiniAccountVo(new Page<>(myTeamMiniAccountParam.getCurrent(), myTeamMiniAccountParam.getSize()), myTeamMiniAccountParam);
            return new PageUtils<MyTeamMiniAccountVo>(myTwoTeamMiniAccountVo);
        }
    }

    @Override
    public Integer getTeamNumCount(Integer type) {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (curUser == null) {
            throw new ServiceException("token错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(curUser.getUserId());
        String userId = miniAccountExtends.getUserId();
        Integer count= 0;
        if(type==1){//一级客户数
            LambdaQueryWrapper<MiniAccount>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MiniAccount::getParentId,userId);
            wrapper.eq(MiniAccount::getWhetherAuthorization,1);
            count = this.baseMapper.selectCount(wrapper);
        }
        if(type==2){//二级客户数
            LambdaQueryWrapper<MiniAccount>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MiniAccount::getAboveParentId,userId);
            wrapper.eq(MiniAccount::getWhetherAuthorization,1);
            count = this.baseMapper.selectCount(wrapper);
        }
        return count;
    }


    /**
     * app登录
     * @param loginDto
     * @param resetTokenFlag
     * @return
     */
    @Override
    public LoginBaseInfoVo appLogin(AppLoginDto loginDto, boolean resetTokenFlag) {
        LoginBaseInfoVo vo = null;
        switch (loginDto.getLoginType()) {
            case 1:
                vo = passwdLogin(loginDto.getPhone(), loginDto.getPassword(), loginDto.getTenantId(), resetTokenFlag);
                break;
            case 2:
                vo = phoneCodeLogin(loginDto.getPhone(), loginDto.getCertificate(), resetTokenFlag);
                break;
            default:
                throw new ServiceException("非法登录请求");
        }
        return vo;
    }

    /**
     * app注册
     * @param appRegisterDto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean appRegister(AppRegisterDto appRegisterDto){
        String tenantId  = TenantContextHolder.getTenantId();
        if(StrUtil.isBlank(tenantId) || tenantId.equals(CommonConstants.DEFAULT_TENANT_ID)){
            throw new ServiceException("入参错误");
        }
        MiniAccount accountInfo = this.getByPhone(appRegisterDto.getPhone());
        if (accountInfo != null) {
            throw new ServiceException("手机号已经存在");
        }
        // 验证码校验通过
        // sendCodeService.certificateCheck(appRegisterDto.getCertificate(), appRegisterDto.getPhone(), AuthCodeEnum.CREATE_MINI_REGISTER.getType());
        //初始化信息
        LocalDateTime currentDateTime = LocalDateTime.now();
        LoginDto login = new LoginDto();
        login.setInviteCode(appRegisterDto.getInviteCode());
        MiniAccountOauthsDto dto = initMiniInfo(currentDateTime, login, null, true, OauthTypeEnum.APP, appRegisterDto);
        //用户授权成功添加积分
        if(dto!=null){
            MiniAccount miniAccount = this.getByUserId(dto.getUserId());
            miniAccountIntegralService.loginAddIntegral(miniAccount);
        }
        return true;
    }

    /**
     * 密码登录
     *
     * @param phone    手机号
     * @param password 密码
     * @return
     */
    private LoginBaseInfoVo passwdLogin(String phone, String password, String tenantId, boolean resetTokenFlag) {
        MiniAccount accountInfo = this.getByPhone(phone);
        if (accountInfo == null) {
            throw new ServiceException("账号或密码错误");
        }
        String md5Pw = SecureUtil.md5(password.concat(accountInfo.getSalt()));
        if (!md5Pw.equals(accountInfo.getPasswd())) {
            throw new ServiceException("账号或密码错误");
        }

        return getLoginInfoVo(accountInfo);
    }

    private MiniAccount getByPhone(String username) {
        if (!ReUtil.isMatch(RegexConstants.REGEX_MOBILE_EXACT, username)) {
            throw new ServiceException("手机号错误", SystemCode.DATA_NOT_EXIST.getCode());
        }
        return this.baseMapper.selectOne(new QueryWrapper<MiniAccount>().eq("phone", username));
    }

    private LoginBaseInfoVo phoneCodeLogin(String phone, String certificate, boolean resetTokenFlag) {
        MiniAccount accountInfo = this.getByPhone(phone);
        if (accountInfo == null) {
            throw new ServiceException("账号不存在");
        }
        sendCodeService.certificateCheck(certificate, phone, AuthCodeEnum.MINI_LOGIN.getType());
        return getLoginInfoVo(accountInfo);
    }

    /**
     * 返回登录信息，设置登录缓存
     * @param accountInfo
     * @return
     */
    private LoginBaseInfoVo getLoginInfoVo(MiniAccount accountInfo){
        String loginKey = RedisConstant.LOGIN_KEY.concat(":")
                .concat(OauthTypeEnum.APP.getType().toString()).concat(":").concat(accountInfo.getUserId());
        AccountRedis accountRedis = new AccountRedis();
        //获取缓存中用户数据
        String loginBase = accountRedis.get(loginKey);
        MiniAccountOauthsDto miniAccountOauthsDto = null;
        if (StrUtil.isNotEmpty(loginBase)) {
            miniAccountOauthsDto = JSON.parseObject(loginBase, MiniAccountOauthsDto.class);
        } else {
            MiniAccountOauths miniAccountOauths = miniAccountOauthsService.getByUserId(OauthTypeEnum.APP.getType(), accountInfo.getUserId());
            //缓存用户数据
            MiniAccountExtends accountExtends = miniAccountExtendsService.findByCurrentStatus(accountInfo.getUserId());
            miniAccountOauthsDto = accuontOauthsCache(loginKey, miniAccountOauths, accountInfo, accountExtends);
        }

        LocalDateTime currentDateTime = LocalDateTime.now();
        String userId = miniAccountOauthsDto.getUserId();
        CompletableFuture.runAsync(() -> {
            //异步更新最后登陆时间
            MiniAccountExtends accountExtends = miniAccountExtendsService.findByCurrentStatus(userId);
            miniAccountExtendsService.update(new UpdateWrapper<MiniAccountExtends>()
                    .set("last_login_time", currentDateTime).eq("shop_user_id", accountExtends.getShopUserId()));
        }).exceptionally(throwable -> {
            throwable.printStackTrace();
            return null;
        });
        //封装返回对象
        LoginBaseInfoVo vo = new LoginBaseInfoVo();
        vo.setToken(RedisConstant.ACCOUNT_KEY.concat(miniAccountOauthsDto.getToken()));
        vo.setWhetherAuthorization(accountInfo.getWhetherAuthorization());
        return vo;
    }

    /**
     * 上传用户头像单独接口
     * @param updateUserBaseInfoDto dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadAvatar(UpdateUserBaseInfoDto updateUserBaseInfoDto) {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        MiniAccount miniAccount = this.getByShopUserId(curUser.getUserId());
        if (miniAccount == null) {
            throw new ServiceException("数据错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        miniAccount.setAvatarUrl(updateUserBaseInfoDto.getAvatarUrl());
        this.updateById(miniAccount);
        //缓存用户最新的基础信息
        accountCache(miniAccount);

    }

    @Override
    public MiniAccountParentVo getMiniAccountParentVo(String userId) {
        MiniAccountParentVo miniAccountParentVo = new MiniAccountParentVo();
        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(userId);
        if(miniAccountExtends!=null){
            userId = miniAccountExtends.getUserId();
        }
        //用户信息
        LambdaQueryWrapper<MiniAccount>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccount::getUserId,userId);
        MiniAccount miniAccount = this.getOne(wrapper);
        if(miniAccount!=null){
            miniAccountParentVo.setUserId(miniAccount.getUserId());
            miniAccountParentVo.setPhone(miniAccount.getPhone());
            miniAccountParentVo.setNikeName(miniAccount.getNikeName());
            if(StringUtil.isNotEmpty(miniAccount.getMemberLevelId())){
                MemberLevel memberLevel = memberLevelService.getById(miniAccount.getMemberLevelId());
                if(memberLevel!=null){
                    miniAccountParentVo.setMemberLevel(memberLevel.getMemberLevel());
                }
            }
            //上级用户信息
            if(StringUtil.isNotEmpty(miniAccount.getParentId())){
                LambdaQueryWrapper<MiniAccount>parentWrapper = new LambdaQueryWrapper<>();
                parentWrapper.eq(MiniAccount::getUserId,miniAccount.getParentId());
                MiniAccount parentMiniAccount = this.getOne(parentWrapper);
                if(parentMiniAccount!=null&&!parentMiniAccount.equals("")){
                    miniAccountParentVo.setParentUserId(parentMiniAccount.getUserId());
                    miniAccountParentVo.setParentNikeName(parentMiniAccount.getNikeName());
                    miniAccountParentVo.setParentPhone(parentMiniAccount.getPhone());
                    if(StringUtil.isNotEmpty(parentMiniAccount.getMemberLevelId())){
                        MemberLevel parentMemberLevel = memberLevelService.getById(parentMiniAccount.getMemberLevelId());
                        if(parentMemberLevel!=null){
                            miniAccountParentVo.setParentMemberLevel(parentMemberLevel.getMemberLevel());
                        }
                    }
                }
            }
            //下级用户信息
            List<MiniAccountChildVo>miniAccountChildVoList = this.baseMapper.getMiniAccountChildVo(miniAccount.getUserId());
            if(miniAccountChildVoList!=null&&miniAccountChildVoList.size()>0){
                miniAccountParentVo.setChildVoList(miniAccountChildVoList);
            }
        }
        return miniAccountParentVo;
    }



    @Override
    public PageUtils<MiniAccountChildVo> getMiniAccountParentList(MiniAccountChildParam param) {
        String userId = param.getUserId();
        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(userId);
        if(miniAccountExtends!=null){
            userId = miniAccountExtends.getUserId();
        }
        param.setUserId(userId);
        IPage<MiniAccountChildVo> miniAccountParentList = this.baseMapper.getMiniAccountParentList(new Page<>(param.getCurrent(), param.getSize()), param);
        return new PageUtils<MiniAccountChildVo>(miniAccountParentList);
    }

    @Override
    public PageUtils<MiniAccountChildVo> getMiniAccountChildList(MiniAccountChildParam param) {
        String userId = param.getUserId();
        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(userId);
        if(miniAccountExtends!=null){
            userId = miniAccountExtends.getUserId();
        }
        LambdaQueryWrapper<MiniAccount>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccount::getUserId,userId);
        MiniAccount miniAccount = this.getOne(wrapper);
        if(miniAccount!=null){
            if(StringUtil.isNotEmpty(miniAccount.getParentId())){
                param.setTParentId(miniAccount.getParentId());
            }
            if(StringUtil.isNotEmpty(miniAccount.getAboveParentId())){
                param.setTAboveParentId(miniAccount.getAboveParentId());
            }
        }
        param.setUserId(userId);
        param.setParentId(userId);
        IPage<MiniAccountChildVo> miniAccountParentList = this.baseMapper.getMiniAccountChildList(new Page<>(param.getCurrent(), param.getSize()), param);
        return new PageUtils<MiniAccountChildVo>(miniAccountParentList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMiniAccountParentChild(MiniAccountParentChildParam param) throws Exception {
        String userId = param.getUserId();
        String parentUserId = param.getParentUserId();
        List<String> childUserIds = param.getChildUserIds();
        if(StringUtil.isEmpty(userId)){
            throw new ServiceException("用户id不能为空！");
        }
        if(StringUtil.isNotEmpty(parentUserId)&&childUserIds!=null&&childUserIds.size()>0&&childUserIds.contains(parentUserId)){
            throw new ServiceException("上下级不能取同一个用户！");
        }
        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(userId);
        if(miniAccountExtends!=null){
            userId = miniAccountExtends.getUserId();
        }
        LambdaQueryWrapper<MiniAccount>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccount::getUserId,userId);
        //获取当前用户
        MiniAccount miniAccount = this.getOne(wrapper);
        String parentId = miniAccount.getParentId();
        //如果用户已经有上级用户，并且给上级用户了产生佣金
        if(StringUtil.isNotEmpty(parentId)){
            if(StringUtil.isEmpty(parentUserId)||(StringUtil.isNotEmpty(parentUserId)&&!parentUserId.equals(parentId))){
                MiniAccountExtends miniAccountExtendsParent = miniAccountExtendsService.findByUserId(parentId);
                String shopParentUserId = miniAccountExtendsParent.getShopUserId();
                String shopUserId = miniAccountExtendsService.findByUserId(userId).getShopUserId();
                LambdaQueryWrapper<MiniAccountCommission>lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(MiniAccountCommission::getUserId,shopParentUserId);
                lambdaQueryWrapper.eq(MiniAccountCommission::getSourceShopUserId,shopUserId);
                int count = miniAccountCommissionService.count(lambdaQueryWrapper);
                if(count>0){
                    throw new ServiceException("用户已经产生佣金，不能修改用户上级关系！");
                }
            }

        }

        //重置上级关系
        miniAccount.setParentId("");
        //重置上上级关系
        miniAccount.setAboveParentId("");
        this.updateById(miniAccount);

        //重置下级关系
        LambdaQueryWrapper<MiniAccount>childWrapper = new LambdaQueryWrapper<>();
        childWrapper.eq(MiniAccount::getParentId,userId);
        List<MiniAccount> childMiniAccountList = this.list(childWrapper);
        if(childMiniAccountList!=null&&childMiniAccountList.size()>0){
            for (MiniAccount childMiniAccount : childMiniAccountList) {
                String childUserId = childMiniAccount.getUserId();
                if((childUserIds==null||childUserIds.size()==0)||(childUserIds!=null&&childUserIds.size()>0&&!childUserIds.contains(childUserId))){
                    String shopUserId = miniAccountExtendsService.findByUserId(userId).getShopUserId();
                    String childShopUserId = miniAccountExtendsService.findByUserId(childMiniAccount.getUserId()).getShopUserId();
                    LambdaQueryWrapper<MiniAccountCommission>lambdaQueryWrapper = new LambdaQueryWrapper<>();
                    lambdaQueryWrapper.eq(MiniAccountCommission::getUserId,shopUserId);
                    lambdaQueryWrapper.eq(MiniAccountCommission::getSourceShopUserId,childShopUserId);
                    int count = miniAccountCommissionService.count(lambdaQueryWrapper);
                    if(count>0){
                        throw new ServiceException("用户已经产生佣金，不能修改用户下级关系！");
                    }
                }
                childMiniAccount.setParentId("");
                childMiniAccount.setAboveParentId("");
                this.updateById(childMiniAccount);
            }
        }
        //重置下下级关系
        LambdaQueryWrapper<MiniAccount>aboveChildWrapper = new LambdaQueryWrapper<>();
        aboveChildWrapper.eq(MiniAccount::getAboveParentId,userId);
        List<MiniAccount> aboveChildMiniAccountList = this.list(aboveChildWrapper);
        if(aboveChildMiniAccountList!=null&&aboveChildMiniAccountList.size()>0){
            for (MiniAccount aboveChildMiniAccount : aboveChildMiniAccountList) {
                aboveChildMiniAccount.setAboveParentId("");
                this.updateById(aboveChildMiniAccount);
            }
        }

        //添加上级关系
        if(StringUtil.isNotEmpty(parentUserId)){

            LambdaQueryWrapper<MiniAccount>parentWrapper = new LambdaQueryWrapper<>();
            parentWrapper.eq(MiniAccount::getUserId,parentUserId);
            miniAccount.setParentId(parentUserId);
            //上级用户
            MiniAccount parentMiniAccount = this.getOne(parentWrapper);
            //如果上级用户存在上级则添加上上级
            String aboveParentId = parentMiniAccount.getParentId();
            if(StringUtil.isNotEmpty(aboveParentId)){
                miniAccount.setAboveParentId(aboveParentId);
            }
            this.updateById(miniAccount);
        }

        //添加下级关系
        if(childUserIds!=null&&childUserIds.size()>0){
            for (String childUserId : childUserIds) {
                LambdaQueryWrapper<MiniAccount> childMiniAccountWrapper = new LambdaQueryWrapper<>();
                childMiniAccountWrapper.eq(MiniAccount::getUserId,childUserId);
                MiniAccount childMiniAccount = this.getOne(childMiniAccountWrapper);
                childMiniAccount.setParentId(userId);
                LambdaQueryWrapper<MiniAccount>wrapper2 = new LambdaQueryWrapper<>();
                wrapper2.eq(MiniAccount::getUserId,userId);
                //获取当前用户
                MiniAccount miniAccount2 = this.getOne(wrapper2);
                parentId = miniAccount2.getParentId();
                childMiniAccount.setAboveParentId(parentId);
                this.updateById(childMiniAccount);

                LambdaQueryWrapper<MiniAccount>childParentMiniAccountWrapper = new LambdaQueryWrapper<>();
                childParentMiniAccountWrapper.eq(MiniAccount::getParentId,childUserId);
                List<MiniAccount> childParentMiniAccountList = this.list(childParentMiniAccountWrapper);
                if(childParentMiniAccountList!=null&&childParentMiniAccountList.size()>0){
                    for (MiniAccount childParentMiniAccount : childParentMiniAccountList) {
                        childParentMiniAccount.setAboveParentId(userId);
                        this.updateById(childParentMiniAccount);
                    }
                }
            }
        }

    }

    @Override
    public Integer getAddCustom(String startDate, String endDate) {
        LambdaQueryWrapper<MiniAccount>queryWrapper = new LambdaQueryWrapper<>();
        if(StringUtil.isNotEmpty(startDate)){
            queryWrapper.ge(MiniAccount::getCreateTime,startDate);
        }
        if(StringUtil.isNotEmpty(endDate)){
            queryWrapper.le(MiniAccount::getCreateTime,endDate);
        }
        queryWrapper.eq(MiniAccount::getWhetherAuthorization,1);
        return this.baseMapper.selectCount(queryWrapper);
    }

    @Override
    public void updateAccountData(ApiUpdateAccountDto dto) {
        Long id = dto.getId();
        if(null == id){
            throw  new ServiceException("小程序用户id不能为空！");
        }
        MiniAccount miniAccount = this.baseMapper.selectById(id);

        if(null == miniAccount){
            throw  new ServiceException("小程序用户不存在！");
        }
        miniAccount.setUserName(dto.getUserName());
        miniAccount.setAge(dto.getAge());
        miniAccount.setGender(dto.getGender());
        miniAccount.setSendStatus(ExternalAccountEnum.NOT_ISSUED.getStatus());
        this.baseMapper.updateById(miniAccount);
    }

    @Override
    public PageUtils<BindMiniAccountVo> getBindMiniAccount(BindMiniAccountParam param) {
        if(StringUtils.isEmpty(param.getAccountId())){
            param.setAccountId("");
        }
        IPage<BindMiniAccountVo> page = this.baseMapper.getBindMiniAccount(new Page<BindMiniAccountVo>(param.getCurrent(),param.getSize()), param);
        return new PageUtils<>(page);
    }

    @Override
    public void updateMiniAccountDetail(MiniAccountDetailDto dto) {
        String id = dto.getId();
        if(StringUtils.isNotEmpty(id)){

            MiniAccount miniAccount = this.baseMapper.selectById(id);
            LambdaUpdateWrapper<MiniAccount>updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(MiniAccount::getId,id);
            if(dto.getUserName()!=null){
                if(StringUtils.isNotEmpty(dto.getUserName())){
                    updateWrapper.set(MiniAccount::getUserName,dto.getUserName());
                }else{
                    updateWrapper.set(MiniAccount::getUserName,null);
                }
            }
            if(dto.getAge()!=null){
                updateWrapper.set(MiniAccount::getAge,dto.getAge());
            }else{
                updateWrapper.set(MiniAccount::getAge,null);
            }
            if(dto.getGender()!=null){
                updateWrapper.set(MiniAccount::getGender,dto.getGender());
            }else{
                updateWrapper.set(MiniAccount::getGender,null);
            }
            if(dto.getBirthday()!=null){
                if(StringUtils.isNotEmpty(dto.getBirthday())){
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    updateWrapper.set(MiniAccount::getBirthday,LocalDateTime.parse(dto.getBirthday()+" 00:00:00", formatter));
                }else{
                    updateWrapper.set(MiniAccount::getBirthday,null);
                }
            }
            if(dto.getCard()!=null){
                if(StringUtils.isNotEmpty(dto.getCard())){
                    updateWrapper.set(MiniAccount::getCard,dto.getCard());
                }else{
                    updateWrapper.set(MiniAccount::getCard,null);
                }
            }
            this.baseMapper.update(miniAccount,updateWrapper);
            MiniAccount miniAccountCache = this.getById(miniAccount.getId());
            //缓存用户最新的基础信息
            accountCache(miniAccountCache);
        }else{
            throw new ServiceException("小程序客户id不能为空");
        }

    }

    @Override
    @Transactional
    public Boolean updateMiniAccount(String userId, String accountId) {
        Boolean result = false;
        LambdaQueryWrapper<MiniAccount>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccount::getAccountId,accountId);
        List<MiniAccount> list = this.list(wrapper);
        if(list!=null&&list.size()>0){
            for (MiniAccount miniAccount : list) {
                miniAccount.setAccountId("");
                this.updateById(miniAccount);
            }
        }

        LambdaQueryWrapper<MiniAccount>queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MiniAccount::getUserId,userId);
        List<MiniAccount> miniAccountList = this.list(queryWrapper);
        if(miniAccountList!=null&&miniAccountList.size()>0){
            MiniAccount miniAccount = miniAccountList.get(0);
            miniAccount.setAccountId(accountId);
            result = this.updateById(miniAccount);
        }
        return result;
    }

    @Override
    public void updateMiniAccountLevel() {
        List<MiniAccount> list = this.list();
        if(list!=null&&list.size()>0){
            for (MiniAccount miniAccount : list) {
                //租户id
                String tenantId = miniAccount.getTenantId();
                TenantContextHolder.setTenantId(tenantId);

                //1.先获取会员等级规则
                LambdaQueryWrapper<MemberLevelRuleMessage>wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(MemberLevelRuleMessage::getDeleted,CommonConstants.NUMBER_ZERO);
                MemberLevelRuleMessage memberLevelRuleMessage = memberLevelRuleMessageService.getOne(wrapper);
                if(memberLevelRuleMessage!=null&&!"".equals(memberLevelRuleMessage)){
                    String type = memberLevelRuleMessage.getType();
                    List<String> typeList = Arrays.stream(type.split(",")).collect(Collectors.toList());
                    //获取会员等级顺序
                    Integer sort = 0;
                    if(StringUtils.isNotEmpty(miniAccount.getMemberLevelId())){
                        LambdaQueryWrapper<MemberLevelRule>memberLevelRuleLambdaQueryWrapper = new LambdaQueryWrapper<>();
                        memberLevelRuleLambdaQueryWrapper.eq(MemberLevelRule::getMemberLevelId,miniAccount.getMemberLevelId());
                        List<MemberLevelRule> memberLevelRuleList = memberLevelRuleService.list(memberLevelRuleLambdaQueryWrapper);
                        if(memberLevelRuleList!=null&&memberLevelRuleList.size()>0){
                            MemberLevelRule memberLevelRule = memberLevelRuleList.get(0);
                            if(memberLevelRule.getSort()!=null){
                                sort = memberLevelRule.getSort();
                            }
                        }
                    }
                    if(typeList.contains(MemberLevelRuleTypeEnum.AMOUNT.getStatus())){
                        //获取消费金额
                        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(miniAccount.getUserId());
                        BigDecimal amount = BigDecimal.ZERO;
                        if(miniAccountExtends.getConsumeTotleMoney()!=null){
                            amount = miniAccountExtends.getConsumeTotleMoney();
                        }
                        //1.查看消费总金额是否达到更新等级
                        LambdaQueryWrapper<MemberLevelRule>queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(MemberLevelRule::getTenantId,tenantId);
                        queryWrapper.le(MemberLevelRule::getAmountStart,amount);
                        queryWrapper.orderByDesc(MemberLevelRule::getSort);
                        List<MemberLevelRule> memberLevelRuleList = memberLevelRuleService.list(queryWrapper);
                        if(memberLevelRuleList!=null&&memberLevelRuleList.size()>0){
                            MemberLevelRule memberLevelRule = memberLevelRuleList.get(0);
                            if(memberLevelRule.getSort()>sort){
                                miniAccount.setMemberLevelId(memberLevelRule.getMemberLevelId());
                                this.baseMapper.updateById(miniAccount);
                            }
                        }
                    }

                    if(typeList.contains(MemberLevelRuleTypeEnum.INTEGRAL.getStatus())){
                        //获取积分
                        BigDecimal integral = BigDecimal.ZERO;
                        if(miniAccount.getIntegral()!=null){
                            integral = miniAccount.getIntegral();
                        }
                        //2.查看积分是否达到更新等级
                        LambdaQueryWrapper<MemberLevelRule>queryWrapper2 = new LambdaQueryWrapper<>();
                        queryWrapper2.eq(MemberLevelRule::getTenantId,tenantId);
                        queryWrapper2.le(MemberLevelRule::getIntegralStart,integral);
                        queryWrapper2.orderByDesc(MemberLevelRule::getSort);
                        List<MemberLevelRule> memberLevelRuleList2 = memberLevelRuleService.list(queryWrapper2);
                        if(memberLevelRuleList2!=null&&memberLevelRuleList2.size()>0){
                            MemberLevelRule memberLevelRule = memberLevelRuleList2.get(0);
                            if(memberLevelRule.getSort()>sort){
                                miniAccount.setMemberLevelId(memberLevelRule.getMemberLevelId());
                                this.baseMapper.updateById(miniAccount);
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public void sendBirthDayWxMessage() {
        //获取生日祝福订阅消息
        log.info("开始获取生日祝福订阅消息模板...");
        WxMessageTemplateVo wxMessageTemplateVo = remoteMiniInfoService.getWxMessageTemplateByCode(WxMessageTemplateCodeEnum.BIRTHDAY.getStatus());
        log.info("获取生日祝福订阅消息模板成功：{}",wxMessageTemplateVo);
        if(wxMessageTemplateVo!=null&&StringUtils.isNotEmpty(wxMessageTemplateVo.getTemplateId())
                &&(String.valueOf(wxMessageTemplateVo.getType())).equals(WxMessageTemplateTypeEnum.MINI.getStatus())){
            //获取当天生日的客户信息
            log.info("开始获取当天生日的客户信息...");
            List<AccountSendWxBirthDayMessageVo>list = this.baseMapper.getAccountSendWxBirthDayMessage();
            log.info("获取当天生日的客户信息成功：{}",list);
            if(list!=null&&list.size()>0){
                for (AccountSendWxBirthDayMessageVo accountSendWxBirthDayMessageVo : list) {
                    log.info("发送生日祝福的客户：{}",accountSendWxBirthDayMessageVo);
                    String userId = accountSendWxBirthDayMessageVo.getUserId();
                    //获取用户openid
                    MiniAccountOauths miniAccountOauths = miniAccountOauthsService.getByUserId(OauthTypeEnum.WX_MINI.getType(), userId);
                    log.info("用户miniAccountOauths:{}",miniAccountOauths);
                    if(miniAccountOauths!=null&&StringUtils.isNotEmpty(miniAccountOauths.getOpenId())){
                        TenantContextHolder.setTenantId(accountSendWxBirthDayMessageVo.getTenantId());
                        WxSendMessageDto wxSendMessageDto = new WxSendMessageDto();
                        //获取小程序配置记录
                        MiniInfoVo miniInfoVo = remoteMiniInfoService.getMiniInfoVoByTenantId(accountSendWxBirthDayMessageVo.getTenantId());
                        log.info("小程序配置miniInfoVo:{}",miniInfoVo);
                        wxSendMessageDto.setAppId(miniInfoVo.getAppId());
                        wxSendMessageDto.setAppSecret(miniInfoVo.getAppSecret());
                        wxSendMessageDto.setTenantId(accountSendWxBirthDayMessageVo.getTenantId());
                        //获取主店铺shopId
                        ShopsPartner shopsPartnerMain = remoteShopsService.getShopsPartnerMain();
                        log.info("主店铺shopsPartnerMain:{}",shopsPartnerMain);
                        wxSendMessageDto.setShopId(shopsPartnerMain.getShopId());
                        wxSendMessageDto.setUseType(1);
                        wxSendMessageDto.setMessageType(3);
                        wxSendMessageDto.setTitle(wxMessageTemplateVo.getName());
                        wxSendMessageDto.setTemplateId(wxMessageTemplateVo.getTemplateId());
                        wxSendMessageDto.setMessage(wxMessageTemplateVo.getName());
                        wxSendMessageDto.setPage("/pages/index/index");
                        List<String>openIds = new ArrayList<>();
                        openIds.add(miniAccountOauths.getOpenId());
                        wxSendMessageDto.setOpenIds(openIds);
                        List<WxMessageTemplateDetailVo> wxMessageTemplateDetailVoList = wxMessageTemplateVo.getList();
                        List<Map<String,String>>mapList = new ArrayList<>();
                        if(wxMessageTemplateDetailVoList!=null&&wxMessageTemplateDetailVoList.size()>0){
                            for (WxMessageTemplateDetailVo wxMessageTemplateDetailVo : wxMessageTemplateDetailVoList) {
                                Map<String,String> map = new HashMap<>();
                                if(wxMessageTemplateDetailVo.getName().equals("会员名称")){
                                    if(StringUtil.isNotEmpty(accountSendWxBirthDayMessageVo.getUserName())){
                                        map.put(wxMessageTemplateDetailVo.getKeyData(),accountSendWxBirthDayMessageVo.getUserName());
                                    }else{
                                        map.put(wxMessageTemplateDetailVo.getKeyData(),accountSendWxBirthDayMessageVo.getNikeName());
                                    }
                                }
                                if(wxMessageTemplateDetailVo.getName().equals("温馨提示")){
                                    map.put(wxMessageTemplateDetailVo.getKeyData(),"今天是您的生日。");
                                }
                                if(wxMessageTemplateDetailVo.getName().equals("生日祝福")){
                                    map.put(wxMessageTemplateDetailVo.getKeyData(),"祝您生日快乐！");
                                }
                                mapList.add(map);
                            }
                        }
                        wxSendMessageDto.setMapList(mapList);
                        log.info("订阅消息wxSendMessageDto:{}",wxSendMessageDto);
                        sender.sendWxMessage(wxSendMessageDto);
                    }
                }
            }
        }
    }

    @Override
    public void sendActivityWxMessage(SendActivityWxMessageDto dto) {

        String tenantId = TenantContextHolder.getTenantId();
        String shopId = ShopContextHolder.getShopId();
        //用户标签id
        List<String> tagIds = dto.getTagIds();
        //用户userId
        List<String> userIds = new ArrayList<>();
        if(tagIds!=null&&tagIds.size()>0){
            //选择用户标签发送
            userIds = miniAccountTagGroupService.getUserIdByTagIds(tagIds);
        }else{
            //选择用户id发送
            if(dto.getUserIds()!=null&&dto.getUserIds().size()>0){
                userIds = dto.getUserIds();
            }
        }
        //都没有用户信息则取给所有用户发送消息
        if(userIds.size()==0){
            userIds = this.baseMapper.getAllShopUserId();
        }
        if(userIds!=null&&userIds.size()>0){
            log.info("开始获取活动通知订阅消息模板...");
            WxMessageTemplateVo wxMessageTemplateVo = remoteMiniInfoService.getWxMessageTemplateByCode(WxMessageTemplateCodeEnum.ACTIVITY.getStatus());
            log.info("获取活动通知订阅消息模板成功：{}",wxMessageTemplateVo);
            if(wxMessageTemplateVo!=null&&StringUtils.isNotEmpty(wxMessageTemplateVo.getTemplateId())
                    &&(String.valueOf(wxMessageTemplateVo.getType())).equals(WxMessageTemplateTypeEnum.MINI.getStatus())){
                List<String>openIds = new ArrayList<>();
                for (String userId : userIds) {
                    //获取用户openid
                    MiniAccountExtends ext = miniAccountExtendsService.findByShopUserId(userId);
                    if(ext!=null&&StringUtils.isNotEmpty(ext.getUserId())){
                        MiniAccountOauths miniAccountOauths = miniAccountOauthsService.getByUserId(OauthTypeEnum.WX_MINI.getType(), ext.getUserId());
                        log.info("用户miniAccountOauths:{}",miniAccountOauths);
                        if(miniAccountOauths!=null&&StringUtils.isNotEmpty(miniAccountOauths.getOpenId())){
                            openIds.add(miniAccountOauths.getOpenId());
                        }
                    }
                }
                if(openIds!=null&&openIds.size()>0){
                    WxSendMessageDto wxSendMessageDto = new WxSendMessageDto();
                    //获取小程序配置记录
                    MiniInfoVo miniInfoVo = remoteMiniInfoService.getMiniInfoVoByTenantId(tenantId);
                    log.info("小程序配置miniInfoVo:{}",miniInfoVo);
                    wxSendMessageDto.setAppId(miniInfoVo.getAppId());
                    wxSendMessageDto.setAppSecret(miniInfoVo.getAppSecret());
                    wxSendMessageDto.setTenantId(tenantId);
                    wxSendMessageDto.setShopId(shopId);
                    wxSendMessageDto.setUseType(1);
                    wxSendMessageDto.setMessageType(4);
                    wxSendMessageDto.setTitle(wxMessageTemplateVo.getName());
                    wxSendMessageDto.setTemplateId(wxMessageTemplateVo.getTemplateId());
                    wxSendMessageDto.setMessage(wxMessageTemplateVo.getName());
                    wxSendMessageDto.setPage("/pages/index/index");
                    if(StringUtils.isNotEmpty(dto.getProductId())){
                        ProductVo product = remoteGoodsService.findProductById(Long.valueOf(dto.getProductId()));
                        if(product!=null){
                            wxSendMessageDto.setPage("/subcontract/pages/detail/detail?id="+product.getId()+"&img="+product.getPic());
                        }
                    }
                    wxSendMessageDto.setOpenIds(openIds);
                    List<WxMessageTemplateDetailVo> wxMessageTemplateDetailVoList = wxMessageTemplateVo.getList();
                    List<Map<String,String>>mapList = new ArrayList<>();
                    if(wxMessageTemplateDetailVoList!=null&&wxMessageTemplateDetailVoList.size()>0){
                        for (WxMessageTemplateDetailVo wxMessageTemplateDetailVo : wxMessageTemplateDetailVoList) {
                            Map<String,String> map = new HashMap<>();
                            if(wxMessageTemplateDetailVo.getName().equals("活动名称")){
                                map.put(wxMessageTemplateDetailVo.getKeyData(),dto.getActivityName());
                            }
                            if(wxMessageTemplateDetailVo.getName().equals("活动时间")){
                                map.put(wxMessageTemplateDetailVo.getKeyData(),dto.getActivityDate());
                            }
                            if(wxMessageTemplateDetailVo.getName().equals("备注")){
                                map.put(wxMessageTemplateDetailVo.getKeyData(),dto.getContent());
                            }
                            mapList.add(map);
                        }
                    }
                    wxSendMessageDto.setMapList(mapList);
                    log.info("订阅消息wxSendMessageDto:{}",wxSendMessageDto);
                    sender.sendWxMessage(wxSendMessageDto);
                }
            }
        }
    }

    @Override
    public void updateMpOpenId(UpdateMpOpenIdDto dto) {
        String shopUserId = dto.getShopUserId();
        if(StringUtils.isEmpty(shopUserId)){
            throw new ServiceException("同步失败，用户店铺id不能为空！");
        }
        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(shopUserId);
        if(miniAccountExtends!=null&&StringUtils.isNotEmpty(miniAccountExtends.getUserId())){
            MiniAccountOauths miniAccountOauths = miniAccountOauthsService.getByUserId(OauthTypeEnum.WX_MINI.getType(), miniAccountExtends.getUserId());
            if(miniAccountOauths!=null&&miniAccountOauths.getUnionId()!=null){
                String unionId = miniAccountOauths.getUnionId();
                MiniInfoVo miniInfoVo = remoteMiniInfoService.getShopConfigMini();
                if(miniInfoVo!=null&&StringUtils.isNotEmpty(miniInfoVo.getAppMpId())
                        &&StringUtils.isNotEmpty(miniInfoVo.getAppMpSecret())){
                    WxMpDefaultConfigImpl wxMpDefaultConfig = new WxMpDefaultConfigImpl();
                    wxMpDefaultConfig.setAppId(miniInfoVo.getAppMpId());
                    wxMpDefaultConfig.setSecret(miniInfoVo.getAppMpSecret());
                    WxMpService wxMpService = new WxMpServiceImpl();
                    wxMpService.setWxMpConfigStorage(wxMpDefaultConfig);
                    try {
                        //获取公众号所有openid
                        WxMpUserList wxMpUserList = wxMpService.getUserService().userList(null);
                        if(wxMpUserList!=null&&wxMpUserList.getOpenids()!=null&&wxMpUserList.getOpenids().size()>0){
                            WxMpUserQuery wxMpUserQuery = new WxMpUserQuery();
                            for (String openid : wxMpUserList.getOpenids()) {
                                wxMpUserQuery.add(openid);
                            }
                            //根据公众号openId查询公众号用户具体信息
                            List<WxMpUser> wxMpUsers = wxMpService.getUserService().userInfoList(wxMpUserQuery);
                            if(wxMpUsers!=null&&wxMpUsers.size()>0){
                                for (WxMpUser wxMpUser : wxMpUsers) {
                                    if(wxMpUser!=null&&StringUtils.isNotEmpty(wxMpUser.getUnionId())
                                            &&wxMpUser.getUnionId().equals(unionId)){
                                        miniAccountOauths.setMpOpenId(wxMpUser.getOpenId());
                                        miniAccountOauthsService.updateById(miniAccountOauths);
                                    }
                                }
                            }
                        }
                    } catch (WxErrorException e) {
                        throw new ServiceException("同步失败，"+e.getMessage());
                    }
                }else{
                    throw new ServiceException("同步失败，请先填写公众号appId和secret！");
                }

            }else{
                throw new ServiceException("同步失败，平台未绑定微信开放平台！");
            }

        }else{
            throw new ServiceException("同步失败，用户不存在！");
        }
    }

    @Override
    public IPage<MiniAccountDetailVo> searchMiniAccountDetail(MiniAccountDetailParam param) {
        IPage<MiniAccountDetailVo> page = this.baseMapper.searchMiniAccountDetail(new Page<>(param.getCurrent(),param.getSize()),param);
        List<MiniAccountDetailVo> records = page.getRecords();
        if (!records.isEmpty()){
            for (MiniAccountDetailVo data : records) {
                String userId = data.getUserId();
                MemberLevelRelationParam memberLevelRelationParam = new MemberLevelRelationParam();
                memberLevelRelationParam.setUserId(userId);
                StringBuilder showMemberInfo  = new StringBuilder();
                List<MemberLevelRelation> list = memberLevelRelationService.getMemberLevelRelationNotMemberLevelId(memberLevelRelationParam);
                if (CollectionUtil.isNotEmpty(list)){
                    for (MemberLevelRelation memberLevelRelation : list) {
                        Long memberTypeId = memberLevelRelation.getMemberTypeId();
                        String memberLevelId = memberLevelRelation.getMemberLevelId();

                        MemberType memberType = memberTypeService.getById(memberTypeId);
                        MemberLevel memberLevel = memberLevelService.getById(memberLevelId);
                        showMemberInfo.append(memberType.getName()).append("：").append(memberLevel.getMemberLevel()).append(",");
                    }
                    data.setMemberLevel(showMemberInfo.substring(0,showMemberInfo.length()-1));
                    if(StringUtils.isNotEmpty(data.getShopUserId())){
                        LambdaQueryWrapper<MiniAccountAddress>wrapper = new LambdaQueryWrapper<>();
                        wrapper.eq(MiniAccountAddress::getUserId,data.getShopUserId());
                        List<MiniAccountAddress> miniAccountAddressList = miniAccountAddressService.list(wrapper);
                        if(miniAccountAddressList!=null&&miniAccountAddressList.size()>0){
                            data.setMiniAccountAddressList(miniAccountAddressList);
                            MiniAccountAddress miniAccountAddress = miniAccountAddressList.get(0);
                            String address = miniAccountAddress.getProvince()+"-"+
                                    miniAccountAddress.getCity()+"-"
                                    +miniAccountAddress.getCounty()+"-"
                                    +miniAccountAddress.getDetailInfo();
                            data.setAddress(address);
                        }
                    }
                }
            }
        }
        return page;
    }

    @Override
    public IPage<MiniAccountRoyaltyVo> searchMiniAccountRoyalty(MiniAccountRoyaltyParam param) {
        IPage<MiniAccountRoyaltyVo> page = this.baseMapper.searchMiniAccountRoyalty(new Page<>(param.getCurrent(),param.getSize()),param);
        return page;
    }

    @Override
    public Boolean getMiniAccountAgainPermission() {
        Boolean b  = false;
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        if (ObjectUtil.isNull(curUserDto)) {
            throw new ServiceException(SystemCode.UNAUTHORIZED);
        }
        log.info("当前用户信息:" + curUserDto.toString());
        //查询用户持有的积分、收货地址
        AccountInfoDto accountInfoDto = accountInfo(curUserDto.getUserId(), Arrays.asList(1,2,
                3, 5));
        //会员id
        String memberId = accountInfoDto.getMiniAccountunt().getMemberLevelId();

        LambdaQueryWrapper<MemberLevelRule>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberLevelRule::getDeleted,CommonConstants.NUMBER_ZERO);
        wrapper.eq(MemberLevelRule::getMemberLevelId,memberId);
        List<MemberLevelRule> list = memberLevelRuleService.list(wrapper);
        if(list!=null&&list.size()==1){
            MemberLevelRule memberLevelRule = list.get(0);
            Integer againFlag = memberLevelRule.getAgainFlag();
            if(againFlag == CommonConstants.NUMBER_ONE){
                b = true;
            }
        }
        return b;
    }

    @Override
    public Boolean getMiniAccountAgainPermissionByMemberId(String memberId) {
        Boolean b  = false;
        LambdaQueryWrapper<MemberLevelRule>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberLevelRule::getDeleted,CommonConstants.NUMBER_ZERO);
        wrapper.eq(MemberLevelRule::getMemberLevelId,memberId);
        List<MemberLevelRule> list = memberLevelRuleService.list(wrapper);
        if(list!=null&&list.size()==1){
            MemberLevelRule memberLevelRule = list.get(0);
            Integer againFlag = memberLevelRule.getAgainFlag();
            if(againFlag == CommonConstants.NUMBER_ONE){
                b = true;
            }
        }
        return b;
    }

    @Override
    public IPage<ChooseAccountVo> getChooseAccount(ChooseAccountParam param) {
        IPage<ChooseAccountVo> page = this.baseMapper.getChooseAccount(new Page<>(param.getCurrent(), param.getSize()), param);
        for (ChooseAccountVo chooseAccountVo : page.getRecords()) {
            String userId = chooseAccountVo.getUserId();
            MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(userId);
            LambdaQueryWrapper<MiniAccountTagGroup>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MiniAccountTagGroup::getDeleted,CommonConstants.NUMBER_ZERO);
            wrapper.eq(MiniAccountTagGroup::getUserId,miniAccountExtends.getShopUserId());
            List<MiniAccountTagGroup> list = miniAccountTagGroupService.list(wrapper);
            String tagName = "";
            for (MiniAccountTagGroup miniAccountTagGroup : list) {
                Long tagId = miniAccountTagGroup.getTagId();
                MiniAccountTag miniAccountTag = miniAccountTagService.getById(tagId);
                if(tagName.length()>0){
                    tagName += "，";
                }
                tagName += miniAccountTag.getTagName();
            }
            chooseAccountVo.setTagName(tagName);
        }
        return page;
    }

    @Override
    public ChooseAccountVo getChooseAccountByUserId(String userId) {
        ChooseAccountVo chooseAccountVo = this.baseMapper.getChooseAccountByUserId(userId);
        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(userId);
        LambdaQueryWrapper<MiniAccountTagGroup>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountTagGroup::getDeleted,CommonConstants.NUMBER_ZERO);
        wrapper.eq(MiniAccountTagGroup::getUserId,miniAccountExtends.getShopUserId());
        List<MiniAccountTagGroup> list = miniAccountTagGroupService.list(wrapper);
        String tagName = "";
        for (MiniAccountTagGroup miniAccountTagGroup : list) {
            Long tagId = miniAccountTagGroup.getTagId();
            MiniAccountTag miniAccountTag = miniAccountTagService.getById(tagId);
            if(tagName.length()>0){
                tagName += "，";
            }
            tagName += miniAccountTag.getTagName();
        }
        chooseAccountVo.setTagName(tagName);
        return chooseAccountVo;
    }

    @Override
    public String sendCode(SendCodeDto sendCodeDto) {
        if (sendCodeDto.getPhone() == null || !ReUtil.isMatch(RegexConstants.REGEX_MOBILE_EXACT, sendCodeDto.getPhone())) {
            throw new ServiceException("手机号错误", SystemCode.DATA_NOT_EXIST.getCode());
        }
        if (!AuthCodeEnum.isExistValue(sendCodeDto.getType())) {
            throw new ServiceException("非法校验", SystemCode.FAILURE.getCode());
        }
        AccountRedis accountRedis = new AccountRedis();
        String redisKey = RedisConstant.PHONE_KEY.concat(sendCodeDto.getType().toString()).concat(":").concat(sendCodeDto.getPhone());
        String code = accountRedis.get(redisKey);
        if (StrUtil.isEmpty(code)) {
            code = RandomUtil.randomNumbers(CommonConstants.NUMBER_SIX);
        }
        //验证码有效期未过则重新发送
//        SendSmsFeignDto sendSmsFeignDto = new SendSmsFeignDto();
//        sendSmsFeignDto.setSmsSendTime(Calendar.getInstance().getTimeInMillis());
//        sendSmsFeignDto.setSmsSendMobiles(sendCodeDto.getPhone());
//        sendSmsFeignDto.setSmsSendParam(code);
//        sendSmsFeignDto.setSmsSendZone(SmsConstant.ZONE);
//        sendSmsFeignDto.setSmsType(SmsConstant.SMS_TYPE_ALIYUN);
//        sendSmsFeignDto.setSmsSendZone(SmsConstant.ZONE);
//        sendSmsFeignDto.setSignId(SmsConstant.SIGN_ID);
//        sendSmsFeignDto.setTemplateId(SmsConstant.TEMPLATE_ID);
//        sendSmsFeignDto.setProviderId(SmsConstant.PROVIDER_ID);
//        sendSmsFeignDto.setUserId(SmsConstant.USER_ID);
//        sendSmsFeignDto.setSysTemplateCode(SysSmsTemplateCodeEnum.VERIFYCODE.getCode());
//        Result order = remoteSmsSendService.createOrder(sendSmsFeignDto);
//        if ((CommonConstants.STATUS_OK == order.getCode()) == Boolean.FALSE) {
//            throw new ServiceException("短信发送失败,".concat(order.getMsg()));
//        }
        accountRedis.setNxPx(redisKey, code, TimeConstants.FIVE_MINUTES);
        return code;
    }

    /**
     * 修改支付密码
     * @param dto
     */
    @Override
    public void updateAccountPayPwd(UpdateAccountPayPwdDto dto) {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        MiniAccount miniAccount = this.getByShopUserId(curUser.getUserId());
        if (miniAccount == null) {
            throw new ServiceException("数据错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        String phone = miniAccount.getPhone();
        //判断验证码是否存在
        if(StringUtils.isEmpty(dto.getSmsCode())){
            throw new ServiceException("手机验证码不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //验证验证码正确性
        AccountRedis accountRedis = new AccountRedis();
        String redisKey = RedisConstant.PHONE_KEY.concat(dto.getType().toString()).concat(":").concat(phone);
        String code = accountRedis.get(redisKey);
        if(code == null || !code.equals(dto.getSmsCode())){
            throw new ServiceException("验证码不正确，请重新输入！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        accountRedis.del(redisKey);
        //验证密码不能为空
        if(StringUtils.isEmpty(dto.getPayPwd())){
            throw new ServiceException("支付密码不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        if(StringUtils.isEmpty(dto.getPayPwdAgain())){
            throw new ServiceException("确认密码不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //密码和确认密码要一致
        if(!dto.getPayPwd().equals(dto.getPayPwdAgain())){
            throw new ServiceException("确认密码不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }

        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(miniAccount.getUserId());

        String salt = RandomUtil.randomString(6);
        miniAccountExtends.setPaySalt(salt);
        miniAccountExtends.setPayPwd(SecureUtil.md5(dto.getPayPwd().concat(salt)));
        miniAccountExtendsService.updateById(miniAccountExtends);

    }

    /**
     * 上传微信付款码/支付宝付款码
     * @param dto
     */
    @Override
    public void updateAccountExtends(UpdateAccountExtendsDto dto) {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        MiniAccount miniAccount = this.getByShopUserId(curUser.getUserId());
        if (miniAccount == null) {
            throw new ServiceException("数据错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(miniAccount.getUserId());
        BeanUtils.copyProperties(dto,miniAccountExtends);
        miniAccountExtendsService.updateById(miniAccountExtends);
    }

    /**
     * 获取小程序用户身份证认证信息
     * @return
     */
    @Override
    public ApiMiniAccountCardVo getApiMiniAccountCard() {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        MiniAccount miniAccount = this.getByShopUserId(curUser.getUserId());
        if (miniAccount == null) {
            throw new ServiceException("数据错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        String userId = miniAccount.getUserId();
        ApiMiniAccountCardVo miniAccountCardVo = this.baseMapper.getApiMiniAccountCard(userId);
        Long auditPlatformUserId = miniAccountCardVo.getAuditPlatformUserId();
        AccountInfo accountInfo = remoteMiniInfoService.getAccountInfo(auditPlatformUserId + "");
        if(accountInfo!=null){
            miniAccountCardVo.setAuditPlatformUserName(accountInfo.getNikeName());
        }
        return miniAccountCardVo;
    }

    /**
     * 小程序认证身份证实名
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void authCard(ApiMiniAccountCardDto dto) {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        MiniAccount miniAccount = this.getByShopUserId(curUser.getUserId());
        if (miniAccount == null) {
            throw new ServiceException("数据错误", SystemCode.DATA_NOT_EXIST_CODE);
        }

        if(StringUtils.isEmpty(dto.getCard())){
            throw new ServiceException("身份证号不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }

        if(StringUtils.isEmpty(dto.getUserName())){
            throw new ServiceException("姓名不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }

        if(StringUtils.isEmpty(dto.getIdCardBackUrl())){
            throw new ServiceException("身份证背面不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        if(StringUtils.isEmpty(dto.getIdCardFaceUrl())){
            throw new ServiceException("身份证正面不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }

        miniAccount.setUserName(dto.getUserName());
        miniAccount.setCard(dto.getCard());
        this.updateById(miniAccount);

        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(miniAccount.getUserId());
        miniAccountExtends.setIdCardBackUrl(dto.getIdCardBackUrl());
        miniAccountExtends.setIdCardFaceUrl(dto.getIdCardFaceUrl());
        miniAccountExtends.setCardAuthorization(CardAuthorizationEnum.IN_REVIEW.getStatus());
        miniAccountExtends.setCardAuditReason("");
        miniAccountExtendsService.updateById(miniAccountExtends);

    }

    /**
     * 后台审核小程序用户身份证
     * @param dto
     * @return
     */
    @Override
    public String auditCard(AuditMiniAccountCardDto dto) {

        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (curUser == null) {
            throw new ServiceException("数据错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        String id = dto.getId();
        if(StringUtils.isEmpty(id)){
            throw new ServiceException("id不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        Integer cardAuthorization = dto.getCardAuthorization();
        if(cardAuthorization == null || cardAuthorization.equals("")){
            throw new ServiceException("审核状态不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }

        MiniAccount miniAccount = this.getById(id);
        if(miniAccount == null || miniAccount.equals("")){
            throw new ServiceException("小程序用户不存在！", SystemCode.DATA_NOT_EXIST_CODE);
        }



        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(miniAccount.getUserId());
        if(miniAccountExtends == null || miniAccountExtends.equals("")){
            throw new ServiceException("小程序用户扩展信息不存在！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        if(miniAccountExtends.getCardAuthorization() != CardAuthorizationEnum.IN_REVIEW.getStatus()){
            throw new ServiceException("只能审核待审核小程序用户！", SystemCode.DATA_NOT_EXIST_CODE);
        }


        String result = "";
        if(cardAuthorization == CardAuthorizationEnum.AUDIT_NO.getStatus()){//审核不通过
            if(StringUtils.isEmpty(dto.getCardAuditReason())){
                throw new ServiceException("审核不通过需要填写原因！", SystemCode.DATA_NOT_EXIST_CODE);
            }
            result = "审核不通过";
        }else if(cardAuthorization == CardAuthorizationEnum.YES.getStatus()){//审核通过

            result = "审核通过";
        }else{
            throw new ServiceException("审核状态错误！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        miniAccountExtends.setCardAuditReason(dto.getCardAuditReason());
        miniAccountExtends.setCardAuthorization(cardAuthorization);
        miniAccountExtends.setAuditTime(LocalDateTime.now());
        miniAccountExtends.setAuditPlatformUserId(Long.valueOf(curUser.getUserId()));
        miniAccountExtendsService.updateById(miniAccountExtends);
        return result;
    }

    /**
     * 驳回小程序用户身份证认证
     * @param dto
     * @return
     */
    @Override
    public String rejectCard(AuditMiniAccountCardDto dto) {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (curUser == null) {
            throw new ServiceException("数据错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        String id = dto.getId();
        if(StringUtils.isEmpty(id)){
            throw new ServiceException("id不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }

        MiniAccount miniAccount = this.getById(id);
        if(miniAccount == null){
            throw new ServiceException("小程序用户不存在！", SystemCode.DATA_NOT_EXIST_CODE);
        }

        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(miniAccount.getUserId());
        if(miniAccountExtends == null){
            throw new ServiceException("小程序用户扩展信息不存在！", SystemCode.DATA_NOT_EXIST_CODE);
        }

        if(miniAccountExtends.getCardAuthorization() != CardAuthorizationEnum.YES.getStatus()){
            throw new ServiceException("只能驳回已实名的用户！", SystemCode.DATA_NOT_EXIST_CODE);
        }

        // 设置为驳回状态
        miniAccountExtends.setCardAuditReason("驳回");
        miniAccountExtends.setCardAuthorization(CardAuthorizationEnum.NO.getStatus());
        miniAccountExtends.setAuditTime(LocalDateTime.now());
        miniAccountExtends.setAuditPlatformUserId(Long.valueOf(curUser.getUserId()));
        boolean update = miniAccountExtendsService.updateById(miniAccountExtends);
        return update ?"驳回成功" : "驳回失败";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map updateMiniAccountExtendsStatus() {

        Map data = new HashMap();
        data.put("result","0");
        data.put("message","账户正常！");

        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (curUser == null) {
            throw new ServiceException("数据错误", SystemCode.DATA_NOT_EXIST_CODE);
        }

        MemberActiveSettingVo memberActiveSetting = memberActiveSettingService.getMemberActiveSetting();
        if(memberActiveSetting!=null
                &&memberActiveSetting.getStatus() == MemberActiveSettingStatusEnum.YES.getStatus()
                &&memberActiveSetting.getActiveDays()!=null){

            //剩余天数
            Integer remainingDays = memberActiveSetting.getRemainingDays();
            MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(curUser.getUserId());
            if(Objects.equals(miniAccountExtends.getStatus(), MiniAccountExtendsStatusEnum.NO.getStatus())){
                data.put("result","1");
                data.put("message","您的账户已被冻结，请尽快激活！");
            }else{
                MiniAccount miniAccount = this.getByShopUserId(curUser.getUserId());


                MemberType memberType = memberTypeService.getDefaultMemberType();


                MemberLevelRelationParam param = new MemberLevelRelationParam();
                param.setMemberTypeId(Long.valueOf(memberType.getId()));
                param.setUserId(miniAccountExtends.getUserId());
                String memberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(param);
                if(StringUtils.isNotEmpty(memberLevelId)){
                    MemberLevel memberLevel = memberLevelService.getById(memberLevelId);
                    if(memberLevel.getMemberFlag() == MemberFlagEnum.YES.getStatus()){
                        if(miniAccountExtends.getLastDealTime()!=null){
                            LocalDateTime lastDealTime = miniAccountExtends.getLastDealTime();

                            LambdaQueryWrapper<MemberLevelRelation>wrapper = new LambdaQueryWrapper<>();
                            wrapper.eq(MemberLevelRelation::getUserId,miniAccountExtends.getUserId());
                            wrapper.isNotNull(MemberLevelRelation::getUpLevelTime);
                            wrapper.orderByDesc(MemberLevelRelation::getUpLevelTime);
                            if(lastDealTime == null){
                                lastDealTime = miniAccount.getRegisterTime();
                            }



                            if(lastDealTime!=null){
                                LocalDateTime now = LocalDateTime.now();
                                LocalDateTime endLocalDateTime = lastDealTime.plusDays(memberActiveSetting.getActiveDays());
                                long day = Duration.between(now, endLocalDateTime).toDays();
                                if(day<0){
                                    miniAccountExtends.setStatus(MiniAccountExtendsStatusEnum.NO.getStatus());
                                    miniAccountExtendsService.updateById(miniAccountExtends);
                                    data.put("result","1");
                                    data.put("message","您的账户已被冻结，请尽快激活！");
                                }else if(day == 0){
                                    data.put("result","2");
                                    data.put("message","您的账户今天到期，请尽快下单！");
                                }else {
                                    if(day<=remainingDays){
                                        data.put("result","3");
                                        data.put("message","您的账户还有"+day+"天到期，请尽快下单！");
                                    }
                                }
                            }else{
                                miniAccountExtends.setStatus(MiniAccountExtendsStatusEnum.NO.getStatus());
                                miniAccountExtendsService.updateById(miniAccountExtends);
                                data.put("result","1");
                                data.put("message","您的账户已被冻结，请尽快激活！");
                            }
                        }
                    }
                }
            }
        }
        return data;
    }

    @Override
    public List<ActiveProductVo> getActiveProduct() {

        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (curUser == null) {
            throw new ServiceException("数据错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        MiniAccount miniAccount = this.getByShopUserId(curUser.getUserId());
        if (miniAccount == null) {
            throw new ServiceException("数据错误", SystemCode.DATA_NOT_EXIST_CODE);
        }



        List<ActiveProductVo> productList = new ArrayList<>();
        LambdaQueryWrapper<MemberActiveProduct>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberActiveProduct::getDeleted,CommonConstants.NUMBER_ZERO);
        List<MemberActiveProduct> list = memberActiveProductService.list(wrapper);

        List<MemberTypeVo>memberTypeList = memberTypeService.getMemberTypeList(miniAccount.getUserId());

        String memberTypeId = "";

        if(memberTypeList!=null&&list.size()>0){

            if(list.size() == 1){
                MemberTypeVo memberTypeVo = memberTypeList.get(0);
                memberTypeId = memberTypeVo.getId();
            }else{
                List<MemberTypeVo> newList = memberTypeList.stream().filter(e -> e.getDefaultType()!=null&&e.getDefaultType() == DefaultTypeEnum.YES.getStatus())
                        .collect(Collectors.toList());
                if(newList!=null&&newList.size()>0){
                    MemberTypeVo memberTypeVo = newList.get(0);
                    memberTypeId = memberTypeVo.getId();
                }else{
                    MemberTypeVo memberTypeVo = memberTypeList.get(0);
                    memberTypeId = memberTypeVo.getId();
                }
            }
        }
        if(list!=null&&list.size()>0&&StringUtils.isNotEmpty(memberTypeId)){

            for (MemberActiveProduct memberActiveProduct : list) {
                ActiveProductVo activeProductVo = new ActiveProductVo();
                activeProductVo.setNumber(memberActiveProduct.getProductQuantity());
                activeProductVo.setSkuId(memberActiveProduct.getProductSkuId());
                activeProductVo.setPriceType(PriceTypeEnum.MEMBER.getStatus());
                activeProductVo.setMemberTypeId(Long.valueOf(memberTypeId));
                productList.add(activeProductVo);
            }
        }

        return productList;
    }

    @Override
    public MiniAccount vailUserMessage(VailUserMessageDto dto) {

        if(StringUtils.isEmpty(dto.getPhone())){
            throw new ServiceException("手机号不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        String phone = dto.getPhone();
        //判断验证码是否存在
        if(StringUtils.isEmpty(dto.getSmsCode())){
            throw new ServiceException("手机验证码不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //验证验证码正确性
        AccountRedis accountRedis = new AccountRedis();
        String redisKey = RedisConstant.PHONE_KEY.concat(dto.getType().toString()).concat(":").concat(phone);
        String code = accountRedis.get(redisKey);
        if(code == null || !code.equals(dto.getSmsCode())){
            throw new ServiceException("验证码不正确，请重新输入！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        LambdaQueryWrapper<MiniAccount>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccount::getPhone,phone);
        List<MiniAccount> list = this.list(wrapper);
        String userId = "";
        if(list!=null&&list.size()>0){
            MiniAccount miniAccount = list.get(0);
            MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(miniAccount.getUserId());
            Integer isBlacklist = miniAccountExtends.getIsBlacklist();
            if(isBlacklist == 1){
                throw new ServiceException("用户不存在");
            }
            return miniAccount;
        }else{
            throw new ServiceException("用户不存在");
        }

    }

    /**
     * 小程序根据手机号码擦好像用户信息
     * @param param
     * @return
     */
    @Override
    public ApiMiniAccountVo getApiMiniAccount(ApiMiniAccountParam param) {

        if(StringUtils.isEmpty(param.getPhone())){
            throw new ServiceException("手机号不能为空！");
        }

        LambdaQueryWrapper<MiniAccount>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccount::getPhone,param.getPhone());

        List<MiniAccount> list = this.list(wrapper);

        if(list!=null&&list.size()>0){

            if(list.size()>1){
                throw new ServiceException("用户重复了，请联系管理员处理！");
            }else{
                MiniAccount miniAccount = list.get(0);
                ApiMiniAccountVo apiMiniAccountVo = new ApiMiniAccountVo();
                apiMiniAccountVo.setNikeName(miniAccount.getNikeName());
                apiMiniAccountVo.setUserId(miniAccount.getUserId());
                return apiMiniAccountVo;
            }
        }else{
            throw new ServiceException("该用户不存在，请检查手机号是否正确！");
        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean subtractMiniAccountBalance(MiniAccountBalanceDto miniAccountBalanceDto) {
        boolean result = false;
        BigDecimal payGolden = miniAccountBalanceDto.getPayGolden();
        BigDecimal payCommission = miniAccountBalanceDto.getPayCommission();
        String userId = miniAccountBalanceDto.getUserId();
        String orderId = miniAccountBalanceDto.getOrderId();
        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(userId);
        if(payGolden.add(payCommission).compareTo(BigDecimal.ZERO)>0){
            MiniAccount account = this.getByShopUserId(miniAccountExtends.getShopUserId());
            //扣减金豆
            if(payGolden.compareTo(BigDecimal.ZERO)>0){
                MiniAccountGolden miniAccountGolden = new MiniAccountGolden();

                miniAccountGolden.setCommissionType(CommissionTypeEnum.ORDER_COMMISSION.getType());
                miniAccountGolden.setAmount(payGolden.negate());
                miniAccountGolden.setUserId(miniAccountExtends.getShopUserId());
                miniAccountGolden.setOrderType(GoldenOrderTypeEnum.BUY_ORDER.getType());
                miniAccountGolden.setOrderId(Long.valueOf(orderId));
                miniAccountGolden.setRemark(CommissionTypeEnum.ORDER_COMMISSION.getDesc());
                miniAccountGolden.setSource(CommonConstants.NUMBER_ZERO);


                BigDecimal usedGolden = account.getUsedGolden() == null ? BigDecimal.ZERO :account.getUsedGolden();
                BigDecimal currentGolden = account.getCurrentGolden() == null ? BigDecimal.ZERO :account.getCurrentGolden();

                account.setUsedGolden(usedGolden.add(payGolden));
                account.setCurrentGolden(currentGolden.subtract(payGolden));


                miniAccountGolden.setLastGolden(currentGolden);
                miniAccountGolden.setTotalGolden(account.getCurrentGolden());

                miniAccountGoldenService.save(miniAccountGolden);
            }
            //扣减佣金
            if(payCommission.compareTo(BigDecimal.ZERO)>0){
                MiniAccountCommission miniAccountCommission = new MiniAccountCommission();
                miniAccountCommission.setCommissionType(CommissionTypeEnum.ORDER_COMMISSION.getType());
                miniAccountCommission.setAmount(payCommission.negate());
                miniAccountCommission.setUserId(miniAccountExtends.getShopUserId());
                miniAccountCommission.setOrderId(Long.valueOf(orderId));
                miniAccountCommission.setRemark(CommissionTypeEnum.ORDER_COMMISSION.getDesc());
                miniAccountCommission.setSource(CommonConstants.NUMBER_ZERO);

                BigDecimal usedCommission = account.getUsedCommission() == null ?  BigDecimal.ZERO : account.getUsedCommission();
                BigDecimal currentCommission = account.getCurrentCommission() == null ? BigDecimal.ZERO : account.getCurrentCommission();
                account.setUsedCommission(usedCommission.add(payCommission));
                account.setCurrentCommission(currentCommission.subtract(payCommission));

                miniAccountCommission.setLastCommission(currentCommission);
                miniAccountCommission.setTotalCommission(account.getCurrentCommission());

                miniAccountCommissionService.save(miniAccountCommission);

            }
            this.updateById(account);
            result = true;
        }else{
            result = true;
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSaleFlag(List<String> accountIds, Integer saleFlag) {
        if(accountIds!=null&&accountIds.size()>0){
            for (String accountId : accountIds) {
                if(SalesmanFlagEnum.YES.getStatus() == saleFlag){
                    MiniAccount miniAccount = this.getById(accountId);
                    if(miniAccount == null || miniAccount.equals("")){
                        throw new ServiceException("用户不存在，不能设为业务员！");
                    }
                    if(miniAccount.getRegisterTime() == null){
                        throw new ServiceException("用户"+miniAccount.getNikeName()+"未注册，不能设为业务员！");
                    }
                }

            }
        }
        baseMapper.updateSaleFlag(accountIds,saleFlag);
    }

    @Override
    public Integer getDirectMemberCount(Long memberTypeId, String userId) {
        return this.baseMapper.getDirectMemberCount(memberTypeId,userId);
    }

    @Override
    public Integer getInDirectMemberCount(Long memberTypeId, String userId) {
        return this.baseMapper.getInDirectMemberCount(memberTypeId,userId);
    }

    @Override
    public Integer getRegionMemberCount(String memberTypeId, String userId) {
        return this.baseMapper.getRegionMemberCount(memberTypeId,userId);
    }

    @Override
    public Integer getDirectMemberCountByMemberTypeIds(String directMemberLevelIds, String userId) {
        List<String>memberLevelIds = new ArrayList<>();
        if(StringUtils.isNotEmpty(directMemberLevelIds)){
            for (String memberLevelId : directMemberLevelIds.split(",")) {
                memberLevelIds.add(memberLevelId);
            }
        }
        return this.baseMapper.getDirectMemberCountByMemberTypeIds(memberLevelIds,userId);
    }

    @Override
    public void exportUserList(String nikeName, String phone, Long tagId, String orderSuccessStartTime,
                               String orderSuccessEndTime, Integer sortType, Integer userId, Integer superiorAndSubordinate,
                               Long parentId, Long aboveParentId, String shopIds,Integer expireDays) {

        // 返回有问题
        PageUtils pageUtils = this.userList(nikeName, phone, tagId, orderSuccessStartTime, orderSuccessEndTime,
                1, CommonConstants.MAX_EXPORT_SIZE, sortType, userId, superiorAndSubordinate, parentId, aboveParentId, shopIds,expireDays);
        List<UserListVo> list = pageUtils.getList();
        HuToolExcelUtils.exportData(list, "客户列表", source -> {
            UserListExcelVo excelVo = new UserListExcelVo();
            BeanUtils.copyProperties(source, excelVo);

            // 处理标签拼接
            if (CollectionUtil.isNotEmpty(source.getUserTagVos())) {
                String tags = source.getUserTagVos().stream()
                        .map(UserTagVo::getTagName)
                        .collect(Collectors.joining(","));
                excelVo.setUserTags(tags);
            }

            if (source.getSendStatus()!=null){
                excelVo.setSendStatus(1 == source.getSendStatus() ? "已发送" : "未发送");
            }else {
                excelVo.setSendStatus("未发送");
            }
            // 格式化时间
            if (source.getOrderLastDealTime() != null) {
                excelVo.setOrderLastDealTime(DateUtil.format(source.getOrderLastDealTime(), "yyyy-MM-dd HH:mm:ss"));
            }
            if (source.getFirstLoginTime() != null) {
                excelVo.setFirstLoginTime(DateUtil.format(source.getFirstLoginTime(), "yyyy-MM-dd HH:mm:ss"));
            }
            return excelVo;
        });
    }

    @Override
    public void exportMiniAccountDetail(MiniAccountDetailParam param) {
        HuToolExcelUtils.exportParamToMax(param);

        IPage<MiniAccountDetailVo> page = this.searchMiniAccountDetail(param);
        List<MiniAccountDetailVo> detailList = page.getRecords();

        HuToolExcelUtils.exportData(detailList, "客户明细", source -> {
            MiniAccountDetailExcelVo excelVo = new MiniAccountDetailExcelVo();
            BeanUtils.copyProperties(source, excelVo);
            List<MiniAccountAddress> miniAccountAddressList = source.getMiniAccountAddressList();
            String address = "";
            if(miniAccountAddressList!=null&&miniAccountAddressList.size()>0){
                for (MiniAccountAddress miniAccountAddress : miniAccountAddressList) {
                    if(address.length()>0){
                        address = address + "，";
                    }
                    address = address + miniAccountAddress.getProvince() + "-" +
                            miniAccountAddress.getCity() + "-" +
                            miniAccountAddress.getCounty() + "-" +
                            miniAccountAddress.getDetailInfo();
                }
            }
            excelVo.setAddress(address);
            // 格式化注册时间
            if (source.getFirstLoginTime() != null) {
                excelVo.setFirstLoginTime(DateUtil.format(source.getFirstLoginTime(), "yyyy-MM-dd HH:mm:ss"));
            }
            return excelVo;
        });
    }

    @Override
    public Integer vailDeleteOrderFlag(Long orderId) {
        Integer result = 0;
        if(result == 0){
            LambdaQueryWrapper<MiniAccountPackageGoods>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MiniAccountPackageGoods::getOrderId,orderId);
            List<MiniAccountPackageGoods> list = miniAccountPackageGoodsService.list(wrapper);
            if(list!=null&&list.size()>0){
                for (MiniAccountPackageGoods miniAccountPackageGoods : list) {
                    if(miniAccountPackageGoods.getAlreadyTimes()>0){
                        result = 1;
                        log.info("用户套包已经核销");
                        break;
                    }
                }
            }
        }
        if(result == 0){
            LambdaQueryWrapper<MiniAccountPassTicket>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MiniAccountPassTicket::getOrderId,orderId);
            List<MiniAccountPassTicket> list = miniAccountPassTicketService.list(wrapper);
            if(list!=null&&list.size()>0){
                for (MiniAccountPassTicket miniAccountPassTicket : list) {
                    if(PromotionStatusEnum.USED.getStatus() == miniAccountPassTicket.getStatus()){
                        result = 2;
                        log.info("用户通惠证已经核销");
                        break;
                    }
                }
            }
        }
        return result;
    }

    @Override
    @Transactional
    public void deleteOrder(OrderVo orderVo) {
        TenantContextHolder.setTenantId(orderVo.getTenantId());
        //1.删除套包
        miniAccountPackageOrderService.deleteByOrderId(orderVo.getId());
        //2.删除通惠证
        miniAccountPassTicketService.deleteByPassTicketId(orderVo.getId());
        //3.回退积分
        miniAccountIntegralService.rollback(orderVo.getId());
        //4.回退佣金
        miniAccountCommissionService.rollback(orderVo.getId());

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateMemberLevelRelation(MiniAccount miniAccount){

        if (miniAccount.getMemberLevelId() == null || miniAccount.getId()==null){
            throw new ServiceException("参数错误",SystemCode.PARAM_MISS.getCode());
        }

        MiniAccount account = this.getBaseMapper().selectById(miniAccount.getId());
        if (account == null)
            throw new ServiceException("找不到用户账户",SystemCode.DATA_NOT_EXIST_CODE);
        // 查找需要更新的会员类型;
        MemberLevel memberLevel = memberLevelService.getById(miniAccount.getMemberLevelId());
        if (memberLevel==null){
            throw new ServiceException("找不到需要更新的会员等级",SystemCode.DATA_NOT_EXIST_CODE);
        }

        // 当前会员类型下是否存在会员等级记录
        MemberLevelRelationParam param = new MemberLevelRelationParam();
        param.setUserId(account.getUserId());
        param.setMemberTypeId(memberLevel.getMemberTypeId());
        List<MemberLevelRelation> memberLevelRelationList
                = memberLevelRelationService.getMemberLevelRelationNotMemberLevelId(param);


        boolean updateFlag;
        if (CollectionUtil.isEmpty(memberLevelRelationList)){
            // 添加
            MemberLevelRelation updateMemberLevelRelation = new MemberLevelRelation();
            updateMemberLevelRelation.setMemberLevelId(memberLevel.getId());
            updateMemberLevelRelation.setMemberTypeId(memberLevel.getMemberTypeId());
            updateMemberLevelRelation.setUserId(account.getUserId());
            updateMemberLevelRelation.setUpLevelTime(LocalDateTime.now());
            updateFlag = memberLevelRelationService.save(updateMemberLevelRelation);
        }else {
            //修改
            MemberLevelRelation memberLevelRelation = memberLevelRelationList.get(0);

            if(StringUtils.isNotEmpty(memberLevelRelation.getMemberLevelId())){
                MemberLevel oldMemberLevel = memberLevelService.getById(memberLevelRelation.getMemberLevelId());
                if(oldMemberLevel.getMemberFlag() == MemberFlagEnum.NO.getStatus()&&
                        memberLevel.getMemberFlag() == MemberFlagEnum.YES.getStatus()){
                    memberLevelRelation.setUpLevelTime(LocalDateTime.now());
                }
            }
            memberLevelRelation.setMemberLevelId(memberLevel.getId());
            updateFlag = memberLevelRelationService.updateById(memberLevelRelation);
        }

        if (!updateFlag)
            throw new ServiceException("更新会员关系失败",SystemCode.DATA_UPDATE_FAILED_CODE);
        //更新会员等级关系
        /*     miniAccountService.updateById(miniAccount);*/
    }


    @Override
    public IPage<MemberSalesReportVo> getSalesReport(MemberSalesReportParam param) {
        IPage<MemberSalesReportVo> salesReport = this.getBaseMapper().getSalesReport(new Page<>(param.getCurrent(), param.getSize()), param);
    /*    //合计
       if (salesReport.getRecords() != null){
           BigDecimal  totalAmount = BigDecimal.ZERO;
           Integer totalCount = 0;
           Integer  totalQtl = 0;
           for (MemberSalesReportVo data : salesReport.getRecords()) {
               totalAmount = totalAmount.add(data.getPurchaseAmount());
               totalCount += data.getPurchaseCount();
               totalQtl += data.getPurchaseQuantity();
           }
           MemberSalesReportVo memberSalesReportVo = new MemberSalesReportVo();
           memberSalesReportVo.setRepurchaseAmount(totalAmount);
           memberSalesReportVo.setRepurchaseCount(totalCount);
           memberSalesReportVo.setRepurchaseQuantity(totalQtl);
           List<MemberSalesReportVo> records = salesReport.getRecords();
           records.add(memberSalesReportVo);
       }*/

        return salesReport;
    }

    @Override
    public IPage<MemberTypeReportVo> getMemberTypeReport(MemberReportParam param) {
        IPage<MemberTypeReportVo> typeReport = this.getBaseMapper().getMemberTypeReport(new Page<>(param.getCurrent(), param.getSize()), param);
        return typeReport;
    }

    @Override
    public IPage<MemberLevelReportVo> getMemberLevelReport(MemberReportParam param) {
        IPage<MemberLevelReportVo> levelReport = this.getBaseMapper().getMemberLevelReport(new Page<>(param.getCurrent(), param.getSize()), param);
        return levelReport;
    }
}
