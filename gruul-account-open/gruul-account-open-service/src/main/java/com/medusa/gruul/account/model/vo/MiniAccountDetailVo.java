package com.medusa.gruul.account.model.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.medusa.gruul.account.api.entity.MiniAccountAddress;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:24 2024/10/28
 */
@Data
@ApiModel(value = "会员详情")
public class MiniAccountDetailVo {
    @ApiModelProperty(value = "id ")
    private Long id;
    @ApiModelProperty(value = "用户id")
    private String userId;
    @ApiModelProperty(value = "会员卡号")
    private String cardNumber;
    @ApiModelProperty(value = "用户昵称")
    private String nikeName;
    @ApiModelProperty(value = "会员等级")
    private String memberLevel;
    @ApiModelProperty(value = "成为会员时间")
    private LocalDateTime firstLoginTime;
    @ApiModelProperty(value = "手机号码")
    private String phone;
    @ApiModelProperty(value = "推荐人")
    private String parentName;
    @ApiModelProperty(value = "消费次数")
    private Integer consumeNum;
    @ApiModelProperty(value = "交易总额")
    private BigDecimal consumeTotalMoney;
    @ApiModelProperty(value = "会员消费额")
    private BigDecimal memberMoney;
    @ApiModelProperty(value = "年龄")
    private Integer age;
    @ApiModelProperty(value = "性别 0：未知、1：男、2：女")
    private Integer gender;
    @ApiModelProperty(value = "客户名称")
    private String userName;
    @ApiModelProperty(value = "生日")
    private String birthday;
    @ApiModelProperty(value = "身份证")
    private String card;
    @ApiModelProperty(value = "积分")
    private BigDecimal integral;
    @ApiModelProperty(value = "已消费积分")
    private BigDecimal usedIntegral;
    @ApiModelProperty(value = "当前积分")
    private BigDecimal currentIntegral;
    @ApiModelProperty(value = "销售积分")
    private BigDecimal saleIntegral;
    @ApiModelProperty(value = "总积分")
    private BigDecimal allIntegral;
    @ApiModelProperty(value = "微信收款码URL")
    private String wxAccountUrl;
    @ApiModelProperty(value = "支付宝收款码URL")
    private String alipayAccountUrl;
    @ApiModelProperty(value = "实名状态:-1->审核不通过;0->未实名;1->待审核;2->已实名")
    private Integer cardAuthorization;

    @ApiModelProperty(value = "银行卡信息")
    private List<MiniAccountBankVo> AccountBank;

    /**
     * 身份证人像面URL
     */
    @ApiModelProperty(value = "身份证人像面URL")
    private String idCardFaceUrl;

    /**
     * 身份证背面URL
     */
    @ApiModelProperty(value = "身份证背面URL")
    private String idCardBackUrl;

    @ApiModelProperty(value = "推荐人")
    private String recommendName;
    @ApiModelProperty(value = "小程序店铺id")
    private String shopUserId;
    @ApiModelProperty(value = "小程序用户收货地址列表")
    private List<MiniAccountAddress>miniAccountAddressList;
    @ApiModelProperty(value = "小程序用户收货地址")
    private String address;
}
