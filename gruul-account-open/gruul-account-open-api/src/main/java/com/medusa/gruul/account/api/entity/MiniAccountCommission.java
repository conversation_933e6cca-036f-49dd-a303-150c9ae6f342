package com.medusa.gruul.account.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 小程序会员-佣金明细记录
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_mini_account_commission")
@ApiModel(value = "MiniAccountCommission对象", description = "小程序会员-佣金明细记录")
public class MiniAccountCommission extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    @TableField(value = "create_user_name",fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    @TableField(value = "create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 最近更新人id
     */
    @ApiModelProperty(value = "最近更新人id")
    @TableField(value = "last_modify_user_id",fill = FieldFill.UPDATE)
    private Long lastModifyUserId;

    /**
     * 最近更新人姓名
     */
    @ApiModelProperty(value = "最近更新人姓名")
    @TableField(value = "last_modify_user_name",fill = FieldFill.UPDATE)
    private String lastModifyUserName;

    /**
     * 小程序用户id（实际存的是用户信息扩展表的shop_user_id）
     */
    @ApiModelProperty(value = "小程序用户id")
    @TableField("user_id")
    private String userId;

    /**
     * 订单id
     */
    @ApiModelProperty(value = "订单id")
    @TableField("order_id")
    private Long orderId;

    /**
     * 佣金类型:100->团队分佣;佣金类型:101->奖励佣金;佣金类型:102->奖励提成;200->佣金提现;300->删除订单
     */
    @ApiModelProperty(value = "佣金类型:100->团队分佣;佣金类型:101->奖励佣金;佣金类型:102->奖励提成;200->佣金提现;300->删除订单")
    @TableField("commission_type")
    private Integer commissionType;

    /**
     * 佣金
     */
    @ApiModelProperty(value = "佣金")
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    /**
     * 此笔订单佣金下订单的用户的shopUserId
     */
    @ApiModelProperty(value = "佣金来源店铺用户id")
    @TableField("source_shop_user_id")
    private String sourceShopUserId;
    /**
     * 奖励活动id
     */
    @ApiModelProperty(value = "奖励活动id")
    @TableField("reward_id")
    private String rewardId;
    /**
     * 奖励活动明细id
     */
    @ApiModelProperty(value = "奖励活动明细id")
    @TableField("reward_det_id")
    private String rewardDetId;


    /**
     * 转赠人用户ID
     */
    @ApiModelProperty(value = "转赠人用户ID")
    @TableField("transfer_shop_user_id")
    private String transferShopUserId;

    /**
     * 接收人用户ID
     */
    @ApiModelProperty(value = "接收人用户ID")
    @TableField("receive_shop_user_id")
    private String receiveShopUserId;

    /**
     * 后台备注
     */
    @ApiModelProperty(value = "后台备注")
    private String platformRemark;

    /**
     * 数据来源，0-系统，1-后台
     */
    @ApiModelProperty(value = "数据来源，0-系统，1-后台")
    private Integer source;

    /**
     * 平台操作用户id
     */
    @ApiModelProperty(value = "平台操作用户id")
    private String platformUserId;

    /**
     * 平台操作用户名
     */
    @ApiModelProperty(value = "平台操作用户名")
    private String platformUserName;


    /**
     * 变动前佣金
     */
    @ApiModelProperty(value = "变动前佣金")
    @TableField("last_commission")
    private BigDecimal lastCommission;

    /**
     * 变动后佣金
     */
    @ApiModelProperty(value = "变动后佣金")
    @TableField("total_commission")
    private BigDecimal totalCommission;
}
