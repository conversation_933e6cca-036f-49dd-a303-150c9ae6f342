package com.medusa.gruul.account.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:15 2025/6/11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_mini_account_golden")
@ApiModel(value = "MiniAccountGolden对象", description = "小程序会员-金豆明细记录")
public class MiniAccountGolden extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    @TableField(value = "create_user_name",fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    @TableField(value = "create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 最近更新人id
     */
    @ApiModelProperty(value = "最近更新人id")
    @TableField(value = "last_modify_user_id",fill = FieldFill.UPDATE)
    private Long lastModifyUserId;

    /**
     * 最近更新人姓名
     */
    @ApiModelProperty(value = "最近更新人姓名")
    @TableField(value = "last_modify_user_name",fill = FieldFill.UPDATE)
    private String lastModifyUserName;

    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id")
    @TableField("shop_id")
    private String shopId;

    /**
     * 小程序用户id（实际存的是用户信息扩展表的shop_user_id）
     */
    @ApiModelProperty(value = "小程序用户id")
    @TableField("user_id")
    private String userId;

    /**
     * 订单id
     */
    @ApiModelProperty(value = "订单id")
    @TableField("order_id")
    private Long orderId;

    /**
     * 佣金类型:99->金豆划转;100->消费;104->订单消费
     */
    @ApiModelProperty(value = "佣金类型:99->金豆划转;100->消费;104->订单消费")
    @TableField("commission_type")
    private Integer commissionType;

    /**
     * 金豆
     */
    @ApiModelProperty(value = "金豆")
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 订单类型:1->快递订单;2->充值订单
     */
    @ApiModelProperty(value = "订单类型:1->快递订单;2->充值订单")
    @TableField("order_type")
    private Integer orderType;

    /**
     * 奖励活动id
     */
    @ApiModelProperty(value = "奖励活动id")
    @TableField("reward_id")
    private String rewardId;
    /**
     * 奖励活动明细id
     */
    @ApiModelProperty(value = "奖励活动明细id")
    @TableField("reward_det_id")
    private String rewardDetId;

    /**
     * 接收人用户ID
     */
    @ApiModelProperty(value = "接收人用户ID")
    @TableField("receive_user_id")
    private String receiveUserId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    /**
     * 数据来源，0-系统，1-后台
     */
    @ApiModelProperty(value = "数据来源，0-系统，1-后台")
    @TableField("source")
    private Integer source;

    /**
     * 变更前金豆
     */
    @ApiModelProperty(value = "变更前金豆")
    @TableField("last_golden")
    private BigDecimal lastGolden;

    /**
     * 金豆
     */
    @ApiModelProperty(value = "变更后金豆")
    @TableField("total_golden")
    private BigDecimal totalGolden;

    /**
     * 金豆来源店铺用户id
     */
    @ApiModelProperty(value = "金豆来源店铺用户id")
    @TableField("source_shop_user_id")
    private String sourceShopUserId;

}
