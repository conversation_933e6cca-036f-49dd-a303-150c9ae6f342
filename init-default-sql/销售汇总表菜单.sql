-- 添加销售汇总表菜单
INSERT INTO `t_auth_menu_info` 
VALUES (
    UNIX_TIMESTAMP() + FLOOR(RAND() * 1000), 
    '销售汇总表', 
    'salesReport', 
    (SELECT id FROM `t_auth_menu_info` WHERE name = '权益包商品' AND parent_id = 0 LIMIT 1), 
    4, 
    0, 
    NOW(), 
    NOW(), 
    NULL, 
    NULL, 
    NULL, 
    NULL, 
    NULL, 
    '100001', 
    NULL, 
    NULL, 
    NULL, 
    NULL, 
    NULL, 
    1
);

-- 说明：
-- 1. 使用时间戳和随机数生成唯一ID
-- 2. 名称为"销售汇总表"
-- 3. 代码为"salesReport"，与前端路由匹配
-- 4. 父ID通过查询权益包商品的ID获取
-- 5. 显示顺序设为4，表示在销售记录(3)和销售明细(3)之后
-- 6. 最后的1表示启用状态 