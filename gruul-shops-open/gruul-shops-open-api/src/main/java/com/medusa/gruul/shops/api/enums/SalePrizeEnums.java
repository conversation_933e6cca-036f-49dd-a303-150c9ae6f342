package com.medusa.gruul.shops.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;

public class SalePrizeEnums {

    /**
     * 活动状态
     */
    @Getter
    public enum Status {
        STATUS_DRAFT(0, "草稿"),
        STATUS_NOT_RELEASED(101, "未生效"),
        STATUS_RELEASED(104, "已生效"),
        STATUS_EXPIRED(300, "失效"),
        STATUS_STOPPED(400, "停止");
        @EnumValue
        private final Integer code;
        private final String desc;
        Status(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }
    @TableField("status")
    private Integer status;
    @Getter
    public enum AuditStatus {

        STATUS_PENDING(100, "待审核"),
        STATUS_APPROVED(101, "审核通过"),
        STATUS_REJECTED(200, "审核不通过");
        @EnumValue
        private final Integer code;
        private final String desc;
        AuditStatus(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    /**
     * 是否指定商品
     */
    @Getter
    public enum ProductFlat {
        NO(0, "否"),
        YES(1, "是");
        @EnumValue
        private final Integer code;
        private final String desc;
        ProductFlat(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }
    /**
     * 奖品发放方式枚举
     */
    @AllArgsConstructor
    @Getter
    public enum VerifyType {
        ONLINE_DELIVERY(1, "线上邮寄"),
        OFFLINE_VERIFY(2, "线下核销");
        private final Integer code;
        private final String desc;
    }

    /**
     * 奖品类型枚举
     */
    @Getter
    @AllArgsConstructor
    public enum PrizeType {
        PRODUCT(1, "商城商品"),
        COUPON(2, "优惠券");
        private final Integer code;
        private final String desc;
    }

    /**
     * 是否间隔奖项枚举
     */
    @Getter
    @AllArgsConstructor
    public enum SpacingFlag {
        NO(0, "否"),
        YES(1, "是");

        private final Integer code;
        private final String desc;
    }

    /**
     * 是否奖品枚举
     */
    @Getter
    @AllArgsConstructor
    public enum PrizeFlag {
        NO(0, "否"),
        YES(1, "是");

        private final Integer code;
        private final String desc;
    }

    /**
     * 奖品状态枚举
     */
    @Getter
    @AllArgsConstructor
    public enum PrizeStatus {
        STATUS_UNISSUED(0, "未发放"),
        STATUS_ISSUED(1, "已发放");

        private final Integer code;
        private final String desc;
    }

    /**
     * 核销码状态枚举
     */
    @Getter
    @AllArgsConstructor
    public enum CodeStatus {
        STATUS_UNUSED(100, "未核销"),
        STATUS_USED(101, "已核销"),
        STATUS_EXPIRED(200, "已失效");

        private final Integer code;
        private final String desc;
    }

    /**
     * 赠送方式枚举
     */
    @Getter
    @AllArgsConstructor
    public enum GiftType {
        Order(1, "订单"),
        Direct (2, "直推"),
        Login(3, "登录"),
        Register(4, "注册"),
        Share(5, "分享");

        private final Integer code;
        private final String desc;
    }
}
