<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.shops.mapper.ShopSalePrizeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.shops.api.entity.ShopSalePrize">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="name" property="name" />
        <result column="per_count" property="perCount" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="back_image_url" property="backImageUrl" />
        <result column="member_level_ids" property="memberLevelIds" />
        <result column="product_flag" property="productFlag" />
        <result column="order_amount" property="orderAmount" />
        <result column="direct_member_qty" property="directMemberQty" />
        <result column="direct_low_member_level_id" property="directLowMemberLevelId" />
        <result column="prize_type" property="prizeType" />
        <result column="status" property="status" />
        <result column="audit_platform_user_id" property="auditPlatformUserId" />
        <result column="audit_time" property="auditTime" />
        <result column="audit_reason" property="auditReason" />
        <result column="remark" property="remark" />
        <result column="rule" property="rule" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_user_id" property="createUserId"  />
        <result column="create_user_name" property="createUserName"  />
        <result column="create_time" property="createTime"  />
        <result column="last_modify_user_id" property="lastModifyUserId"  />
        <result column="last_modify_user_name" property="lastModifyUserName"  />
        <result column="update_time" property="updateTime"  />
        <result column="is_deleted" property="deleted"  />
    </resultMap>


    <!-- 分页查询 -->
    <select id="getPage" resultType="com.medusa.gruul.shops.model.vo.ShopSalePrizeVo">
        SELECT
            t.id, t.shop_id, t.name, t.per_count, t.start_time, t.end_time, t.back_image_url,
        t.prize_type, t.status, t.create_time, t.update_time,t.remark,t.audit_status,t.create_User_Name,t.bill_date
        FROM
            t_shop_sale_prize t
        <where>
            t.is_deleted = 0
            <if test="param.auditStatus != null">
                AND t.audit_status = #{param.auditStatus}
            </if>
            <if test="param.status != null">
                AND t.status = #{param.status}
            </if>
            <if test="param.name != null and param.name != ''">
                AND t.name LIKE CONCAT('%', #{param.name}, '%')
            </if>
            <if test="param.startTime != null">
                AND t.start_time is not null AND t.start_time &gt;= #{param.startTime}
            </if>
            <if test="param.endTime != null">
                AND t.end_time is not null AND t.end_time &lt;= #{param.endTime}
            </if>
            <if test="param.prizeType != null">
                AND t.prize_type = #{param.prizeType}
            </if>
            <if test="param.createUserName != null and param.createUserName != ''">
                AND t.create_user_name = #{param.createUserName}
            </if>
            <if test="param.billDateStart != null">
                AND t.bill_date &gt;= #{param.billDateStart}
            </if>
            <if test="param.billDateEnd != null">
                AND t.bill_date &lt;= #{param.billDateEnd}
            </if>
        </where>
        <choose>
            <when test="param.sortType != null and param.sortType != '' and param.sortOrder != null and param.sortOrder != ''">
                ORDER BY
                <choose>
                    <when test="param.sortType == 'id'">t.id</when>
                    <when test="param.sortType == 'name'">t.name</when>
                    <when test="param.sortType == 'status'">t.status</when>
                    <when test="param.sortType == 'auditStatus'">t.audit_status</when>
                    <when test="param.sortType == 'prizeType'">t.prize_type</when>
                    <when test="param.sortType == 'startTime'">t.start_time</when>
                    <when test="param.sortType == 'endTime'">t.end_time</when>
                    <when test="param.sortType == 'createTime'">t.create_time</when>
                    <when test="param.sortType == 'updateTime'">t.update_time</when>
                    <when test="param.sortType == 'billDate'">t.bill_date</when>
                    <when test="param.sortType == 'createUserName'">t.create_user_name</when>
                    <otherwise>t.id</otherwise>
                </choose>
                <choose>
                    <when test="param.sortOrder == 'asc' or param.sortOrder == 'ASC'">ASC</when>
                    <otherwise>DESC</otherwise>
                </choose>
                ,id DESC
            </when>
            <otherwise>
                ORDER BY t.id DESC
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询明细列表 -->
    <select id="getItemPage" resultType="com.medusa.gruul.shops.model.vo.ShopSalePrizeItemDetailVo">
        SELECT
            i.id, i.main_id, i.level_name, i.seq_by, i.spacing_flag, i.prize_flag,
            i.verify_type, i.prize_type, i.product_id,
            i.sku_id, i.product_name, i.coupon_id, i.prize_name, i.winning_rate,
            i.level_pic, i.winning_person_times, t.name AS mainPrizeName, t.create_user_name, t.prize_type as main_prize_type, t.member_level_ids, t.start_time, t.end_time,
            (SELECT GROUP_CONCAT(DISTINCT pp.product_name ORDER BY pp.id SEPARATOR ',')
             FROM t_shop_sale_prize_product pp
             WHERE pp.main_id = t.id AND pp.is_deleted = 0) AS participateInTheProduct
        FROM
            t_shop_sale_prize_item i
            INNER JOIN t_shop_sale_prize t ON i.main_id = t.id
        <where>
            i.is_deleted = 0
            AND t.is_deleted = 0
 /*           AND t.audit_status = 101
            AND t.status = 104*/
            <if test="param.startTime != null">
                AND t.start_time &gt;= #{param.startTime}
            </if>
            <if test="param.endTime != null">
                AND t.end_time &lt;= #{param.endTime}
            </if>
            <if test="param.mainPrizeName != null and param.mainPrizeName != ''">
                AND t.name LIKE CONCAT('%', #{param.mainPrizeName}, '%')
            </if>
            <if test="param.createUserName != null and param.createUserName != ''">
                AND t.create_user_name  LIKE CONCAT('%', #{param.createUserName}, '%')
            </if>
            <if test="param.memberLevelIds != null and param.memberLevelIds != ''">
                AND FIND_IN_SET(#{param.memberLevelIds}, t.member_level_ids) > 0
            </if>
            <if test="param.mainPrizeType != null">
                AND t.prize_type = #{param.mainPrizeType}
            </if>
            <if test="param.participateInTheProduct != null and param.participateInTheProduct != ''">
                AND EXISTS (
                    SELECT 1 FROM t_shop_sale_prize_product pp
                    WHERE pp.main_id = t.id
                    AND pp.is_deleted = 0
                    AND pp.product_name LIKE CONCAT('%', #{param.participateInTheProduct}, '%')
                )
            </if>
        </where>
        ORDER BY i.id DESC
    </select>

    <!-- 分页查询汇总表 -->
    <select id="getSummaryPage" resultType="com.medusa.gruul.shops.model.vo.ShopSalePrizeSummaryVo">
        SELECT
            t.id AS mainPrizeId,
            i.id AS prizeItemId,
            t.name AS mainPrizeName,
            i.level_name AS levelName,
            i.prize_name AS prizeName,
            i.product_name AS productName,
            i.verify_type AS verifyType,
            prize_stats.winning_count AS winningCount,
            prize_stats.winning_person_count AS winningPersonCount,
            total_stats.total_participant_count AS totalParticipantCount,
            prize_stats.verify_person_count AS verifyPersonCount
        FROM
            t_shop_sale_prize t
            INNER JOIN t_shop_sale_prize_item i ON t.id = i.main_id
            INNER JOIN (
                SELECT
                    p.prize_id,
                    p.prize_item_id,
                    COUNT(*) AS winning_count,
                    COUNT(p.user_id) AS winning_person_count,
                    COUNT(CASE WHEN p.verify_type = 2 AND p.status = 1 THEN p.user_id END) AS verify_person_count
                FROM t_mini_account_sale_prize p
                WHERE p.is_deleted = 0 AND p.prize_flag = 1
                GROUP BY p.prize_id, p.prize_item_id
            ) prize_stats ON t.id = prize_stats.prize_id AND i.id = prize_stats.prize_item_id
            LEFT JOIN (
                SELECT
                    p.prize_id,
                    COUNT(DISTINCT p.user_id) AS total_participant_count
                FROM t_mini_account_sale_prize p
                WHERE p.is_deleted = 0
                GROUP BY p.prize_id
            ) total_stats ON t.id = total_stats.prize_id
        <where>
            t.is_deleted = 0
            AND i.is_deleted = 0
            <if test="param.mainPrizeName != null and param.mainPrizeName != ''">
                AND t.name LIKE CONCAT('%', #{param.mainPrizeName}, '%')
            </if>
            <if test="param.levelName != null and param.levelName != ''">
                AND i.level_name LIKE CONCAT('%', #{param.levelName}, '%')
            </if>
            <if test="param.prizeName != null and param.prizeName != ''">
                AND i.prize_name LIKE CONCAT('%', #{param.prizeName}, '%')
            </if>

        </where>
        ORDER BY t.id, i.id
    </select>

    <update id="updateStatusToExpired">
        UPDATE t_shop_sale_prize
        SET status = 300
        WHERE audit_status = 101
        AND end_time &lt; #{currentTime}
        AND is_deleted = 0
    </update>
</mapper> 