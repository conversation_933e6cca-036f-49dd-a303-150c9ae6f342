<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.shops.mapper.MiniAccountSalePrizeMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, prize_id, order_id, user_rate, prize_item_id, prize_flag, verify_type,
        start_time, end_time, prize_type, product_id, sku_id, product_name, coupon_id, prize_name,
        post_order_id, status, create_time, update_time, is_deleted,create_user_id,create_user_name,
        last_modify_user_id,last_modify_user_name
    </sql>

    <!-- 获取会员抽奖记录列表 -->
    <select id="getAccountPrizeList" resultType="com.medusa.gruul.shops.model.vo.MiniAccountSalePrizeVo">
        SELECT
        p.*,
        item.level_pic as product_pic,
        item.level_name,
        t.name as main_prize_name,
        c.verify_user_id,
        c.verify_time,
        c.verify_nick_name

        FROM
        t_mini_account_sale_prize p
        left join t_shop_sale_prize t on p.prize_id = t.id
        left join t_mini_account_sale_prize_code c on p.id = c.mini_account_prize_id
        left join t_shop_sale_prize_item item on item.main_id = t.id and item.product_id = p.product_id and item.sku_id = p.sku_id and item.is_deleted = 0

<!--        left join t_product i on p.product_id = i.id -->
        WHERE
        p.is_deleted = 0
        <if test="params.shopId != null and params.shopId != ''">
            AND p.shop_id = #{params.shopId}
        </if>
        <if test="params.userId != null and params.userId != ''">
            AND p.user_id = #{params.userId}
        </if>
        <if test="params.prizeFlag != null">
            AND p.prize_flag = #{params.prizeFlag}
        </if>
        <if test="params.verifyType != null">
            AND p.verify_type = #{params.verifyType}
        </if>
        <if test="params.prizeType != null">
            AND p.prize_type = #{params.prizeType}
        </if>
        <if test="params.status != null">
            AND p.status = #{params.status}
        </if>
        <if test="params.mainPrizeName != null and params.mainPrizeName != ''">
            AND t.name LIKE CONCAT('%', #{params.mainPrizeName}, '%')
        </if>
        <if test="params.levelName != null and params.levelName != ''">
            AND item.level_name LIKE CONCAT('%', #{params.levelName}, '%')
        </if>

        <if test="params.createStartTime != null">
            AND p.create_time &gt;=  #{params.createStartTime}
        </if>

        <if test="params.createEndTime != null ">
            AND p.create_time  &lt;=  #{params.createEndTime}
        </if>
        <if test="params.productName != null and params.productName != '' ">
            AND p.product_name  LIKE CONCAT('%', #{params.productName}, '%')
        </if>

        <if test="params.verifyNickName != null and params.verifyNickName != '' ">
            AND c.verify_nick_name  LIKE CONCAT('%', #{params.verifyNickName}, '%')
        </if>

        <if test="params.userName != null and params.userName != '' ">
            AND p.user_name  LIKE CONCAT('%', #{params.userName}, '%')
        </if>
        <if test="params.userPhone != null and params.userPhone != '' ">
            AND p.user_phone  LIKE CONCAT('%', #{params.userPhone}, '%')
        </if>

        ORDER BY
        p.id DESC
    </select>

    <select id="getVerifyUserPrizeCode" resultType="com.medusa.gruul.shops.model.vo.MiniPrizeCodeVerifyVo">
        SELECT
        t.name as main_prize_name,
        c.id as code_id,
        c.status as code_status,
        c.create_time as code_create_time,
        c.verify_code,
        p.*
        FROM
        t_mini_account_sale_prize p
        left join t_mini_account_sale_prize_code c on c.mini_account_prize_id = p.id
        left join t_shop_sale_prize t on p.prize_id = t.id
        WHERE
        p.is_deleted = 0 AND p.verify_type = 2
        <if test="params.verifyUserId != null ">
            AND c.verify_user_id = #{params.verifyUserId}
        </if>
        <if test="params.status != null ">
            AND c.status = #{params.status}
        </if>
        <if test="params.verifyStartTime != null">
            AND c.verify_time >=  #{params.verifyStartTime}
        </if>
        <if test="params.verifyEndTime != null ">
            AND c.verify_time  &lt;=  #{params.verifyEndTime}
        </if>
        ORDER BY
        c.id DESC
    </select>
</mapper>