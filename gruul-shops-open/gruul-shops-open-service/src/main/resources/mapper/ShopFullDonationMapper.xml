<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.shops.mapper.ShopFullDonationMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.shops.api.model.ShopFullDonationVo">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="per_count" property="perCount"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="member_level_ids" property="memberLevelIds"/>
        <result column="shops_flag" property="shopsFlag"/>
        <result column="product_flag" property="productFlag"/>
        <result column="status" property="status"/>
        <result column="audit_platform_user_id" property="auditPlatformUserId"/>
        <result column="audit_platform_user_name" property="auditPlatformUserName"/>
        <result column="audit_time" property="auditTime"/>
        <result column="audit_reason" property="auditReason"/>
        <result column="remark" property="remark"/>
        <collection property="shopFullDonationPartnerList" ofType="com.medusa.gruul.shops.api.model.ShopFullDonationPartnerVo"
                    column="id" select="com.medusa.gruul.shops.mapper.ShopFullDonationPartnerMapper.queryShopFullDonationPartner"
                    ></collection>
        <collection property="shopFullDonationProductList" ofType="com.medusa.gruul.shops.api.model.ShopFullDonationProductVo"
                    column="id" select="com.medusa.gruul.shops.mapper.ShopFullDonationProductMapper.queryShopFullDonationProductList"
                    ></collection>
        <collection property="shopFullDonationRuleList" ofType="com.medusa.gruul.shops.api.model.ShopFullDonationRuleVo"
                    column="id" select="com.medusa.gruul.shops.mapper.ShopFullDonationRuleMapper.queryShopFullDonationRuleList"
                    ></collection>
    </resultMap>
    <select id="getShopFullDonation" resultMap="BaseResultMap">
        SELECT
            t1.id,
            t1.name,
            t1.per_count,
            t1.start_time,
            t1.end_time,
            t1.member_level_ids,
            t1.shops_flag,
            t1.product_flag,
            t1.status,
            t1.audit_platform_user_id,
            t2.nike_name as audit_platform_user_name,
            t1.audit_time,
            t1.audit_reason,
            t1.remark
        FROM
            t_shop_full_donation t1
        LEFT JOIN
            t_platform_account_info t2 ON t1.audit_platform_user_id = t2.id
        where t1.is_deleted = 0
        <if test="params.name!=null and params.name!='' ">
            and t1.name like CONCAT('%',#{params.name},'%')
        </if>

        <if test="params.startTime!=null ">
            and t1.start_time &gt;=#{params.startTime}
        </if>
        <if test="params.endTime!=null ">
            and t1.end_time &lt;=#{params.endTime}
        </if>
        <if test="params.status!=null ">
            and t1.status = #{params.status}
        </if>
        order by
            t1.create_time
        desc

    </select>
    <select id="getShopFullDonationById" resultMap="BaseResultMap">
        SELECT
        t1.id,
        t1.name,
        t1.per_count,
        t1.start_time,
        t1.end_time,
        t1.member_level_ids,
        t1.shops_flag,
        t1.product_flag,
        t1.status,
        t1.audit_platform_user_id,
        t2.nike_name as audit_platform_user_name,
        t1.audit_time,
        t1.audit_reason,
        t1.remark
        FROM
        t_shop_full_donation t1
        LEFT JOIN
        t_platform_account_info t2 ON t1.audit_platform_user_id = t2.id
        where t1.is_deleted = 0 and t1.id = #{id}
    </select>

    <select id="getValidShopFullDonation" resultMap="BaseResultMap">
        SELECT
        t1.id,
        t1.name,
        t1.per_count,
        t1.start_time,
        t1.end_time,
        t1.member_level_ids,
        t1.shops_flag,
        t1.product_flag,
        t1.status,
        t1.audit_platform_user_id,
        t2.nike_name as audit_platform_user_name,
        t1.audit_time,
        t1.audit_reason,
        t1.remark
        FROM
        t_shop_full_donation t1
        LEFT JOIN
        t_platform_account_info t2 ON t1.audit_platform_user_id = t2.id
        where t1.is_deleted = 0
        <if test="params.name!=null and params.name!='' ">
            and t1.name like CONCAT('%',#{params.name},'%')
        </if>

        <if test="params.startTime!=null ">
            and t1.start_time &lt;=#{params.startTime}
        </if>
        <if test="params.endTime!=null ">
            and t1.end_time &gt;=#{params.endTime}
        </if>
        <if test="params.status!=null ">
            and t1.status = #{params.status}
        </if>
        <if test="params.memberLevelIdList!=null and params.memberLevelIdList.size>0">
            AND
            <foreach collection="params.memberLevelIdList" item="memberLevelId" open="(" separator="or" close=")">
                 t1.member_level_ids like CONCAT('%',#{memberLevelId},'%')
            </foreach>
        </if>
        <if test="params.userId!=null and params.userId!='' ">
            and (ifNull(t1.per_count, 0) = 0 or t1.per_count &gt;
                 (SELECT COUNT(DISTINCT t4.id) FROM t_order_item t3 left join t_order t4 on t3.order_id = t4.id WHERE t3.is_deleted = 0 AND t4.is_deleted = 0
                 AND t3.activity_id = t1.id AND t4.user_id = #{params.userId}
                <if test="params.orderStatusList!=null and params.orderStatusList.size>0">
                    AND t4.status in
                    <foreach collection="params.orderStatusList" item="status" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </if>
              )
            )
        </if>
    </select>

</mapper>
