package com.medusa.gruul.shops.service.handler;

import com.medusa.gruul.shops.api.entity.ShopSalePrize;
import com.medusa.gruul.shops.api.enums.SalePrizeEnums;
import com.medusa.gruul.shops.model.PrizeMessage;
import com.medusa.gruul.shops.service.PrizeGrantStrategy;
import com.medusa.gruul.shops.service.impl.MiniShopSalePrizeServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 分享赠送抽奖机会策略
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class SharePrizeGrantHandler implements PrizeGrantStrategy {

    @Autowired
    private MiniShopSalePrizeServiceImpl miniShopSalePrizeService;

    @Override
    public SalePrizeEnums.GiftType getGiftType() {
        return SalePrizeEnums.GiftType.Share;
    }

    @Override
    public boolean canGrant(PrizeMessage message, ShopSalePrize prize) {
        // 检查活动是否配置了分享赠送
        if (prize.getShareNumber() == null || prize.getShareNumber() <= 0) {
            log.debug("活动 {} 未配置分享赠送或赠送次数为0", prize.getName());
            return false;
        }
        
        // 检查是否有新用户信息（存储在parentId字段中）
        if (message.getParentId() == null) {
            log.debug("分享消息中缺少新用户信息");
            return false;
        }
        
        return true;
    }

    @Override
    public void grantPrize(PrizeMessage message, ShopSalePrize prize) {
        try {
            // 赠送指定次数的抽奖机会给分享者
            for (int i = 0; i < prize.getShareNumber(); i++) {
                miniShopSalePrizeService.addPrizeCountToRedis(
                        -1L, // 订单ID，分享赠送时不需要
                        -1L, // 会员类型ID，分享赠送时不需要
                        prize,
                        message.getShopUserId(), // 分享者
                        getGiftType()
                );
            }
            
            log.info("用户 {} 通过分享获得抽奖机会，活动: {}, 赠送次数: {}, 新用户: {}", 
                    message.getShopUserId(), prize.getName(), prize.getShareNumber(), message.getParentId());
                    
        } catch (Exception e) {
            log.error("分享赠送抽奖机会失败: 分享者={}, 新用户={}, 活动={}", 
                    message.getShopUserId(), message.getParentId(), prize.getName(), e);
            throw e;
        }
    }
}
