package com.medusa.gruul.shops.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 抽奖概率测试类 - 用于测试抽奖概率逻辑，不依赖数据库
 */
public class SalePrizeProbabilityTest {

    // 模拟奖项实体类
    static class ShopSalePrizeItem {
        private Long id;
        private BigDecimal winningRate;
        private Long winningPersonTimes;

        public ShopSalePrizeItem(Long id, BigDecimal winningRate, Long winningPersonTimes) {
            this.id = id;
            this.winningRate = winningRate;
            this.winningPersonTimes = winningPersonTimes;
        }

        public Long getId() {
            return id;
        }

        public BigDecimal getWinningRate() {
            return winningRate;
        }

        public Long getWinningPersonTimes() {
            return winningPersonTimes;
        }
    }

    // 模拟中奖记录
    static class MiniAccountSalePrize {
        private Long prizeId;
        private Long prizeItemId;
        private Integer prizeFlag; // 1 表示中奖，0 表示未中奖

        public MiniAccountSalePrize(Long prizeId, Long prizeItemId, Integer prizeFlag) {
            this.prizeId = prizeId;
            this.prizeItemId = prizeItemId;
            this.prizeFlag = prizeFlag;
        }

        public Long getPrizeId() {
            return prizeId;
        }

        public Long getPrizeItemId() {
            return prizeItemId;
        }

        public Integer getPrizeFlag() {
            return prizeFlag;
        }
    }

    /**
     * 测试抽奖概率命中方法
     * 
     * @param prizeId 奖项ID
     * @param userRate 用户随机概率值
     * @param totalParticipations 总参与次数
     * @param eligibleItems 符合条件的奖项列表
     * @param winRecords 已有的中奖记录
     * @return 命中的奖项，如果没有命中则返回null
     */
    public static ShopSalePrizeItem testRaffleProbability(
            Long prizeId,
            BigDecimal userRate,
            long totalParticipations,
            List<ShopSalePrizeItem> eligibleItems,
            List<MiniAccountSalePrize> winRecords) {

        // 遍历是否中奖
        for (ShopSalePrizeItem item : eligibleItems) {
            // 统计已中奖的记录数
            long winCount = winRecords.stream()
                    .filter(record -> record.getPrizeId().equals(prizeId)
                            && record.getPrizeItemId().equals(item.getId())
                            && record.getPrizeFlag() == 1) // 1 表示中奖
                    .count();

            // 多少人次参与后最多中奖一次
            long winningPersonTimes = item.getWinningPersonTimes() != null ? item.getWinningPersonTimes() : 0;
            if (winningPersonTimes <= 0) {
                continue;
            }

            // 当前活动参与次数 / 多少人次参与后最多中奖一次 -> 允许的最大中奖次数
            long remainder = totalParticipations / winningPersonTimes;

            if (winCount < remainder) {
                return item;
            }
        }
        return null; // 没有中奖
    }

    /**
     * 测试用例：验证抽奖概率逻辑
     */
    public static void main(String[] args) {
        // 创建测试数据
        Long prizeId = 1L;
        
        // 创建奖项列表
        List<ShopSalePrizeItem> eligibleItems = new ArrayList<>();
        eligibleItems.add(new ShopSalePrizeItem(1L, new BigDecimal("0.1"), 10L)); // 10人次中奖1次，中奖率0.1
        eligibleItems.add(new ShopSalePrizeItem(2L, new BigDecimal("0.3"), 5L));  // 5人次中奖1次，中奖率0.3
        eligibleItems.add(new ShopSalePrizeItem(3L, new BigDecimal("0.6"), 2L));  // 2人次中奖1次，中奖率0.6
        eligibleItems.add(new ShopSalePrizeItem(4L, new BigDecimal("0.9"), 1L));  // 每人次都可能中奖，中奖率0.9

        // 创建中奖记录
        List<MiniAccountSalePrize> winRecords = new ArrayList<>();
        // 添加一些已有的中奖记录
        winRecords.add(new MiniAccountSalePrize(1L, 1L, 1)); // 奖项1已中奖1次
        winRecords.add(new MiniAccountSalePrize(1L, 2L, 1)); // 奖项2已中奖1次
        winRecords.add(new MiniAccountSalePrize(1L, 2L, 1)); // 奖项2已中奖2次

        // 测试不同情况
        System.out.println("=== 抽奖概率测试 ===");
        
        // 测试1: 总参与次数为15次
        long totalParticipations1 = 15;
        System.out.println("\n总参与次数: " + totalParticipations1);
        
        // 用户随机概率值为0.05 (很低)
        BigDecimal userRate1 = new BigDecimal("0.05");
        ShopSalePrizeItem result1 = testRaffleProbability(prizeId, userRate1, totalParticipations1, eligibleItems, winRecords);
        System.out.println("用户概率值: " + userRate1 + " -> 命中奖项: " + (result1 != null ? result1.getId() : "未中奖"));
        
        // 根据逻辑分析:
        // 奖项1: 15/10=1, 已中奖1次, 可中奖次数1次, 已满, 不能中奖
        // 奖项2: 15/5=3, 已中奖2次, 可中奖次数3次, 未满, 可以中奖
        // 奖项3: 15/2=7, 已中奖0次, 可中奖次数7次, 未满, 可以中奖
        // 奖项4: 15/1=15, 已中奖0次, 可中奖次数15次, 未满, 可以中奖
        // 由于按中奖率排序，奖项2中奖率0.3，用户概率0.05 < 0.3，所以应该命中奖项2

        // 用户随机概率值为0.4
        BigDecimal userRate2 = new BigDecimal("0.4");
        ShopSalePrizeItem result2 = testRaffleProbability(prizeId, userRate2, totalParticipations1, eligibleItems, winRecords);
        System.out.println("用户概率值: " + userRate2 + " -> 命中奖项: " + (result2 != null ? result2.getId() : "未中奖"));
        
        // 根据逻辑分析:
        // 奖项1: 15/10=1, 已中奖1次, 可中奖次数1次, 已满, 不能中奖
        // 奖项2: 15/5=3, 已中奖2次, 可中奖次数3次, 未满, 可以中奖
        // 奖项3: 15/2=7, 已中奖0次, 可中奖次数7次, 未满, 可以中奖
        // 奖项4: 15/1=15, 已中奖0次, 可中奖次数15次, 未满, 可以中奖
        // 用户概率0.4，大于奖项2的中奖率0.3，所以应该命中奖项3

        // 测试2: 总参与次数为3次
        long totalParticipations2 = 3;
        System.out.println("\n总参与次数: " + totalParticipations2);
        
        // 用户随机概率值为0.2
        BigDecimal userRate3 = new BigDecimal("0.2");
        ShopSalePrizeItem result3 = testRaffleProbability(prizeId, userRate3, totalParticipations2, eligibleItems, winRecords);
        System.out.println("用户概率值: " + userRate3 + " -> 命中奖项: " + (result3 != null ? result3.getId() : "未中奖"));
        
        // 根据逻辑分析:
        // 奖项1: 3/10=0, 已中奖1次, 可中奖次数0次, 已满, 不能中奖
        // 奖项2: 3/5=0, 已中奖2次, 可中奖次数0次, 已满, 不能中奖
        // 奖项3: 3/2=1, 已中奖0次, 可中奖次数1次, 未满, 可以中奖
        // 奖项4: 3/1=3, 已中奖0次, 可中奖次数3次, 未满, 可以中奖
        // 用户概率0.2，小于奖项3的中奖率0.6，所以应该命中奖项3

        System.out.println("\n=== 测试完成 ===");
    }
}
