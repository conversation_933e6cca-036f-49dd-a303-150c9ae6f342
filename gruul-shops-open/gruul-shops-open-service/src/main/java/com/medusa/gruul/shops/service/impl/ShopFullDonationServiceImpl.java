package com.medusa.gruul.shops.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.shops.api.entity.*;
import com.medusa.gruul.shops.api.enums.ProductFlagEnum;
import com.medusa.gruul.shops.api.enums.ShopFullDonationStatusFlagEnum;
import com.medusa.gruul.shops.api.enums.ShopsFlagEnum;
import com.medusa.gruul.shops.mapper.ShopFullDonationMapper;
import com.medusa.gruul.shops.model.dto.*;
import com.medusa.gruul.shops.api.model.ShopFullDonationParam;
import com.medusa.gruul.shops.api.model.ShopFullDonationVo;
import com.medusa.gruul.shops.service.IShopFullDonationPartnerService;
import com.medusa.gruul.shops.service.IShopFullDonationProductService;
import com.medusa.gruul.shops.service.IShopFullDonationRuleService;
import com.medusa.gruul.shops.service.IShopFullDonationService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:32 2025/7/4
 */
@Service
public class ShopFullDonationServiceImpl extends ServiceImpl<ShopFullDonationMapper, ShopFullDonation>implements IShopFullDonationService {

    @Autowired
    private IShopFullDonationProductService shopFullDonationProductService;
    @Autowired
    private IShopFullDonationPartnerService shopFullDonationPartnerService;
    @Autowired
    private IShopFullDonationRuleService shopFullDonationRuleService;

    @Override
    @Transactional
    public void add(ShopFullDonationDto dto) {

        Integer status = dto.getStatus();
        if(status!=null){
            if(status != ShopFullDonationStatusFlagEnum.DRAFT.getType()
                    && status != ShopFullDonationStatusFlagEnum.WAIT.getType() ){
                throw new ServiceException("满减满赠状态必须为待审核或者草稿");
            }
        }else{
            throw new ServiceException("满减满赠状态不能为空");
        }

        if(status == ShopFullDonationStatusFlagEnum.WAIT.getType()){
            List<ShopFullDonationRuleDto> shopFullDonationRuleList = dto.getShopFullDonationRuleList();
            if(shopFullDonationRuleList!=null&&shopFullDonationRuleList.size()>0){
                Integer fullType = null;
                for (ShopFullDonationRuleDto shopFullDonationRuleDto : shopFullDonationRuleList) {
                    if(shopFullDonationRuleDto.getFullType() == null){
                        throw new ServiceException("满减满赠规则满额类型不能为空");
                    }
                    if(fullType!=null && fullType == shopFullDonationRuleDto.getFullType()){
                        throw new ServiceException("一个活动里满额类型只能有一种");
                    }
                    fullType = shopFullDonationRuleDto.getFullType();
                }
                vailData(dto);
            }else{
                throw new ServiceException("满减满赠规则不能为空");
            }
        }
        ShopFullDonation shopFullDonation = new ShopFullDonation();
        BeanUtils.copyProperties(dto,shopFullDonation,"id");
        this.save(shopFullDonation);
        List<ShopFullDonationProductDto> shopFullDonationProductList = dto.getShopFullDonationProductList();
        if(shopFullDonationProductList!=null&& !shopFullDonationProductList.isEmpty()){
            for (ShopFullDonationProductDto shopFullDonationProductDto : shopFullDonationProductList) {
                ShopFullDonationProduct shopFullDonationProduct = new ShopFullDonationProduct();
                BeanUtils.copyProperties(shopFullDonationProductDto,shopFullDonationProduct);
                shopFullDonationProduct.setMainId(shopFullDonation.getId());
                shopFullDonationProductService.save(shopFullDonationProduct);
            }
        }

        List<ShopFullDonationPartnerDto> shopFullDonationPartnerList = dto.getShopFullDonationPartnerList();
        if(shopFullDonationPartnerList!=null&& !shopFullDonationPartnerList.isEmpty()){
            for (ShopFullDonationPartnerDto shopFullDonationPartnerDto : shopFullDonationPartnerList) {
                ShopFullDonationPartner shopFullDonationPartner = new ShopFullDonationPartner();
                BeanUtils.copyProperties(shopFullDonationPartnerDto,shopFullDonationPartner,"id");
                shopFullDonationPartner.setMainId(shopFullDonation.getId());
                shopFullDonationPartnerService.save(shopFullDonationPartner);
            }
        }
        List<ShopFullDonationRuleDto> shopFullDonationRuleList = dto.getShopFullDonationRuleList();
        if(shopFullDonationRuleList!=null&& !shopFullDonationRuleList.isEmpty()){
            for (ShopFullDonationRuleDto shopFullDonationRuleDto : shopFullDonationRuleList) {
                ShopFullDonationRule shopFullDonationRule = new ShopFullDonationRule();
                BeanUtils.copyProperties(shopFullDonationRuleDto,shopFullDonationRule);
                shopFullDonationRule.setMainId(shopFullDonation.getId());
                shopFullDonationRuleService.save(shopFullDonationRule);
            }
        }

    }

    @Override
    @Transactional
    public void edit(ShopFullDonationDto dto) {

        ShopFullDonation shopFullDonation = this.getById(dto.getId());
        if(shopFullDonation == null){
            throw new ServiceException("满赠满减活动不存在！");
        }

        if(shopFullDonation.getStatus()!=ShopFullDonationStatusFlagEnum.DRAFT.getType()
                &&shopFullDonation.getStatus()!=ShopFullDonationStatusFlagEnum.REFUSE.getType()){
            throw new ServiceException("在草稿、审核不通过的状态下才能编辑数据");
        }

        Integer status = dto.getStatus();
        if(status!=null){
            if(status != ShopFullDonationStatusFlagEnum.DRAFT.getType()
                    && status != ShopFullDonationStatusFlagEnum.WAIT.getType() ){
                throw new ServiceException("满减满赠状态必须为待审核或者草稿");
            }
        }else{
            throw new ServiceException("满减满赠状态不能为空");
        }

        if(status == ShopFullDonationStatusFlagEnum.WAIT.getType()){
            List<ShopFullDonationRuleDto> shopFullDonationRuleList = dto.getShopFullDonationRuleList();
            if(shopFullDonationRuleList!=null&&shopFullDonationRuleList.size()>0){
                Integer fullType = null;
                for (ShopFullDonationRuleDto shopFullDonationRuleDto : shopFullDonationRuleList) {
                    if(shopFullDonationRuleDto.getFullType() == null){
                        throw new ServiceException("满减满赠规则满额类型不能为空");
                    }
                    if(fullType!=null && fullType == shopFullDonationRuleDto.getFullType()){
                        throw new ServiceException("一个活动里满额类型只能有一种");
                    }
                    fullType = shopFullDonationRuleDto.getFullType();
                }
                vailData(dto);
            }else{
                throw new ServiceException("满减满赠规则不能为空");
            }
            if(shopFullDonationRuleList == null || (shopFullDonationRuleList!=null&&shopFullDonationRuleList.isEmpty())){

            }

        }

        BeanUtils.copyProperties(dto,shopFullDonation);
        this.updateById(shopFullDonation);
        //删除历史数据
        if(shopFullDonation.getId()!=null){
            LambdaQueryWrapper<ShopFullDonationProduct>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ShopFullDonationProduct::getMainId,shopFullDonation.getId());
            shopFullDonationProductService.remove(wrapper);
        }
        if(shopFullDonation.getId()!=null){
            LambdaQueryWrapper<ShopFullDonationPartner>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ShopFullDonationPartner::getMainId,shopFullDonation.getId());
            shopFullDonationPartnerService.remove(wrapper);
        }
        if(shopFullDonation.getId()!=null){
            LambdaQueryWrapper<ShopFullDonationRule>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ShopFullDonationRule::getMainId,shopFullDonation.getId());
            shopFullDonationRuleService.remove(wrapper);
        }


        List<ShopFullDonationProductDto> shopFullDonationProductList = dto.getShopFullDonationProductList();
        if(shopFullDonationProductList!=null&&shopFullDonationProductList.size()>0){
            for (ShopFullDonationProductDto shopFullDonationProductDto : shopFullDonationProductList) {
                ShopFullDonationProduct shopFullDonationProduct = new ShopFullDonationProduct();
                BeanUtils.copyProperties(shopFullDonationProductDto,shopFullDonationProduct);
                shopFullDonationProduct.setMainId(shopFullDonation.getId());
                shopFullDonationProductService.save(shopFullDonationProduct);
            }
        }

        List<ShopFullDonationPartnerDto> shopFullDonationPartnerList = dto.getShopFullDonationPartnerList();
        if(shopFullDonationPartnerList!=null&&shopFullDonationPartnerList.size()>0){
            for (ShopFullDonationPartnerDto shopFullDonationPartnerDto : shopFullDonationPartnerList) {
                ShopFullDonationPartner shopFullDonationPartner = new ShopFullDonationPartner();
                BeanUtils.copyProperties(shopFullDonationPartnerDto,shopFullDonationPartner);
                shopFullDonationPartner.setMainId(shopFullDonation.getId());
                shopFullDonationPartnerService.save(shopFullDonationPartner);
            }
        }
        List<ShopFullDonationRuleDto> shopFullDonationRuleList = dto.getShopFullDonationRuleList();
        if(shopFullDonationRuleList!=null&&shopFullDonationRuleList.size()>0){
            for (ShopFullDonationRuleDto shopFullDonationRuleDto : shopFullDonationRuleList) {
                ShopFullDonationRule shopFullDonationRule = new ShopFullDonationRule();
                BeanUtils.copyProperties(shopFullDonationRuleDto,shopFullDonationRule);
                shopFullDonationRule.setMainId(shopFullDonation.getId());
                shopFullDonationRuleService.save(shopFullDonationRule);
            }
        }
    }

    @Override
    @Transactional
    public void deleteById(Long id) {
        ShopFullDonation shopFullDonation = this.getById(id);


        if(shopFullDonation == null){
            throw new ServiceException("满赠满减活动不存在！");
        }
        if(shopFullDonation.getStatus()!=ShopFullDonationStatusFlagEnum.DRAFT.getType()){
            throw new ServiceException("草稿状态下的记录能删除!");
        }
        this.removeById(id);
        if(shopFullDonation.getId()!=null){
            LambdaQueryWrapper<ShopFullDonationProduct>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ShopFullDonationProduct::getMainId,shopFullDonation.getId());
            shopFullDonationProductService.remove(wrapper);
        }
        if(shopFullDonation.getId()!=null){
            LambdaQueryWrapper<ShopFullDonationPartner>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ShopFullDonationPartner::getMainId,shopFullDonation.getId());
            shopFullDonationPartnerService.remove(wrapper);
        }
        if(shopFullDonation.getId()!=null){
            LambdaQueryWrapper<ShopFullDonationRule>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ShopFullDonationRule::getMainId,shopFullDonation.getId());
            shopFullDonationRuleService.remove(wrapper);
        }
    }

    @Override
    @Transactional
    public void audit(ShopFullDonationAuditDto dto) {
        ShopFullDonation shopFullDonation = this.getById(dto.getId());
        if(shopFullDonation == null){
            throw new ServiceException("满赠满减活动不存在！");
        }
        if(shopFullDonation.getStatus()!=ShopFullDonationStatusFlagEnum.WAIT.getType()){
            throw new ServiceException("只能审核待审核记录!");
        }
        if(dto.getStatus() == ShopFullDonationStatusFlagEnum.PASS.getType()){
            ShopFullDonationDto shopFullDonationDto = new ShopFullDonationDto();
            BeanUtils.copyProperties(shopFullDonation,shopFullDonationDto);
            if(shopFullDonation.getId()!=null){
                LambdaQueryWrapper<ShopFullDonationProduct>wrapper= new LambdaQueryWrapper<>();
                wrapper.eq(ShopFullDonationProduct::getMainId,shopFullDonation.getId());
                List<ShopFullDonationProduct> list = shopFullDonationProductService.list(wrapper);
                List<ShopFullDonationProductDto>shopFullDonationProductList = new ArrayList<>();
                if(list!=null&&list.size()>0){
                    for (ShopFullDonationProduct shopFullDonationProduct : list) {
                        ShopFullDonationProductDto shopFullDonationProductDto = new ShopFullDonationProductDto();
                        BeanUtils.copyProperties(shopFullDonationProduct,shopFullDonationProductDto);
                        shopFullDonationProductList.add(shopFullDonationProductDto);
                    }
                }
                shopFullDonationDto.setShopFullDonationProductList(shopFullDonationProductList);
            }
            if(shopFullDonation.getId()!=null){
                LambdaQueryWrapper<ShopFullDonationPartner>wrapper= new LambdaQueryWrapper<>();
                wrapper.eq(ShopFullDonationPartner::getMainId,shopFullDonation.getId());
                List<ShopFullDonationPartner> list = shopFullDonationPartnerService.list(wrapper);
                List<ShopFullDonationPartnerDto>shopFullDonationPartnerList = new ArrayList<>();
                if(list!=null&&list.size()>0){
                    for (ShopFullDonationPartner ShopFullDonationPartner : list) {
                        ShopFullDonationPartnerDto shopFullDonationPartnerDto = new ShopFullDonationPartnerDto();
                        BeanUtils.copyProperties(ShopFullDonationPartner,shopFullDonationPartnerDto);
                        shopFullDonationPartnerList.add(shopFullDonationPartnerDto);
                    }
                }
                shopFullDonationDto.setShopFullDonationPartnerList(shopFullDonationPartnerList);
            }
            if(shopFullDonation.getId()!=null){
                LambdaQueryWrapper<ShopFullDonationRule>wrapper= new LambdaQueryWrapper<>();
                wrapper.eq(ShopFullDonationRule::getMainId,shopFullDonation.getId());
                List<ShopFullDonationRule> list = shopFullDonationRuleService.list(wrapper);
                List<ShopFullDonationRuleDto>shopFullDonationRuleList = new ArrayList<>();
                if(list!=null&&list.size()>0){
                    for (ShopFullDonationRule shopFullDonationRule : list) {
                        ShopFullDonationRuleDto shopFullDonationRuleDto = new ShopFullDonationRuleDto();
                        BeanUtils.copyProperties(shopFullDonationRule,shopFullDonationRuleDto);
                        shopFullDonationRuleList.add(shopFullDonationRuleDto);
                    }
                }
                shopFullDonationDto.setShopFullDonationRuleList(shopFullDonationRuleList);
            }
            shopFullDonationDto.setVailFlag(CommonConstants.NUMBER_TWO);
            vailData(shopFullDonationDto);
        }
        shopFullDonation.setStatus(dto.getStatus());
        shopFullDonation.setAuditTime(DateUtil.toLocalDateTime(new Date()));
        shopFullDonation.setAuditPlatformUserId(Long.parseLong(CurUserUtil.getPcRqeustAccountInfo().getUserId()));
        shopFullDonation.setAuditReason(dto.getAuditReason());
        this.updateById(shopFullDonation);
    }

    @Override
    @Transactional
    public void stop(ShopFullDonationAuditDto dto) {
        ShopFullDonation shopFullDonation = this.getById(dto.getId());
        if(shopFullDonation == null){
            throw new ServiceException("满赠满减活动不存在！");
        }
        if(shopFullDonation.getStatus()!=ShopFullDonationStatusFlagEnum.PASS.getType()){
            throw new ServiceException("只能停用审核通过记录!");
        }
        shopFullDonation.setStatus(ShopFullDonationStatusFlagEnum.STOP.getType());
        this.updateById(shopFullDonation);
    }

    @Override
    @Transactional
    public void autoUpdateStatusByTime() {
        LambdaQueryWrapper<ShopFullDonation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShopFullDonation::getStatus, ShopFullDonationStatusFlagEnum.PASS.getType());
        wrapper.lt(ShopFullDonation::getEndTime, LocalDateTime.now());
        List<ShopFullDonation> list = this.list(wrapper);
        if (list != null && list.size() > 0) {
            for (ShopFullDonation shopFullDonation : list) {
                shopFullDonation.setStatus(ShopFullDonationStatusFlagEnum.EXPIRE.getType());
                this.updateById(shopFullDonation);
            }
        }
    }

    @Override
    @Transactional
    public PageUtils<ShopFullDonationVo> getShopFullDonation(ShopFullDonationParam param) {
        this.autoUpdateStatusByTime();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        IPage<ShopFullDonationVo> page = this.baseMapper.getShopFullDonation(new Page(param.getCurrent(), param.getSize()), param);
        return new PageUtils<>(page);
    }

    @Override
    public ShopFullDonationVo getShopFullDonationById(ShopFullDonationAuditDto dto) {
        return this.baseMapper.getShopFullDonationById(dto.getId());
    }

    @Override
    public List<String> getShowTitleList(Long productId, String shopId,Integer buyType) {
        this.autoUpdateStatusByTime();
        List<String>showTitleList = new ArrayList<>();
        LocalDate now = LocalDate.now();
        LambdaQueryWrapper<ShopFullDonation>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShopFullDonation::getStatus,ShopFullDonationStatusFlagEnum.PASS.getType());
        wrapper.le(ShopFullDonation::getStartTime,now);
        wrapper.ge(ShopFullDonation::getEndTime,now);
        List<ShopFullDonation> list = this.list(wrapper);
        if(list!=null&&list.size()>0){
            for (ShopFullDonation shopFullDonation : list) {
                Boolean b = false;
                //先判断商家
                if(shopFullDonation.getShopsFlag() == ShopsFlagEnum.DEFAULT.getType()){
                    b = true;
                }else if(shopFullDonation.getShopsFlag() == ShopsFlagEnum.YES.getType()){
                    LambdaQueryWrapper<ShopFullDonationPartner>queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(ShopFullDonationPartner::getMainId,shopFullDonation.getId());
                    queryWrapper.eq(ShopFullDonationPartner::getShopsPartnerId,shopId);
                    int count = shopFullDonationPartnerService.count(queryWrapper);
                    if(count>0){
                        b = true;
                    }else{
                        b = false;
                    }
                }else if(shopFullDonation.getShopsFlag() == ShopsFlagEnum.NO.getType()){
                    LambdaQueryWrapper<ShopFullDonationPartner>queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(ShopFullDonationPartner::getMainId,shopFullDonation.getId());
                    queryWrapper.eq(ShopFullDonationPartner::getShopsPartnerId,shopId);
                    if(count()>0){
                        b = false;
                    }else{
                        b = true;
                    }
                }
                //在判断商品
                if(b){
                    if(shopFullDonation.getProductFlag() == ProductFlagEnum.DEFAULT.getType()){
                        b = true;
                    }
                }else if(shopFullDonation.getShopsFlag() == ShopsFlagEnum.YES.getType()){
                    LambdaQueryWrapper<ShopFullDonationProduct>queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(ShopFullDonationProduct::getMainId,shopFullDonation.getId());
                    queryWrapper.eq(ShopFullDonationProduct::getProductId,productId);
                    int count = shopFullDonationProductService.count(queryWrapper);
                    if(count>0){
                        b = true;
                    }else{
                        b = false;
                    }
                }else if(shopFullDonation.getShopsFlag() == ShopsFlagEnum.NO.getType()){
                    LambdaQueryWrapper<ShopFullDonationProduct>queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(ShopFullDonationProduct::getMainId,shopFullDonation.getId());
                    queryWrapper.eq(ShopFullDonationProduct::getProductId,productId);
                    int count = shopFullDonationProductService.count(queryWrapper);
                    if(count>0){
                        b = false;
                    }else{
                        b = true;
                    }
                }
                //获取规则标题
                if(b){
                    LambdaQueryWrapper<ShopFullDonationRule>queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(ShopFullDonationRule::getMainId,shopFullDonation.getId());
                    queryWrapper.eq(ShopFullDonationRule::getBuyType,buyType);
                    List<ShopFullDonationRule> ruleList = shopFullDonationRuleService.list(queryWrapper);
                    if(ruleList!=null&&ruleList.size()>0){
                        for (ShopFullDonationRule shopFullDonationRule : ruleList) {
                            showTitleList.add(shopFullDonationRule.getShowTitle());
                        }
                    }
                }
            }
        }

        return showTitleList;
    }

    /**
     * 查询有效的满减满赠活动
     * @param param
     * @return
     */
    @Override
    public List<ShopFullDonationVo> getValidShopFullDonation(ShopFullDonationParam param) {
        return this.baseMapper.getValidShopFullDonation(param);
    }

    /**
     * 验证交叉数据
     * @param dto
     */
    private void vailData(ShopFullDonationDto dto){
        if(dto.getVailFlag()==null||(dto.getVailFlag()!=null&&dto.getVailFlag() != CommonConstants.NUMBER_ONE)){
            LocalDateTime startTime = dto.getStartTime();
            LocalDateTime endTime = dto.getEndTime();
            //查询是否存在时间交叉记录
            LambdaQueryWrapper<ShopFullDonation>wrapper = new LambdaQueryWrapper<>();

            if(dto.getId()!=null){
                wrapper.ne(ShopFullDonation::getId,dto.getId());
            }
            if(dto.getVailFlag()!=null&&dto.getVailFlag() == CommonConstants.NUMBER_TWO){
                wrapper.eq(ShopFullDonation::getStatus,ShopFullDonationStatusFlagEnum.PASS.getType());
            }
            wrapper.and(e->e.ge(ShopFullDonation::getStartTime,startTime).le(ShopFullDonation::getStartTime,endTime).
                    or(e2->e2.ge(ShopFullDonation::getEndTime,startTime).le(ShopFullDonation::getEndTime,endTime)).
                    or(e3->e3.le(ShopFullDonation::getStartTime,startTime).ge(ShopFullDonation::getEndTime,endTime)));
            List<ShopFullDonation> list = this.list(wrapper);
            if(list!=null){
                for (ShopFullDonation shopFullDonation : list) {
                    Boolean vailMemberFlag = false;
                    if(StringUtils.isNotEmpty(shopFullDonation.getMemberLevelIds())){
                        if(StringUtils.isEmpty(dto.getMemberLevelIds())){
                            vailMemberFlag = true;
                        }else{
                            for (String s : dto.getMemberLevelIds().split(",")) {
                                if(shopFullDonation.getMemberLevelIds().contains(s)){
                                    vailMemberFlag = true;
                                }
                            }
                        }
                    }else{
                        vailMemberFlag = true;
                    }
                    if(vailMemberFlag){
                        if(dto.getProductFlag() == ProductFlagEnum.DEFAULT.getType()
                                ||shopFullDonation.getProductFlag() == ProductFlagEnum.DEFAULT.getType()){
                            if(dto.getVailFlag()!=null&&dto.getVailFlag() == CommonConstants.NUMBER_TWO){
                                throw new ServiceException("符合时间、会员等级、商品相同的活动有多条，无法审核通过");
                            }else{
                                throw new ServiceException("符合时间、会员等级、商品相同的活动有多条，确认要提交吗？");
                            }
                        }else{
                            if(dto.getProductFlag() == shopFullDonation.getProductFlag()){
                                List<ShopFullDonationProductDto> shopFullDonationProductList = dto.getShopFullDonationProductList();
                                for (ShopFullDonationProductDto shopFullDonationProductDto : shopFullDonationProductList) {
                                    LambdaQueryWrapper<ShopFullDonationProduct>queryWrapper = new LambdaQueryWrapper<>();
                                    queryWrapper.eq(ShopFullDonationProduct::getMainId,shopFullDonation.getId());
                                    queryWrapper.eq(ShopFullDonationProduct::getSkuId,shopFullDonationProductDto.getSkuId());
                                    int count = shopFullDonationProductService.count(queryWrapper);
                                    if(count>0){
                                        if(dto.getVailFlag()!=null&&dto.getVailFlag() == CommonConstants.NUMBER_TWO){
                                            throw new ServiceException("符合时间、会员等级、商品相同的活动有多条，无法审核通过");
                                        }else{
                                            throw new ServiceException("符合时间、会员等级、商品相同的活动有多条，确认要提交吗？");
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

}
