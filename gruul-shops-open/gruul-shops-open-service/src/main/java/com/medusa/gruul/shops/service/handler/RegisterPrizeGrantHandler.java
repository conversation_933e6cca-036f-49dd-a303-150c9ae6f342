package com.medusa.gruul.shops.service.handler;

import com.medusa.gruul.common.core.util.StringUtil;
import com.medusa.gruul.shops.api.entity.ShopSalePrize;
import com.medusa.gruul.shops.api.enums.SalePrizeEnums;
import com.medusa.gruul.shops.model.PrizeMessage;
import com.medusa.gruul.shops.mq.Sender;
import com.medusa.gruul.shops.service.PrizeGrantStrategy;
import com.medusa.gruul.shops.service.impl.MiniShopSalePrizeServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 注册赠送抽奖机会策略
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class RegisterPrizeGrantHandler implements PrizeGrantStrategy {

    @Autowired
    private MiniShopSalePrizeServiceImpl miniShopSalePrizeService;

    @Autowired
    private Sender sender;

    @Override
    public SalePrizeEnums.GiftType getGiftType() {
        return SalePrizeEnums.GiftType.Register;
    }

    @Override
    public boolean canGrant(PrizeMessage message, ShopSalePrize prize) {
        // 检查活动是否配置了注册赠送
        if (prize.getRegisterNumber() == null || prize.getRegisterNumber() <= 0) {
            log.debug("活动 {} 未配置注册赠送或赠送次数为0", prize.getName());
            return false;
        }
        
        return true;
    }

    @Override
    public void grantPrize(PrizeMessage message, ShopSalePrize prize) {
        try {
            // 赠送指定次数的抽奖机会给新注册用户
            for (int i = 0; i < prize.getRegisterNumber(); i++) {
                miniShopSalePrizeService.addPrizeCountToRedis(
                        -1L, // 订单ID，注册赠送时不需要
                        -1L, // 会员类型ID，注册赠送时不需要
                        prize,
                        message.getShopUserId(),
                        getGiftType()
                );
            }
            
            log.info("用户 {} 注册获得抽奖机会，活动: {}, 赠送次数: {}", 
                    message.getShopUserId(), prize.getName(), prize.getRegisterNumber());
            
            // 如果有父级用户且活动配置了分享赠送，给父级用户发送分享奖励消息
            if (StringUtil.isNotEmpty(message.getParentId()) && 
                prize.getShareNumber() != null && prize.getShareNumber() > 0) {
                
                sendSharePrizeMessage(message, prize);
            }
                    
        } catch (Exception e) {
            log.error("注册赠送抽奖机会失败: 用户={}, 活动={}", 
                    message.getShopUserId(), prize.getName(), e);
            throw e;
        }
    }

    /**
     * 发送分享奖励消息给父级用户
     * 
     * @param message 原注册消息
     * @param prize 抽奖活动
     */
    private void sendSharePrizeMessage(PrizeMessage message, ShopSalePrize prize) {
        try {
            // 创建分享奖励消息
            PrizeMessage shareMessage = PrizeMessage.forShare(
                    message.getTenantId(),
                    message.getShopId(),
                    message.getParentId(), // 分享者（父级用户）
                    message.getShopUserId() // 新用户
            );
            
            // 发送到MQ队列
            sender.sendPrizeMessage(shareMessage);
            
            log.info("已发送分享奖励消息: 分享者={}, 新用户={}, 活动={}", 
                    message.getParentId(), message.getShopUserId(), prize.getName());
                    
        } catch (Exception e) {
            log.error("发送分享奖励消息失败: 分享者={}, 新用户={}, 活动={}", 
                    message.getParentId(), message.getShopUserId(), prize.getName(), e);
            // 分享奖励发送失败不影响注册奖励
        }
    }
}
