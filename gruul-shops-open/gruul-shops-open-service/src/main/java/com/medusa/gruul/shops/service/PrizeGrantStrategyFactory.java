package com.medusa.gruul.shops.service;

import com.medusa.gruul.shops.api.enums.SalePrizeEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 抽奖赠送策略工厂
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class PrizeGrantStrategyFactory implements ApplicationContextAware {

    private Map<SalePrizeEnums.GiftType, PrizeGrantStrategy> strategyMap = new ConcurrentHashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        // 自动收集所有策略实现
        Map<String, PrizeGrantStrategy> strategies = applicationContext.getBeansOfType(PrizeGrantStrategy.class);
        
        strategyMap = strategies.values().stream()
                .collect(Collectors.toMap(
                        PrizeGrantStrategy::getGiftType,
                        Function.identity(),
                        (existing, replacement) -> {
                            log.warn("发现重复的策略实现: {}, 使用: {}", existing.getClass().getSimpleName(), replacement.getClass().getSimpleName());
                            return replacement;
                        }
                ));
        
        log.info("初始化抽奖赠送策略工厂，共加载 {} 个策略: {}", 
                strategyMap.size(), 
                strategyMap.keySet().stream().map(Enum::name).collect(Collectors.joining(", ")));
    }

    /**
     * 根据赠送类型获取对应的策略
     * 
     * @param giftType 赠送类型
     * @return 策略实现
     */
    public PrizeGrantStrategy getStrategy(SalePrizeEnums.GiftType giftType) {
        PrizeGrantStrategy strategy = strategyMap.get(giftType);
        if (strategy == null) {
            log.warn("未找到赠送类型 {} 对应的策略实现", giftType);
        }
        return strategy;
    }

    /**
     * 检查是否存在指定类型的策略
     * 
     * @param giftType 赠送类型
     * @return 是否存在
     */
    public boolean hasStrategy(SalePrizeEnums.GiftType giftType) {
        return strategyMap.containsKey(giftType);
    }

    /**
     * 获取所有已注册的策略类型
     * 
     * @return 策略类型集合
     */
    public java.util.Set<SalePrizeEnums.GiftType> getSupportedTypes() {
        return strategyMap.keySet();
    }
}
