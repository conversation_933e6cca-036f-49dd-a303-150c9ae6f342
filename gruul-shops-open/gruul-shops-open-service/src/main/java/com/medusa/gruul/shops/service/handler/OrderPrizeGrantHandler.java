package com.medusa.gruul.shops.service.handler;

import com.medusa.gruul.common.core.util.StringUtil;
import com.medusa.gruul.shops.api.entity.ShopSalePrize;
import com.medusa.gruul.shops.api.enums.SalePrizeEnums;
import com.medusa.gruul.shops.model.PrizeMessage;
import com.medusa.gruul.shops.service.PrizeGrantStrategy;
import com.medusa.gruul.shops.service.impl.MiniShopSalePrizeServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 订单赠送抽奖机会策略
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class OrderPrizeGrantHandler implements PrizeGrantStrategy {

    @Autowired
    private MiniShopSalePrizeServiceImpl miniShopSalePrizeService;

    @Override
    public SalePrizeEnums.GiftType getGiftType() {
        return SalePrizeEnums.GiftType.Order;
    }

    @Override
    public boolean canGrant(PrizeMessage message, ShopSalePrize prize) {
        // 检查订单ID是否存在
        if (message.getOrderId() == null) {
            log.debug("订单ID为空，跳过订单赠送");
            return false;
        }
        
        // 检查活动是否配置了订单赠送
        if (prize.getOrderNumber() == null || prize.getOrderNumber() <= 0) {
            log.debug("活动 {} 未配置订单赠送或赠送次数为0", prize.getName());
            return false;
        }
        
        // 检查用户是否满足会员类型条件
        if (!checkMemberType(message, prize)) {
            log.debug("用户 {} 不满足活动 {} 的会员类型条件", message.getShopUserId(), prize.getName());
            return false;
        }
        
        return true;
    }

    @Override
    public void grantPrize(PrizeMessage message, ShopSalePrize prize) {
        try {
            // 赠送指定次数的抽奖机会
            for (int i = 0; i < prize.getOrderNumber(); i++) {
                miniShopSalePrizeService.addPrizeCountToRedis(
                        message.getOrderId(),
                        -1L, // 会员等级ID，订单赠送时不需要
                        prize,
                        message.getShopUserId(),
                        getGiftType()
                );
            }
            
            log.info("用户 {} 通过订单 {} 获得抽奖机会，活动: {}, 赠送次数: {}", 
                    message.getShopUserId(), message.getOrderId(), prize.getName(), prize.getOrderNumber());
                    
        } catch (Exception e) {
            log.error("订单赠送抽奖机会失败: 用户={}, 订单={}, 活动={}", 
                    message.getShopUserId(), message.getOrderId(), prize.getName(), e);
            throw e;
        }
    }

    /**
     * 检查用户是否满足会员类型条件
     * 
     * @param message 赠送消息
     * @param prize 抽奖活动
     * @return 是否满足条件
     */
    private boolean checkMemberType(PrizeMessage message, ShopSalePrize prize) {
        // 如果活动没有限制会员类型，则所有用户都可以参与
        if (StringUtil.isEmpty(prize.getMemberTypeIds())) {
            return true;
        }
        
        // 如果消息中没有会员类型ID，则不能参与
        if (message.getMemberTypeId() == null) {
            return false;
        }
        
        // 检查用户的会员类型是否在活动允许的范围内
        String[] allowedTypes = prize.getMemberTypeIds().split(",");
        String userTypeId = message.getMemberTypeId().toString();
        
        for (String allowedType : allowedTypes) {
            if (allowedType.trim().equals(userTypeId)) {
                return true;
            }
        }
        
        return false;
    }
}
