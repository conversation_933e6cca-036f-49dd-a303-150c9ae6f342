package com.medusa.gruul.shops.service;

import com.medusa.gruul.shops.api.entity.ShopSalePrize;
import com.medusa.gruul.shops.api.enums.SalePrizeEnums;
import com.medusa.gruul.shops.model.PrizeMessage;

/**
 * 抽奖赠送策略接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface PrizeGrantStrategy {

    /**
     * 获取策略对应的赠送类型
     * 
     * @return 赠送类型
     */
    SalePrizeEnums.GiftType getGiftType();

    /**
     * 检查是否可以赠送抽奖机会
     * 
     * @param message 赠送消息
     * @param prize 抽奖活动
     * @return 是否可以赠送
     */
    boolean canGrant(PrizeMessage message, ShopSalePrize prize);

    /**
     * 执行赠送抽奖机会
     * 
     * @param message 赠送消息
     * @param prize 抽奖活动
     */
    void grantPrize(PrizeMessage message, ShopSalePrize prize);
}
