package com.medusa.gruul.shops.service.handler;

import com.medusa.gruul.shops.api.entity.ShopSalePrize;
import com.medusa.gruul.shops.api.enums.SalePrizeEnums;
import com.medusa.gruul.shops.model.PrizeMessage;
import com.medusa.gruul.shops.service.PrizeGrantStrategy;
import com.medusa.gruul.shops.service.impl.MiniShopSalePrizeServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 直推赠送抽奖机会策略
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class DirectPrizeGrantHandler implements PrizeGrantStrategy {

    @Autowired
    private MiniShopSalePrizeServiceImpl miniShopSalePrizeService;

    @Override
    public SalePrizeEnums.GiftType getGiftType() {
        return SalePrizeEnums.GiftType.Direct;
    }

    @Override
    public boolean canGrant(PrizeMessage message, ShopSalePrize prize) {
        // 检查会员等级ID是否存在
        if (message.getMemberLevelId() == null) {
            log.debug("会员等级ID为空，跳过直推赠送");
            return false;
        }
        
        // 检查活动是否配置了直推赠送
        if (prize.getDirectMemberQty() == null || prize.getDirectMemberQty() <= 0) {
            log.debug("活动 {} 未配置直推赠送或赠送条件为0", prize.getName());
            return false;
        }
        
        // 检查直推最低会员等级限制
        if (prize.getDirectLowMemberLevelId() != null) {
            if (!checkMemberLevelRequirement(message, prize)) {
                log.debug("用户 {} 不满足活动 {} 的直推会员等级条件", message.getShopUserId(), prize.getName());
                return false;
            }
        }
        
        return true;
    }

    @Override
    public void grantPrize(PrizeMessage message, ShopSalePrize prize) {
        try {
            // 直推赠送通常是赠送1次机会
            miniShopSalePrizeService.addPrizeCountToRedis(-1L,
                    message.getMemberTypeId(), prize, message.getShopUserId(), getGiftType()
            );
            
            log.info("用户 {} 通过直推会员升级获得抽奖机会，活动: {}, 升级等级: {}", 
                    message.getShopUserId(), prize.getName(), message.getMemberLevelId());
                    
        } catch (Exception e) {
            log.error("直推赠送抽奖机会失败: 用户={}, 等级={}, 活动={}", 
                    message.getShopUserId(), message.getMemberLevelId(), prize.getName(), e);
            throw e;
        }
    }

    /**
     * 检查会员等级是否满足直推赠送要求
     * 
     * @param message 赠送消息
     * @param prize 抽奖活动
     * @return 是否满足条件
     */
    private boolean checkMemberLevelRequirement(PrizeMessage message, ShopSalePrize prize) {
        Long requiredLevelId = prize.getDirectLowMemberLevelId();
        Long userLevelId = message.getMemberLevelId();
        
        // 这里需要根据实际的会员等级比较逻辑来实现
        // 假设等级ID越大等级越高，实际情况可能需要查询会员等级表进行比较
        return userLevelId >= requiredLevelId;
    }
}
