package com.medusa.gruul.shops.model;

import com.medusa.gruul.shops.api.enums.SalePrizeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Map;

/**
 * 抽奖赠送消息对象
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@ApiModel(value = "抽奖赠送消息对象")
public class PrizeMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "店铺ID")
    private String shopId;

    @ApiModelProperty(value = "用户ID")
    private String shopUserId;

    @ApiModelProperty(value = "赠送类型")
    private SalePrizeEnums.GiftType giftType;

    @ApiModelProperty(value = "订单ID（订单赠送时使用）")
    private Long orderId;

    @ApiModelProperty(value = "父级用户ID（注册/分享时使用）")
    private String parentId;

    @ApiModelProperty(value = "最后登录日期（登录赠送时使用）")
    private LocalDate lastLoginDate;

    @ApiModelProperty(value = "会员类型ID")
    private Long memberTypeId;

    @ApiModelProperty(value = "会员等级ID（直推赠送时使用）")
    private Long memberLevelId;

    @ApiModelProperty(value = "扩展参数")
    private Map<String, Object> extraParams;

    /**
     * 构造器 - 订单赠送
     */
    public static PrizeMessage forOrder(String tenantId, String shopId, String shopUserId, Long orderId, Long memberTypeId) {
        PrizeMessage message = new PrizeMessage();
        message.setTenantId(tenantId);
        message.setShopId(shopId);
        message.setShopUserId(shopUserId);
        message.setGiftType(SalePrizeEnums.GiftType.Order);
        message.setOrderId(orderId);
        message.setMemberTypeId(memberTypeId);
        return message;
    }

    /**
     * 构造器 - 直推赠送
     */
    public static PrizeMessage forDirect(String tenantId, String shopId, String parentId, Long memberLevelId, Long memberTypeId) {
        PrizeMessage message = new PrizeMessage();
        message.setTenantId(tenantId);
        message.setShopId(shopId);
        message.setShopUserId(parentId);
        message.setGiftType(SalePrizeEnums.GiftType.Direct);
        message.setMemberLevelId(memberLevelId);
        message.setMemberTypeId(memberTypeId);
        return message;
    }

    /**
     * 构造器 - 登录赠送
     */
    public static PrizeMessage forLogin(String tenantId, String shopId, String shopUserId, LocalDate lastLoginDate) {
        PrizeMessage message = new PrizeMessage();
        message.setTenantId(tenantId);
        message.setShopId(shopId);
        message.setShopUserId(shopUserId);
        message.setGiftType(SalePrizeEnums.GiftType.Login);
        message.setLastLoginDate(lastLoginDate);
        return message;
    }

    /**
     * 构造器 - 注册赠送
     */
    public static PrizeMessage forRegister(String tenantId, String shopId, String shopUserId, String parentId) {
        PrizeMessage message = new PrizeMessage();
        message.setTenantId(tenantId);
        message.setShopId(shopId);
        message.setShopUserId(shopUserId);
        message.setGiftType(SalePrizeEnums.GiftType.Register);
        message.setParentId(parentId);
        return message;
    }

    /**
     * 构造器 - 分享赠送
     */
    public static PrizeMessage forShare(String tenantId, String shopId, String parentId, String newUserId) {
        PrizeMessage message = new PrizeMessage();
        message.setTenantId(tenantId);
        message.setShopId(shopId);
        message.setShopUserId(parentId);
        message.setGiftType(SalePrizeEnums.GiftType.Share);
        message.setParentId(newUserId); // 这里存储新用户ID作为关联信息
        return message;
    }
}
