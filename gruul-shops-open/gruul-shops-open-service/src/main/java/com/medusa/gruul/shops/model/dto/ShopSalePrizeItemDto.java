package com.medusa.gruul.shops.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 营销活动抽奖明细参数
 */
@Data
@ApiModel(value = "ShopSalePrizeItemDto对象", description = "营销活动抽奖明细参数")
public class ShopSalePrizeItemDto {

    @ApiModelProperty(value = "主键ID，新增时为空")
    private Long id;

    @ApiModelProperty(value = "主表ID，新增时为空")
    private Long mainId;

    @ApiModelProperty(value = "奖项名称", required = true)
    @NotBlank(message = "奖项名称不能为空")
    private String levelName;

    @ApiModelProperty(value = "排序", required = true)
    @NotNull(message = "排序不能为空")
    private Integer seqBy;

    @ApiModelProperty(value = "是否间隔奖项：0-否，1-是", required = true)
    @NotNull(message = "是否间隔奖项不能为空")
    private Integer spacingFlag;

    @ApiModelProperty(value = "是否奖品：0-否，1-是", required = true)
    @NotNull(message = "是否奖品不能为空")
    private Integer prizeFlag;

    @ApiModelProperty(value = "奖品发放方式：1-线上邮寄，2-线下核销", required = true)
    @NotNull(message = "奖品发放方式不能为空")
    private Integer verifyType;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @ApiModelProperty(value = "奖品有效期开始时间")
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @ApiModelProperty(value = "奖品有效期结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "奖品类型：1-商城商品，2-优惠券")
    private Integer prizeType;

    @ApiModelProperty(value = "商品ID")
    private Long productId;

    @ApiModelProperty(value = "商品规格ID")
    private Long skuId;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "优惠券ID")
    private Long couponId;

    @ApiModelProperty(value = "奖品名称")
    private String prizeName;

    @ApiModelProperty(value = "奖项图片")
    private String levelPic;

    @ApiModelProperty(value = "中奖概率", required = true)
    @NotNull(message = "中奖概率不能为空")
    @DecimalMin(value = "0", message = "中奖概率必须大于等于0")
    @DecimalMax(value = "1", message = "中奖概率必须小于等于1")
    private BigDecimal winningRate;

    @ApiModelProperty(value = "中奖参与人次", required = true)
    @NotNull(message = "中奖参与人次不能为空")
    private Integer winningPersonTimes;
} 