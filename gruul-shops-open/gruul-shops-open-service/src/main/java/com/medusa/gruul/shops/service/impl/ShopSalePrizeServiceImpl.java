package com.medusa.gruul.shops.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.common.core.constant.enums.ApproveStatusEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.*;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.enums.ProductStatusEnum;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.vo.manager.ProductVo;
import com.medusa.gruul.shops.api.entity.ShopCoupon;
import com.medusa.gruul.shops.api.entity.ShopSalePrize;
import com.medusa.gruul.shops.api.entity.ShopSalePrizeItem;
import com.medusa.gruul.shops.api.entity.ShopSalePrizeProduct;
import com.medusa.gruul.shops.api.enums.SalePrizeEnums;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import com.medusa.gruul.shops.mapper.ShopSalePrizeItemMapper;
import com.medusa.gruul.shops.mapper.ShopSalePrizeMapper;
import com.medusa.gruul.shops.mapper.ShopSalePrizeProductMapper;
import com.medusa.gruul.shops.model.dto.ShopSalePrizeAuditDto;
import com.medusa.gruul.shops.model.dto.ShopSalePrizeDto;
import com.medusa.gruul.shops.model.dto.ShopSalePrizeItemDto;
import com.medusa.gruul.shops.model.dto.ShopSalePrizeProductDto;
import com.medusa.gruul.shops.model.param.ShopSalePrizeQueryParam;
import com.medusa.gruul.shops.model.param.ShopSalePrizeItemQueryParam;
import com.medusa.gruul.shops.model.vo.ShopSalePrizeDetailVo;
import com.medusa.gruul.shops.model.vo.ShopSalePrizeItemVo;
import com.medusa.gruul.shops.model.vo.ShopSalePrizeItemDetailVo;
import com.medusa.gruul.shops.model.vo.ShopSalePrizeProductVo;
import com.medusa.gruul.shops.model.vo.ShopSalePrizeVo;
import com.medusa.gruul.shops.model.vo.ShopSalePrizeSummaryVo;
import com.medusa.gruul.shops.service.IShopCouponService;
import com.medusa.gruul.shops.service.IShopSalePrizeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 营销活动抽奖表 服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class ShopSalePrizeServiceImpl extends ServiceImpl<ShopSalePrizeMapper, ShopSalePrize> implements IShopSalePrizeService {

    @Resource
    private ShopSalePrizeProductMapper shopSalePrizeProductMapper;

    @Resource
    private ShopSalePrizeItemMapper shopSalePrizeItemMapper;

    @Autowired
    private RemoteMiniAccountService remoteMiniAccountService;
    @Autowired
    private RemoteGoodsService remoteGoodsService;
    @Autowired
    private IShopCouponService shopCouponService;


    @Override
    public IPage<ShopSalePrizeVo> page(ShopSalePrizeQueryParam param) {
        // 先检查并更新过期活动的状态
        this.checkAndUpdateExpiredStatus();

        // 验证排序参数
        this.validateSortParams(param);

        IPage<ShopSalePrize> page = new Page<>(param.getCurrent(), param.getSize());
        IPage<ShopSalePrizeVo> pageResult = this.baseMapper.getPage(page, param);

        return pageResult;
    }

    @Override
    public IPage<ShopSalePrizeItemDetailVo> itemPage(ShopSalePrizeItemQueryParam param) {
        IPage<ShopSalePrize> page = new Page<>(param.getCurrent(), param.getSize());
        IPage<ShopSalePrizeItemDetailVo> pageResult = this.baseMapper.getItemPage(page, param);

        return pageResult;
    }

    @Override
    public IPage<ShopSalePrizeSummaryVo> summaryPage(ShopSalePrizeItemQueryParam param) {
        IPage<ShopSalePrize> page = new Page<>(param.getCurrent(), param.getSize());
        IPage<ShopSalePrizeSummaryVo> pageResult = this.baseMapper.getSummaryPage(page, param);

        return pageResult;
    }

    @Override
    public Result<ShopSalePrizeDetailVo> detail(Long id) {
        // 获取活动基本信息
        ShopSalePrize shopSalePrize = this.getById(id);
        if (shopSalePrize == null) {
            return Result.failed("活动不存在");
        }

        // 转换为VO
        ShopSalePrizeDetailVo detailVo = new ShopSalePrizeDetailVo();
        BeanUtils.copyProperties(shopSalePrize, detailVo);

        if (SalePrizeEnums.ProductFlat.YES.getCode().equals(shopSalePrize.getProductFlag())){
            // 获取指定参与商品列表
            List<ShopSalePrizeProductVo> productList = shopSalePrizeProductMapper.getListByMainId(id);
            detailVo.setProductList(productList);
        }

        // 获取奖项列表
        List<ShopSalePrizeItemVo> prizeItemList = shopSalePrizeItemMapper.getListByMainId(id);
        detailVo.setPrizeItemList(prizeItemList);

        return Result.ok(detailVo);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public  Result<Boolean> batchCopy(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException("活动ID不能为空",SystemCode.PARAM_MISS.getCode());
        }

        List<ShopSalePrize> prizeList = this.baseMapper.selectBatchIds(ids);
        for (ShopSalePrize source : prizeList) {
            if (source == null) {
                throw new ServiceException("活动不存在",SystemCode.DATA_NOT_EXIST.getCode());
            }
            Long id = source.getId();
            // 构建ShopSalePrizeDto对象
            ShopSalePrizeDto dto = new ShopSalePrizeDto();
            BeanUtils.copyProperties(source, dto);
            dto.setId(null);
            dto.setName(source.getName() + "-复制");
            log.info("营销活动主信息准备完成，源活动ID: {}，新活动名称: {}", id, dto.getName());

            // 复制关联商品信息
            if (SalePrizeEnums.ProductFlat.YES.getCode().equals(source.getProductFlag())) {
                LambdaQueryWrapper<ShopSalePrizeProduct> productWrapper = new LambdaQueryWrapper<>();
                productWrapper.eq(ShopSalePrizeProduct::getMainId, id);
                List<ShopSalePrizeProduct> sourceProducts = shopSalePrizeProductMapper.selectList(productWrapper);
                if (!CollectionUtils.isEmpty(sourceProducts)) {
                    List<ShopSalePrizeProductDto> productDtoList = new ArrayList<>();
                    for (ShopSalePrizeProduct sourceProduct : sourceProducts) {
                        ShopSalePrizeProductDto productDto = new ShopSalePrizeProductDto();
                        BeanUtils.copyProperties(sourceProduct, productDto);
                        productDto.setId(null);
                        productDto.setMainId(null);
                        productDtoList.add(productDto);
                    }
                    dto.setProductList(productDtoList);
                }
            }

            // 复制关联奖项信息
            LambdaQueryWrapper<ShopSalePrizeItem> itemWrapper = new LambdaQueryWrapper<>();
            itemWrapper.eq(ShopSalePrizeItem::getMainId, id);
            List<ShopSalePrizeItem> sourceItems = shopSalePrizeItemMapper.selectList(itemWrapper);
            if (!CollectionUtils.isEmpty(sourceItems)) {
                List<ShopSalePrizeItemDto> itemDtoList = new ArrayList<>();
                for (ShopSalePrizeItem sourceItem : sourceItems) {
                    ShopSalePrizeItemDto itemDto = new ShopSalePrizeItemDto();
                    BeanUtils.copyProperties(sourceItem, itemDto);
                    itemDto.setId(null);
                    itemDto.setMainId(null);
                    itemDtoList.add(itemDto);
                }
                dto.setPrizeItemList(itemDtoList);
            }

            ShopSalePrizeVo vo = saveOrUpdatePrize(dto, SalePrizeEnums.Status.STATUS_DRAFT.getCode(),null);
            log.info("营销活动复制成功，源活动ID: {}，新活动ID: {}，新活动名称: {}", id, vo.getId(), vo.getName());
        }
        return Result.ok(true);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopSalePrizeVo saveDraft(ShopSalePrizeDto param) {
        validateSubmitData(param,  true);
        return saveOrUpdatePrize(param,SalePrizeEnums.Status.STATUS_DRAFT.getCode(),null);
    }

    @Override
    public ShopSalePrizeVo edit(ShopSalePrizeDto param) {
        validateSubmitData(param,  true);
        ShopSalePrize salePrize = this.getById(param.getId());
        if (salePrize == null) {
            return null;
        }
        return saveOrUpdatePrize(param,salePrize.getStatus(),salePrize.getAuditStatus());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> deleteById(Long id) {
        // 检查活动是否存在
        ShopSalePrize prize = this.getById(id);
        if (prize == null) {
            return Result.failed("活动不存在");
        }

        // 检查活动状态是否为草稿
        if (!(SalePrizeEnums.Status.STATUS_DRAFT.getCode().equals(prize.getStatus()))
                ) {
            return Result.failed("只有草稿状态的活动才能删除");
        }

        // 删除活动及相关数据
        boolean result = SpringContextHolder.getBean(IShopSalePrizeService.class).removeById(id);
        if (result) {
            deleteByMainId(id);
        }

        return Result.ok(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopSalePrizeVo submit(ShopSalePrizeDto param) {
        // 校验提交审核的数据
        validateSubmitData(param,false);
        return saveOrUpdatePrize(param, SalePrizeEnums.Status.STATUS_NOT_RELEASED.getCode(),null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> audit(ShopSalePrizeAuditDto param) {

        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (null ==curUser){
            throw new ServiceException("找不到用户信息",SystemCode.UNAUTHORIZED.getCode());
        }
        String userId = curUser.getUserId();
        for (Long id : param.getIds()) {
            // 检查活动是否存在
            ShopSalePrize prize = this.getById(id);

            if (prize == null) {
                return Result.failed("活动不存在");
            }
            // 检查活动状态是否为待审核
            if (!(SalePrizeEnums.AuditStatus.STATUS_PENDING.getCode().equals(prize.getAuditStatus()))) {
                return Result.failed("只有待审核状态的活动才能进行审核");
            }

            // 更新活动状态
            ShopSalePrize entity = new ShopSalePrize();
            if (param.getAuditStatus().equals(SalePrizeEnums.AuditStatus.STATUS_APPROVED.getCode())){

                // 检查时间段是否与已生效的活动重叠
                if (hasTimeOverlap(prize.getStartTime(), prize.getEndTime(), id)) {
                    return Result.failed("活动生效期有重复活动，请重新选择活动时间");
                }
                entity.setStatus(SalePrizeEnums.Status.STATUS_RELEASED.getCode());
            }else {
                entity.setStatus(SalePrizeEnums.Status.STATUS_DRAFT.getCode());
            }



            BeanUtils.copyProperties(param, entity);
            entity.setId(id);
            entity.setAuditTime(LocalDateTime.now());
            entity.setAuditPlatformUserId(Long.valueOf(userId));
            entity.setAuditPlatformUserName(curUser.getNikeName());

            // 更新审核状态
            boolean update = SpringContextHolder.getBean(IShopSalePrizeService.class).updateById(entity);
            if (! update){
                throw new ServiceException("更新失败",SystemCode.DATA_EXPIRED.getCode());
            }
        }

        return   Result.ok(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> stop(Long id) {
        // 检查活动是否存在
        ShopSalePrize prize = this.getById(id);
        if (prize == null) {
            return Result.failed("活动不存在");
        }

        // 检查活动状态是否为审核通过
        if (!(SalePrizeEnums.AuditStatus.STATUS_APPROVED.getCode().equals(prize.getAuditStatus()))) {
            return Result.failed("只有审核通过状态的活动才能停止");
        }

        // 更新为停止状态
        LambdaUpdateWrapper<ShopSalePrize> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ShopSalePrize::getId, id)
                .set(ShopSalePrize::getStatus, SalePrizeEnums.Status.STATUS_STOPPED.getCode());

        boolean result = SpringContextHolder.getBean(IShopSalePrizeService.class).update(updateWrapper);
        return Result.ok(result);
    }


    private ShopSalePrizeVo saveOrUpdatePrize(ShopSalePrizeDto param,int status,Integer auditStatus) {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (null == curUser){
            throw new ServiceException("找不到用户信息",SystemCode.UNAUTHORIZED.getCode());
        }
        String shopUserId = curUser.getUserId();
        String userName = curUser.getNikeName();
        ShopSalePrize prize = new ShopSalePrize();
        BeanUtils.copyProperties(param, prize);
        //设置状态
        prize.setStatus(status);
        if (null == auditStatus){
            prize.setAuditStatus(SalePrizeEnums.AuditStatus.STATUS_PENDING.getCode());
        }else {
            prize.setAuditStatus(auditStatus);
        }

        boolean isUpdate = param.getId()!= null;
        if (isUpdate) {
            // 更新
            ShopSalePrize existPrize = this.getById(param.getId());
            if (existPrize == null) {
               throw new ServiceException("活动不存在");
            }

            // 检查活动状态是否允许编辑
            if (!(SalePrizeEnums.Status.STATUS_DRAFT.getCode().equals(existPrize.getStatus()))
                    && !(SalePrizeEnums.AuditStatus.STATUS_REJECTED.getCode().equals(existPrize.getAuditStatus()))) {
               throw new ServiceException("只有草稿或审核不通过状态的活动才能编辑");
            }

            prize.setLastModifyUserId(Long.valueOf(shopUserId));
            prize.setLastModifyUserName(userName);
            SpringContextHolder.getBean(IShopSalePrizeService.class).updateById(prize);

            // 删除原有的商品和奖项
            deleteByMainId(param.getId());
        } else {
            // 新增活动
            prize.setCreateUserId(Long.valueOf(shopUserId));
            prize.setCreateUserName(userName);
            if (prize.getBillDate()==null){
                prize.setBillDate(LocalDate.now());
            }
            SpringContextHolder.getBean(IShopSalePrizeService.class).save(prize);
        }

        // 保存指定商品列表
        if (SalePrizeEnums.ProductFlat.YES.getCode().equals(param.getProductFlag())
                && !CollectionUtils.isEmpty(param.getProductList())) {
            List<ShopSalePrizeProduct> productList = param.getProductList().stream().map(productParam -> {
                ShopSalePrizeProduct product = new ShopSalePrizeProduct();
                BeanUtils.copyProperties(productParam, product, "id","mainId");
                product.setShopId(prize.getShopId());
                product.setMainId(prize.getId());
                product.setShopId(prize.getShopId());
                product.setCreateUserId(Long.valueOf(shopUserId));
                product.setLastModifyUserId(Long.valueOf(shopUserId));
                product.setLastModifyUserName(userName);
                product.setCreateUserName(userName);
                return product;
            }).collect(Collectors.toList());

            for (ShopSalePrizeProduct product : productList) {
                shopSalePrizeProductMapper.insert(product);
            }
        }

        // 保存奖项列表
        if (!CollectionUtils.isEmpty(param.getPrizeItemList())) {
            List<ShopSalePrizeItem> itemList = param.getPrizeItemList().stream().map(itemParam -> {
                ShopSalePrizeItem item = new ShopSalePrizeItem();
                BeanUtils.copyProperties(itemParam, item,"id","mainId");
                item.setShopId(prize.getShopId());
                item.setMainId(prize.getId());
                item.setShopId(prize.getShopId());
                item.setCreateUserId(Long.valueOf(shopUserId));
                item.setLastModifyUserId(Long.valueOf(shopUserId));
                item.setLastModifyUserName(userName);
                item.setCreateUserName(userName);
                return item;
            }).collect(Collectors.toList());

            for (ShopSalePrizeItem item : itemList) {
                shopSalePrizeItemMapper.insert(item);
            }
        }

        //构建返回对象
        ShopSalePrizeVo vo = new ShopSalePrizeVo();
        BeanUtils.copyProperties(prize, vo);
        return vo;
    }
    /**
     * 验证排序参数
     */
    private void validateSortParams(ShopSalePrizeQueryParam param) {
        if (StringUtil.isNotBlank(param.getSortOrder())) {
            // 验证排序方向是否有效
            String sortOrder = param.getSortOrder().toLowerCase();
            if (!"asc".equals(sortOrder) && !"desc".equals(sortOrder)) {
                // 如果排序方向无效，设置为默认值
                param.setSortOrder("desc");
            }
        }
    }

    /**
     * 校验提交审核的数据
     */
    private void validateSubmitData(ShopSalePrizeDto param,boolean isDraft) {
        // 校验活动名称是否重复
        LambdaQueryWrapper<ShopSalePrize> queryWrapper = new LambdaQueryWrapper<>();
        if (param.getId() != null) {
            queryWrapper.ne(ShopSalePrize::getId, param.getId());
        }

        queryWrapper.eq(ShopSalePrize::getName, param.getName());
        if (StringUtil.isBlank(param.getShopId())){
            param.setShopId(ShopContextHolder.getShopId());
        }
        int count = this.count(queryWrapper);
        if (count > 0) {
            throw new ServiceException("活动名称已存在");
        }
        // 如果是草稿就不需要校验其他数据
        if (isDraft){
            return;
        }
        // 校验指定参与商品
        if (SalePrizeEnums.ProductFlat.YES.getCode().equals(param.getProductFlag())
                && CollectionUtils.isEmpty(param.getProductList())) {
            throw new ServiceException( "指定参与商品时，商品列表不能为空");
        }

        if (param.getStartTime() == null || param.getEndTime() == null){
            throw new ServiceException( "活动时间段不能为空");
        }

        if (param.getEndTime().isBefore(LocalDateTime.now())){
            throw new ServiceException( "活动结束时间不能早于当前时间");
        }


        // 校验奖项
        if (CollectionUtils.isEmpty(param.getPrizeItemList())) {
            throw new ServiceException( "奖项列表不能为空");
        }

        // 校验奖品类型
        for (ShopSalePrizeItemDto itemParam : param.getPrizeItemList()) {
            String levelName = itemParam.getLevelName();
            if (StringUtil.isBlank(levelName)) {
                throw new ServiceException( "奖项名称不能为空");
            }
            if (itemParam.getPrizeFlag() != null && itemParam.getPrizeFlag() == 1) {
                // 如果是奖品，校验奖品类型
                if (itemParam.getPrizeType() != null) {
                    // 商城商品 todo 检查商铺Id 优惠价Id是否存在？
                    if (itemParam.getPrizeType() == 1) {
                        if ((itemParam.getProductId() == null || itemParam.getSkuId() == null)){
                            throw new ServiceException("选择商城商品时，商品信息不能为空");
                        }
                        ProductVo productBySkuId = remoteGoodsService.findProductBySkuId(itemParam.getSkuId());
                        if (productBySkuId == null){
                            throw new ServiceException("商品不存在");
                        } else if (productBySkuId.getStatus() != ProductStatusEnum.SELL_ON.getStatus() ) {
                            throw new ServiceException("未上架的商品:"+productBySkuId.getName());
                        }
                    }
                    // 优惠券
                    if (itemParam.getPrizeType() == 2 ) {
                        if (itemParam.getCouponId() == null){
                            throw new ServiceException("选择优惠券时，优惠券ID不能为空");
                        }
                        ShopCoupon coupon = shopCouponService.getById(itemParam.getCouponId());
                        if (coupon == null){
                            throw new ServiceException("优惠券不存在");
                        } else if (coupon.getStatus()!= ApproveStatusEnum.APPROVED.getStatus()) {
                            throw new ServiceException("不是生效中的优惠券:"+coupon.getCouponName());
                        }
                    }
                }
            }
            if (SalePrizeEnums.VerifyType.OFFLINE_VERIFY.getCode().equals(itemParam.getVerifyType())){
                if (itemParam.getStartTime()== null || itemParam.getEndTime()==null){
                    throw new ServiceException("线下核销时，有限期时间段不能为空");
                } else if (itemParam.getStartTime().isAfter(itemParam.getEndTime())) {
                    throw new ServiceException("线下核销时，有限期时间段开始时间不应在结束时间之后");
                }
            }
        }
    }
    /**
     * 检查时间段是否与已生效的活动重叠
     */
    private boolean hasTimeOverlap(LocalDateTime startTime, LocalDateTime endTime, Long excludeId) {
        LambdaQueryWrapper<ShopSalePrize> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShopSalePrize::getStatus, SalePrizeEnums.Status.STATUS_RELEASED.getCode())
                .eq(ShopSalePrize::getAuditStatus, SalePrizeEnums.AuditStatus.STATUS_APPROVED.getCode());

        // 排除当前活动
        if (excludeId != null) {
            queryWrapper.ne(ShopSalePrize::getId, excludeId);
        }

        // 时间段重叠检查：新活动开始时间 < 现有活动结束时间  新活动结束时间 > 现有活动开始时间
        queryWrapper.and(wrapper -> wrapper
                .lt(ShopSalePrize::getStartTime, endTime)
                .gt(ShopSalePrize::getEndTime, startTime)
        );

        return this.count(queryWrapper) > 0;
    }

    /**
     * 更新超时活动为失效
     */
    public void checkAndUpdateExpiredStatus() {
        LocalDateTime now = LocalDateTime.now();
        int count = this.baseMapper.updateStatusToExpired(now);
        if (count > 0) {
            log.info("更新了{}个过期活动状态为失效", count);
        }
    }

    /**
     * 删除子表
     */
    private void deleteByMainId(Long id) {
        LambdaQueryWrapper<ShopSalePrizeProduct> productWrapper = new LambdaQueryWrapper<>();
        productWrapper.eq(ShopSalePrizeProduct::getMainId, id);
        shopSalePrizeProductMapper.delete(productWrapper);

        LambdaQueryWrapper<ShopSalePrizeItem> itemWrapper = new LambdaQueryWrapper<>();
        itemWrapper.eq(ShopSalePrizeItem::getMainId, id);
        shopSalePrizeItemMapper.delete(itemWrapper);
    }
} 