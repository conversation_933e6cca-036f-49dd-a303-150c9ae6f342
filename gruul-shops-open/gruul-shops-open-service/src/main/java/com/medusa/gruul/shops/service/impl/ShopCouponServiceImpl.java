package com.medusa.gruul.shops.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.api.model.vo.ChooseAccountVo;
import com.medusa.gruul.account.api.model.vo.ChooseTagVo;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.TimeConstants;
import com.medusa.gruul.common.core.constant.enums.ApproveStatusEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.entity.ShowCategory;
import com.medusa.gruul.goods.api.entity.SkuStock;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.vo.manager.CategoryVo;
import com.medusa.gruul.goods.api.model.vo.manager.ProductVo;
import com.medusa.gruul.shops.api.conf.ShopsRedis;
import com.medusa.gruul.shops.api.constant.ShopsRedisKey;
import com.medusa.gruul.shops.api.entity.*;
import com.medusa.gruul.shops.api.enums.CouponTypeEnum;
import com.medusa.gruul.shops.api.enums.ExpiredTypeEnum;
import com.medusa.gruul.shops.api.enums.GrantTypeEnum;
import com.medusa.gruul.shops.api.enums.ProhibitStatusEnum;
import com.medusa.gruul.shops.api.model.SendCouponMessage;
import com.medusa.gruul.shops.mapper.ShopCouponMapper;
import com.medusa.gruul.shops.model.dto.*;
import com.medusa.gruul.shops.model.param.ShopCouponParam;
import com.medusa.gruul.shops.model.param.ShopPassTicketPartnerParam;
import com.medusa.gruul.shops.model.vo.*;
import com.medusa.gruul.shops.mq.Sender;
import com.medusa.gruul.shops.service.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:33 2024/8/23
 */
@Service(value = "shopCouponServiceImpl")
public class ShopCouponServiceImpl extends ServiceImpl<ShopCouponMapper, ShopCoupon> implements IShopCouponService {

    @Autowired
    private ShopsPartnerService shopsPartnerService;
    @Autowired
    private IShopCouponPartnerService shopCouponPartnerService;
    @Autowired
    private RemoteMiniAccountService remoteMiniAccountService;
    @Autowired
    private RemoteGoodsService remoteGoodsService;
    @Autowired
    private IShopCouponProductService shopCouponProductService;
    @Autowired
    private IShopCouponCategoryService shopCouponCategoryService;
    @Autowired
    private IShopCouponAccountService shopCouponAccountService;
    @Autowired
    private Sender sender;
    @Override
    public IPage pageList(ShopCouponParam shopCouponParam) {
        IPage<ShopCouponVo> shopCouponVoIPage = this.baseMapper.queryList(new Page(shopCouponParam.getCurrent(), shopCouponParam.getSize()), shopCouponParam);
        return shopCouponVoIPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopCoupon add(ShopCouponDto shopCouponDto) {


        //判断当前是否主店铺
        ShopsPartner shop = shopsPartnerService.getShopsPartner();
        if(shop == null){
            throw new ServiceException("店铺为空！");
        }

        Integer mainFlag = shop.getMainFlag();
        //非主店铺
        if(mainFlag == CommonConstants.NUMBER_ZERO){
            //设为指定商家
            shopCouponDto.setShopFlag(Boolean.TRUE);
            List<ShopCouponPartnerDto>List = new ArrayList<>();
            ShopCouponPartnerDto shopCouponPartnerDto = new ShopCouponPartnerDto();
            shopCouponPartnerDto.setShopsPartnerId(shop.getPartnerId());
            List.add(shopCouponPartnerDto);
            shopCouponDto.setPartnerList(List);
        }



        ShopCoupon shopCoupon = new ShopCoupon();
        BeanUtils.copyProperties(shopCouponDto, shopCoupon);
        shopCoupon.setStatus(ApproveStatusEnum.AUDIT.getStatus());
        shopCoupon.setApprovedStatus(ApproveStatusEnum.AUDIT.getStatus());
        //保存主表信息
        this.save(shopCoupon);
        Long id = shopCoupon.getId();

        List<ShopCouponPartnerDto> partnerDtoList = shopCouponDto.getPartnerList();
        List<ShopCouponPartner> partnerList = new ArrayList<ShopCouponPartner>();

        //将ShopCouponPartnerDto转成ShopCouponPartner
        List<Long> partnerIdList = new ArrayList<>();
        if(partnerDtoList!=null&&partnerDtoList.size()>0){
            for(ShopCouponPartnerDto partnerDto : partnerDtoList){

                if(partnerIdList.contains(partnerDto.getShopsPartnerId())){
                    ShopsPartner shopsPartner =  shopsPartnerService.getById(partnerDto.getShopsPartnerId());
                    throw new ServiceException("重复选择了商家[" + shopsPartner.getName() + "]");
                }else{
                    partnerIdList.add(partnerDto.getShopsPartnerId());
                }
                partnerDto.setCouponId(id);
                ShopCouponPartner partner = new ShopCouponPartner();
                BeanUtils.copyProperties(partnerDto, partner);
                partnerList.add(partner);
            }
        }
        shopCouponPartnerService.saveBatch(partnerList);

        if(shopCouponDto.getCouponType()!=null&&shopCouponDto.getCouponType() == CouponTypeEnum.PRODUCTS.getType()){
            List<ShopCouponProductDto> productDtoList = shopCouponDto.getProducts();
            List<ShopCouponProduct> productList = new ArrayList<ShopCouponProduct>();
            //将ShopCouponProductDto转成ShopCouponProduct
            List<String> productIdList = new ArrayList<>();
            if(productDtoList!=null&&productDtoList.size()>0){
                for (ShopCouponProductDto productDto : productDtoList) {
                    String key = productDto.getProductId() + "-" + productDto.getSkuId();
                    if(productIdList.contains(key)){
                        ProductVo product = remoteGoodsService.findProductById(Long.valueOf(productDto.getProductId()));
                        SkuStock skuStock = remoteGoodsService.findSkuStockById(Long.valueOf(productDto.getSkuId()));
                        throw new ServiceException("重复选择了商品[" + product.getName() + "]，规格["+skuStock.getSpecs()+"]");
                    }else{
                        productIdList.add(key);
                    }
                    productDto.setCouponId(String.valueOf(id));
                    ShopCouponProduct product = new ShopCouponProduct();
                    BeanUtils.copyProperties(productDto, product);
                    productList.add(product);
                }
            }
            shopCouponProductService.saveBatch(productList);
        }
        if(shopCouponDto.getCouponType()!=null&&shopCouponDto.getCouponType() == CouponTypeEnum.CATEGORYS.getType()){
            List<ShopCouponCategoryDto> categoryDtoList = shopCouponDto.getCategorys();
            List<ShopCouponCategory> categoryList = new ArrayList<ShopCouponCategory>();
            //将ShopCouponCategoryDto转成ShopCouponCategory
            List<String> categoryIdList = new ArrayList<>();
            if(categoryDtoList!=null&&categoryDtoList.size()>0){
                for (ShopCouponCategoryDto categoryDto : categoryDtoList) {
                    String key = categoryDto.getCategoryId();
                    if(categoryIdList.contains(key)){
                        CategoryVo categoryVo = remoteGoodsService.findCategoryById(categoryDto.getCategoryId());
                        throw new ServiceException("重复选择了品类[" + categoryVo.getCategoryName() + "]");
                    }else{
                        categoryIdList.add(key);
                    }
                    categoryDto.setCouponId(String.valueOf(id));
                    ShopCouponCategory category = new ShopCouponCategory();
                    BeanUtils.copyProperties(categoryDto, category);
                    categoryList.add(category);
                }
            }
            shopCouponCategoryService.saveBatch(categoryList);
        }
        if(shopCouponDto.getGrantType()!=null&&shopCouponDto.getGrantType() == GrantTypeEnum.TAG.getType()){
            List<ShopCouponAccountDto> accountDtoList = shopCouponDto.getAccounts();
            List<ShopCouponAccount> accountList = new ArrayList<ShopCouponAccount>();
            //将ShopCouponAccountDto转成ShopCouponAccount
            List<String> accountIdList = new ArrayList<>();

            if(accountDtoList!=null&&accountDtoList.size()>0){
                for (ShopCouponAccountDto accountDto : accountDtoList) {
                    String key = accountDto.getSourceId();
                    if(accountIdList.contains(key)){
                        ChooseTagVo chooseTagVo = remoteMiniAccountService.getChooseTagById(accountDto.getSourceId());
                        throw new ServiceException("重复选择了标签[" + chooseTagVo.getTagName() + "]");
                    }else{
                        accountIdList.add(key);
                    }
                    accountDto.setCouponId(String.valueOf(id));
                    ShopCouponAccount account = new ShopCouponAccount();
                    BeanUtils.copyProperties(accountDto, account);
                    accountList.add(account);
                }
            }
            shopCouponAccountService.saveBatch(accountList);
        }
        if(shopCouponDto.getGrantType()!=null&&shopCouponDto.getGrantType() == GrantTypeEnum.MEMBER.getType()){
            List<ShopCouponAccountDto> accountDtoList = shopCouponDto.getAccounts();
            List<ShopCouponAccount> accountList = new ArrayList<ShopCouponAccount>();
            //将ShopCouponAccountDto转成ShopCouponAccount
            if(accountDtoList!=null&&accountDtoList.size()>0){
                for (ShopCouponAccountDto accountDto : accountDtoList) {
                    accountDto.setCouponId(String.valueOf(id));
                    ShopCouponAccount account = new ShopCouponAccount();
                    BeanUtils.copyProperties(accountDto, account);
                    accountList.add(account);
                }
            }
            shopCouponAccountService.saveBatch(accountList);
        }
        if(shopCouponDto.getGrantType()!=null&&shopCouponDto.getGrantType() == GrantTypeEnum.APPOINT.getType()){
            List<ShopCouponAccountDto> accountDtoList = shopCouponDto.getAccounts();
            List<ShopCouponAccount> accountList = new ArrayList<ShopCouponAccount>();
            //将ShopCouponAccountDto转成ShopCouponAccount
            List<String> accountIdList = new ArrayList<>();

            if(accountDtoList!=null&&accountDtoList.size()>0){
                for (ShopCouponAccountDto accountDto : accountDtoList) {
                    String key = accountDto.getSourceId();
                    if(accountIdList.contains(key)){
                        ChooseAccountVo chooseAccountVo = remoteMiniAccountService.getChooseAccountByUserId(accountDto.getSourceId());
                        throw new ServiceException("重复选择了客户[" + chooseAccountVo.getNikeName() + "]");
                    }else{
                        accountIdList.add(key);
                    }
                    accountDto.setCouponId(String.valueOf(id));
                    ShopCouponAccount account = new ShopCouponAccount();
                    BeanUtils.copyProperties(accountDto, account);
                    accountList.add(account);
                }
            }
            shopCouponAccountService.saveBatch(accountList);
        }
        return shopCoupon;
    }

    @Override
    public ShopCouponVo queryById(Long id) {
        ShopCoupon shopCoupon = this.getById(id);
        ShopCouponVo shopCouponVo = new ShopCouponVo();
        BeanUtils.copyProperties(shopCoupon,shopCouponVo);
        LambdaQueryWrapper<ShopCouponPartner> partnerLambdaQueryWrapper = new LambdaQueryWrapper<>();
        partnerLambdaQueryWrapper.eq(ShopCouponPartner::getCouponId, id);
        List<ShopCouponPartner> partnerList = this.shopCouponPartnerService.list(partnerLambdaQueryWrapper);
        shopCouponVo.setPartnerList(partnerList);

        LambdaQueryWrapper<ShopCouponProduct> productLambdaQueryWrapper = new LambdaQueryWrapper<>();
        productLambdaQueryWrapper.eq(ShopCouponProduct::getCouponId,id);
        List<ShopCouponProduct> productList = this.shopCouponProductService.list(productLambdaQueryWrapper);
        List<ShopCouponProductVo>products = new ArrayList<>();
        for (ShopCouponProduct shopCouponProduct : productList) {
            ShopCouponProductVo shopCouponProductVo = new ShopCouponProductVo();
            BeanUtils.copyProperties(shopCouponProduct,shopCouponProductVo);
            ProductVo productVo = remoteGoodsService.findProductById(Long.valueOf(shopCouponProduct.getProductId()));
            SkuStock skuStock = remoteGoodsService.findSkuStockById(Long.valueOf(shopCouponProduct.getSkuId()));
            if(productVo!=null){
                shopCouponProductVo.setProductName(productVo.getName());
            }
            if(skuStock!=null){
                shopCouponProductVo.setSpecs(skuStock.getSpecs());
                shopCouponProductVo.setPrice(skuStock.getPrice());
            }
            products.add(shopCouponProductVo);
        }
        shopCouponVo.setProducts(products);

        LambdaQueryWrapper<ShopCouponCategory>categoryLambdaQueryWrapper = new LambdaQueryWrapper<>();
        categoryLambdaQueryWrapper.eq(ShopCouponCategory::getCouponId,id);
        List<ShopCouponCategory> categoryList = this.shopCouponCategoryService.list(categoryLambdaQueryWrapper);
        List<ShopCouponCategoryVo>categorys = new ArrayList<>();
        for (ShopCouponCategory shopCouponCategory : categoryList) {
            ShopCouponCategoryVo shopCouponCategoryVo = new ShopCouponCategoryVo();
            BeanUtils.copyProperties(shopCouponCategory,shopCouponCategoryVo);
            CategoryVo categoryVo = remoteGoodsService.findCategoryById(shopCouponCategory.getCategoryId());
            shopCouponCategoryVo.setCategoryName(categoryVo.getCategoryName());
            shopCouponCategoryVo.setCategoryParentName(categoryVo.getCategoryParentName());
            shopCouponCategoryVo.setModeName(categoryVo.getModeName());
            categorys.add(shopCouponCategoryVo);
        }
        shopCouponVo.setCategorys(categorys);

        LambdaQueryWrapper<ShopCouponAccount>accountLambdaQueryWrapper = new LambdaQueryWrapper<>();
        accountLambdaQueryWrapper.eq(ShopCouponAccount::getCouponId,id);
        List<ShopCouponAccount> accountList = this.shopCouponAccountService.list(accountLambdaQueryWrapper);
        List<ShopCouponAccountVo>accounts = new ArrayList<>();
        List<String>memberLevels = new ArrayList<>();
        for (ShopCouponAccount account : accountList) {
            ShopCouponAccountVo shopCouponAccountVo = new ShopCouponAccountVo();
            BeanUtils.copyProperties(account,shopCouponAccountVo);
            if(shopCoupon.getGrantType() == GrantTypeEnum.TAG.getType()){
                ChooseTagVo chooseTagVo = remoteMiniAccountService.getChooseTagById(shopCouponAccountVo.getSourceId());
                shopCouponAccountVo.setAccountNum(chooseTagVo.getAccountNum());
                shopCouponAccountVo.setTagName(chooseTagVo.getTagName());
            }
            if(shopCoupon.getGrantType() == GrantTypeEnum.MEMBER.getType()){
                memberLevels.add(shopCouponAccountVo.getSourceId());
            }
            if(shopCoupon.getGrantType() == GrantTypeEnum.APPOINT.getType()){
                ChooseAccountVo chooseAccountVo = remoteMiniAccountService.getChooseAccountByUserId(shopCouponAccountVo.getSourceId());
                shopCouponAccountVo.setTagName(chooseAccountVo.getTagName());
                shopCouponAccountVo.setMemberLevel(chooseAccountVo.getMemberLevel());
                shopCouponAccountVo.setNikeName(chooseAccountVo.getNikeName());
                shopCouponAccountVo.setPhone(chooseAccountVo.getPhone());
            }
            accounts.add(shopCouponAccountVo);
        }
        shopCouponVo.setMemberLevels(memberLevels);
        shopCouponVo.setAccounts(accounts);
        return shopCouponVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopCoupon edit(ShopCouponDto shopCouponDto) {

        //判断当前是否主店铺
        ShopsPartner shop = shopsPartnerService.getShopsPartner();
        if(shop == null){
            throw new ServiceException("店铺为空！");
        }

        Integer mainFlag = shop.getMainFlag();
        //非主店铺
        if(mainFlag == CommonConstants.NUMBER_ZERO){
            //设为指定商家
            shopCouponDto.setShopFlag(Boolean.TRUE);
            List<ShopCouponPartnerDto>List = new ArrayList<>();
            ShopCouponPartnerDto shopCouponPartnerDto = new ShopCouponPartnerDto();
            shopCouponPartnerDto.setShopsPartnerId(shop.getPartnerId());
            List.add(shopCouponPartnerDto);
            shopCouponDto.setPartnerList(List);
        }

        ShopCoupon shopCoupon = this.getById(shopCouponDto.getId());
        if(null == shopCoupon){
            throw new ServiceException("数据错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        if(shopCoupon.getApprovedStatus() == ApproveStatusEnum.APPROVED.getStatus()){
            throw new ServiceException("已审核，不能再编辑", SystemCode.DATA_EXPIRED_CODE);
        }
        Long id = shopCoupon.getId();
        BeanUtils.copyProperties(shopCouponDto, shopCoupon);

        //先删除子表信息
        LambdaQueryWrapper<ShopCouponPartner> partnerWrapper = new LambdaQueryWrapper<>();
        partnerWrapper.eq(ShopCouponPartner::getCouponId, id);
        this.shopCouponPartnerService.remove(partnerWrapper);

        LambdaQueryWrapper<ShopCouponProduct> productWrapper = new LambdaQueryWrapper<>();
        productWrapper.eq(ShopCouponProduct::getCouponId, id);
        this.shopCouponProductService.remove(productWrapper);

        LambdaQueryWrapper<ShopCouponCategory> categoryWrapper = new LambdaQueryWrapper<>();
        categoryWrapper.eq(ShopCouponCategory::getCouponId, id);
        this.shopCouponCategoryService.remove(categoryWrapper);


        LambdaQueryWrapper<ShopCouponAccount> accountWrapper = new LambdaQueryWrapper<>();
        accountWrapper.eq(ShopCouponAccount::getCouponId, id);
        this.shopCouponAccountService.remove(accountWrapper);

        //保存主表信息
        this.updateById(shopCoupon);
        // 新增子表信息
        List<ShopCouponPartnerDto> partnerDtoList = shopCouponDto.getPartnerList();
        List<ShopCouponPartner> partnerList = new ArrayList<ShopCouponPartner>();

        //将ShopCouponPartnerDto转成ShopCouponPartner
        if(partnerDtoList!=null&&partnerDtoList.size()>0){
            for(ShopCouponPartnerDto partnerDto : partnerDtoList){
                partnerDto.setCouponId(id);
                partnerDto.setId(null);
                ShopCouponPartner partner = new ShopCouponPartner();
                BeanUtils.copyProperties(partnerDto, partner);
                partnerList.add(partner);
            }
        }
        shopCouponPartnerService.saveBatch(partnerList);

        if(shopCouponDto.getCouponType()!=null&&shopCouponDto.getCouponType() == CouponTypeEnum.PRODUCTS.getType()){
            List<ShopCouponProductDto> productDtoList = shopCouponDto.getProducts();
            List<ShopCouponProduct> productList = new ArrayList<ShopCouponProduct>();
            //将ShopCouponProductDto转成ShopCouponProduct
            List<String> productIdList = new ArrayList<>();
            if(productDtoList!=null&&productDtoList.size()>0){
                for (ShopCouponProductDto productDto : productDtoList) {
                    String key = productDto.getProductId() + "-" + productDto.getSkuId();
                    if(productIdList.contains(key)){
                        ProductVo product = remoteGoodsService.findProductById(Long.valueOf(productDto.getProductId()));
                        SkuStock skuStock = remoteGoodsService.findSkuStockById(Long.valueOf(productDto.getSkuId()));
                        throw new ServiceException("重复选择了商品[" + product.getName() + "]，规格["+skuStock.getSpecs()+"]");
                    }else{
                        productIdList.add(key);
                    }
                    productDto.setId(null);
                    productDto.setCouponId(String.valueOf(id));
                    ShopCouponProduct product = new ShopCouponProduct();
                    BeanUtils.copyProperties(productDto, product);
                    productList.add(product);
                }
            }
            shopCouponProductService.saveBatch(productList);
        }

        if(shopCouponDto.getCouponType()!=null&&shopCouponDto.getCouponType() == CouponTypeEnum.CATEGORYS.getType()){
            List<ShopCouponCategoryDto> categoryDtoList = shopCouponDto.getCategorys();
            List<ShopCouponCategory> categoryList = new ArrayList<ShopCouponCategory>();
            //将ShopCouponCategoryDto转成ShopCouponCategory
            List<String> categoryIdList = new ArrayList<>();
            if(categoryDtoList!=null&&categoryDtoList.size()>0){
                for (ShopCouponCategoryDto categoryDto : categoryDtoList) {
                    String key = categoryDto.getCategoryId();
                    if(categoryIdList.contains(key)){
                        CategoryVo categoryVo = remoteGoodsService.findCategoryById(categoryDto.getCategoryId());
                        throw new ServiceException("重复选择了品类[" + categoryVo.getCategoryName() + "]");
                    }else{
                        categoryIdList.add(key);
                    }
                    categoryDto.setCouponId(String.valueOf(id));
                    ShopCouponCategory category = new ShopCouponCategory();
                    BeanUtils.copyProperties(categoryDto, category);
                    categoryList.add(category);
                }
            }
            shopCouponCategoryService.saveBatch(categoryList);
        }
        if(shopCouponDto.getGrantType()!=null&&shopCouponDto.getGrantType() == GrantTypeEnum.TAG.getType()){
            List<ShopCouponAccountDto> accountDtoList = shopCouponDto.getAccounts();
            List<ShopCouponAccount> accountList = new ArrayList<ShopCouponAccount>();
            //将ShopCouponAccountDto转成ShopCouponAccount
            List<String> accountIdList = new ArrayList<>();

            if(accountDtoList!=null&&accountDtoList.size()>0){
                for (ShopCouponAccountDto accountDto : accountDtoList) {
                    String key = accountDto.getSourceId();
                    if(accountIdList.contains(key)){
                        ChooseTagVo chooseTagVo = remoteMiniAccountService.getChooseTagById(accountDto.getSourceId());
                        throw new ServiceException("重复选择了标签[" + chooseTagVo.getTagName() + "]");
                    }else{
                        accountIdList.add(key);
                    }
                    accountDto.setCouponId(String.valueOf(id));
                    ShopCouponAccount account = new ShopCouponAccount();
                    BeanUtils.copyProperties(accountDto, account);
                    accountList.add(account);
                }
            }
            shopCouponAccountService.saveBatch(accountList);
        }
        if(shopCouponDto.getGrantType()!=null&&shopCouponDto.getGrantType() == GrantTypeEnum.MEMBER.getType()){
            List<ShopCouponAccountDto> accountDtoList = shopCouponDto.getAccounts();
            List<ShopCouponAccount> accountList = new ArrayList<ShopCouponAccount>();
            //将ShopCouponAccountDto转成ShopCouponAccount
            if(accountDtoList!=null&&accountDtoList.size()>0){
                for (ShopCouponAccountDto accountDto : accountDtoList) {
                    accountDto.setCouponId(String.valueOf(id));
                    ShopCouponAccount account = new ShopCouponAccount();
                    BeanUtils.copyProperties(accountDto, account);
                    accountList.add(account);
                }
            }
            shopCouponAccountService.saveBatch(accountList);
        }
        if(shopCouponDto.getGrantType()!=null&&shopCouponDto.getGrantType() == GrantTypeEnum.APPOINT.getType()){
            List<ShopCouponAccountDto> accountDtoList = shopCouponDto.getAccounts();
            List<ShopCouponAccount> accountList = new ArrayList<ShopCouponAccount>();
            //将ShopCouponAccountDto转成ShopCouponAccount
            List<String> accountIdList = new ArrayList<>();

            if(accountDtoList!=null&&accountDtoList.size()>0){
                for (ShopCouponAccountDto accountDto : accountDtoList) {
                    String key = accountDto.getSourceId();
                    if(accountIdList.contains(key)){
                        ChooseAccountVo chooseAccountVo = remoteMiniAccountService.getChooseAccountByUserId(accountDto.getSourceId());
                        throw new ServiceException("重复选择了客户[" + chooseAccountVo.getNikeName() + "]");
                    }else{
                        accountIdList.add(key);
                    }
                    accountDto.setCouponId(String.valueOf(id));
                    ShopCouponAccount account = new ShopCouponAccount();
                    BeanUtils.copyProperties(accountDto, account);
                    accountList.add(account);
                }
            }
            shopCouponAccountService.saveBatch(accountList);
        }
        return shopCoupon;
    }

    @Override
    @Transactional
    public ShopCoupon audit(Long id) {
        ShopCoupon shopCoupon = this.getById(id);
        if(null == shopCoupon){
            throw new ServiceException("数据错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        if(shopCoupon.getApprovedStatus() == ApproveStatusEnum.APPROVED.getStatus()){
            throw new ServiceException("已审核，不能重复审核", SystemCode.DATA_NOT_EXIST_CODE);
        }
        shopCoupon.setApprovedStatus(ApproveStatusEnum.APPROVED.getStatus());
        shopCoupon.setApprovedTime(DateUtil.toLocalDateTime(new Date()));
        shopCoupon.setApprovedUserId(Long.parseLong(CurUserUtil.getPcRqeustAccountInfo().getUserId()));
        shopCoupon.setApprovedUserName(CurUserUtil.getPcRqeustAccountInfo().getNikeName());
        //判断是否为指定发券对象
        if(shopCoupon.getGrantType()!=null){
            shopCoupon.setSendFlag(CommonConstants.NUMBER_ONE);
            SendCouponMessage message = new SendCouponMessage();
            message.setCouponId(shopCoupon.getId());
            message.setTenantId(shopCoupon.getTenantId());
            sender.sendCouponMessage(message);
        }
        this.updateById(shopCoupon);
        return shopCoupon;
    }

    @Override
    public ShopCoupon stop(Long id) {
        ShopCoupon shopCoupon = this.getById(id);
        if(null == shopCoupon){
            throw new ServiceException("数据错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        shopCoupon.setStatus(ApproveStatusEnum.TERMINATION.getStatus());
        this.updateById(shopCoupon);
        return shopCoupon;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoUpdateStatusByTime() {
        //查询审核状态为已审核，状态未生效、已生效且开始时间、结束时间大于当前时间的记录
        LambdaQueryWrapper<ShopCoupon> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShopCoupon::getApprovedStatus, ApproveStatusEnum.APPROVED.getStatus())
                .and(i -> i.eq(ShopCoupon::getStatus, ApproveStatusEnum.AUDIT.getStatus()).or().eq(ShopCoupon::getStatus, ApproveStatusEnum.APPROVED.getStatus()))
                .and(i -> i.le(ShopCoupon::getStartTime, new Date()).or().lt(ShopCoupon::getEndTime, new Date()));

        List<ShopCoupon> shopCouponList = this.list(wrapper);
        if(BeanUtil.isNotEmpty(shopCouponList)){
            shopCouponList.stream().forEach(e -> {
                if(e.getEndTime().before(new Date())){
                    // 结束时间小于当前时间，状态改为已结束
                    e.setStatus(ApproveStatusEnum.EXPIRED.getStatus());
                }else if(e.getStatus().intValue() == ApproveStatusEnum.AUDIT.getStatus()){
                    // 未生效，因为前面已经判断结束时间小于当前时间，所以这里一定是开始时间小于当前时间，状态改为已生效
                    e.setStatus(ApproveStatusEnum.APPROVED.getStatus());
                }
                //更新有效期
                e.setUseDate(null);
                if(e.getStatus().equals(ApproveStatusEnum.APPROVED.getStatus())){
                    Date date = new Date();
                    Date endTime = e.getEndTime();
                    long differenceInMillis = Math.abs(endTime.getTime() - date.getTime());  // 相差的毫秒数
                    long differenceInDays = differenceInMillis / (24 * 60 * 60 * 1000);  // 相差的天数
                    e.setUseDate((int) differenceInDays);
                }
            });
            this.updateBatchById(shopCouponList);
        }
    }

    @Override
    public List<ShopCouponVo> getList(ShopCouponParam shopCouponParam) {
        String userId = CurUserUtil.getMiniReqeustAccountInfo().getUserId();
        List<ShopCouponVo>list = new ArrayList<>();
        List<ShopCouponVo> shopCouponVoList = this.baseMapper.selectDisplayList(shopCouponParam);
        if(shopCouponVoList!=null&&shopCouponVoList.size()>0){
            for (ShopCouponVo shopCouponVo : shopCouponVoList) {
                Long couponId = shopCouponVo.getId();
                Integer receiveTimes = shopCouponVo.getReceiveTimes();
                //查看用户领取次数
                Integer count = remoteMiniAccountService.getAccountCouponNum(couponId,userId);
                if(receiveTimes>count){
                    list.add(shopCouponVo);
                }
            }
        }
        return list;
    }

    @Override
    public List<ShopCouponVo> getShopCouponVoList() {
        List<ShopCouponVo>list = this.baseMapper.getShopCouponVoList();
        return list;
    }

    @Override
    public Map getCouponUserFlag() {
        Map result = new HashMap<>();
        int count = this.count();
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        String userId = curUserDto.getUserId();
        if(count>0){
            ShopsRedis shopsRedis = new ShopsRedis();
            String showCouponAccountKey = ShopsRedisKey.getShowCouponAccountKey(userId);
            String showCouponAccountFlag = shopsRedis.get(showCouponAccountKey);
            if(StringUtils.isNotEmpty(showCouponAccountFlag)){
                Boolean b = Boolean.valueOf(showCouponAccountFlag);
                result.put("showCouponAccountFlag",b);
            }else{
                result.put("showCouponAccountFlag",true);
            }
        }else{
            result.put("showCouponAccountFlag",false);
        }
        return result;
    }

    @Override
    public void setShowCouponTime() {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        String userId = curUserDto.getUserId();
        String showCouponAccountKey = ShopsRedisKey.getShowCouponAccountKey(userId);
        ShopsRedis shopsRedis = new ShopsRedis();
        String aFalse = shopsRedis.setNxPx(showCouponAccountKey, "false", TimeConstants.ONE_DAY);
        System.out.println(aFalse);
    }

    @Override
    public List<ShopsPartner> selectPartnerListByCouponId(Long couponId) {
        ShopCoupon shopCoupon = this.getById(couponId);
        boolean shopFlag = shopCoupon.getShopFlag();
        if(!shopFlag){
            //不指定商家，即所有审核通过的商家
            LambdaQueryWrapper<ShopsPartner> partnerLambdaQueryWrapper = new LambdaQueryWrapper<>();
            partnerLambdaQueryWrapper.eq(ShopsPartner::getApprovalStatus, ApproveStatusEnum.APPROVED.getStatus())
                    .eq(ShopsPartner::getProhibitStatus, ProhibitStatusEnum.NORMAL.getStatus());
            List<ShopsPartner> partnerList = this.shopsPartnerService.list(partnerLambdaQueryWrapper);
            return partnerList;
        }

        LambdaQueryWrapper<ShopCouponPartner>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(null != couponId, ShopCouponPartner::getCouponId, couponId);
        List<ShopCouponPartner> couponPartnerList = this.shopCouponPartnerService.list(wrapper);
        List<Long> partnerIds = couponPartnerList.stream().map(ShopCouponPartner::getShopsPartnerId).collect(Collectors.toList());
        LambdaQueryWrapper<ShopsPartner> partnerLambdaQueryWrapper = new LambdaQueryWrapper<>();
        partnerLambdaQueryWrapper.eq(ShopsPartner::getApprovalStatus, ApproveStatusEnum.APPROVED.getStatus())
                .eq(ShopsPartner::getProhibitStatus, ProhibitStatusEnum.NORMAL.getStatus())
                .in(ShopsPartner::getId, partnerIds);
        List<ShopsPartner> partnerList = this.shopsPartnerService.list(partnerLambdaQueryWrapper);
        return partnerList;
    }

    @Override
    public List<ShopCouponVo> getShopCouponListByCouponType(Integer couponType) {
        List<ShopCouponVo>list = this.baseMapper.getShopCouponListByCouponType(couponType);
        return list;
    }
}
