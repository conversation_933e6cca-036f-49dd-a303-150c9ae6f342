package com.medusa.gruul.shops.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.*;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.account.api.model.vo.MemberLevelVo;
import com.medusa.gruul.account.api.model.vo.MemberTypeApiVo;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.common.core.util.StringUtil;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.shops.api.conf.ShopsRedis;
import com.medusa.gruul.shops.api.constant.ShopsRedisKey;
import com.medusa.gruul.shops.api.entity.MiniAccountSalePrize;
import com.medusa.gruul.shops.api.entity.ShopSalePrize;
import com.medusa.gruul.shops.api.entity.ShopSalePrizeItem;
import com.medusa.gruul.shops.api.entity.ShopSalePrizeProduct;
import com.medusa.gruul.shops.api.enums.SalePrizeEnums;
import com.medusa.gruul.shops.api.model.*;
import com.medusa.gruul.shops.mapper.ShopSalePrizeItemMapper;
import com.medusa.gruul.shops.mapper.ShopSalePrizeMapper;
import com.medusa.gruul.shops.mapper.ShopSalePrizeProductMapper;
import com.medusa.gruul.shops.model.dto.RafflePrizeDto;
import com.medusa.gruul.shops.model.vo.MiniAccountSalePrizeVo;
import com.medusa.gruul.shops.model.vo.ShopSalePrizeItemVo;
import com.medusa.gruul.shops.model.vo.ShopSalePrizeVo;
import com.medusa.gruul.shops.mq.Sender;
import com.medusa.gruul.shops.service.IMiniAccountSalePrizeService;
import com.medusa.gruul.shops.service.IMiniShopSalePrizeService;
import com.medusa.gruul.shops.service.IShopSalePrizeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 营销活动抽奖 小程序端服务实现类
 * <AUTHOR>
 */

@Slf4j
@Service
public class MiniShopSalePrizeServiceImpl extends ServiceImpl<ShopSalePrizeMapper, ShopSalePrize> implements IMiniShopSalePrizeService {

    @Resource
    private ShopSalePrizeItemMapper shopSalePrizeItemMapper;
    @Resource
    private ShopSalePrizeProductMapper shopSalePrizeProductMapper;

    @Autowired
    private RemoteMiniAccountService remoteMiniAccountService;
    @Autowired
    private IMiniAccountSalePrizeService miniAccountSalePrizeService;
    @Autowired
    private Sender sender;
    @Autowired
    private RemoteGoodsService remoteGoodsService;
    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    @Autowired
    private IShopSalePrizeService shopSalePrizeService;

    @Override
    public void grantPrizeOpportunityOnOrderPaid(OrderPaidDto orderPaidDto) {
        // 设置必要属性到ThreadLocal
        if (orderPaidDto.getTenantId()!=null)
            TenantContextHolder.setTenantId(orderPaidDto.getTenantId());
        if (orderPaidDto.getShopId()!=null)
            ShopContextHolder.setShopId(orderPaidDto.getShopId());
        LocalDateTime now = LocalDateTime.now();
        // 1. 查询所有进行中、已审核 、 指定商品的活动
        List<ShopSalePrize> activePrizes = getShopSalePrizesByLeNowDate(SalePrizeEnums.ProductFlat.YES.getCode());

        if (CollectionUtils.isEmpty(activePrizes)) {
            log.info("没有找到符合条件的抽奖活动，订单ID: {}", orderPaidDto.getOrderId());
            return;
        }
        // 查出用户会员
        List<MemberLevelRelation> userLevels = null;
        String shopUserId = orderPaidDto.getShopUserId();
        AccountInfoDto infoDto = remoteMiniAccountService.accountInfo(shopUserId, Arrays.asList(1));
        if (ObjectUtil.isNotNull(infoDto)){

            userLevels = remoteMiniAccountService.getMemberLevelsByUserId(infoDto.getMiniAccountunt().getUserId());
        }

        if (CollectionUtils.isEmpty(userLevels)){
            log.info("找不到会员信息: ,shopUserId:{},订单Id:{}", shopUserId,orderPaidDto.getOrderId());
            return;
        }

        // 2. 轮询活动，判断资格
        for (ShopSalePrize prize : activePrizes) {

            // 2.1 判断会员等级
            boolean checkUserLevel = checkUserLevel(prize, userLevels);
            if (!checkUserLevel) {
                log.debug("用户等级不符，跳过活动: {}", prize.getName());
                continue; // 等级不符，跳过此活动
            }

            // 判断 2.2最大抽奖次数
            boolean checkParticipationCount = checkParticipationCount(shopUserId, prize,true);
            if (!checkParticipationCount){
                log.info("会员参与次数已达上限: ,shopUserId:{},订单Id:{}", shopUserId,orderPaidDto.getOrderId());
                continue;
            }
            // 3 判断订单金额
            LambdaQueryWrapper<ShopSalePrizeProduct> productWrapper = new LambdaQueryWrapper<>();
            productWrapper.eq(ShopSalePrizeProduct::getMainId, prize.getId());
            // 查出指定商品
            List<ShopSalePrizeProduct> prizeProducts = shopSalePrizeProductMapper.selectList(productWrapper);
            if (CollectionUtils.isEmpty(prizeProducts)) {
                continue; // 活动配置错误，指定了商品但列表为空
            }
            // 指定商品规格id
            List<Long> requiredSkuIds = prizeProducts.stream().map(ShopSalePrizeProduct::getSkuId).collect(Collectors.toList());

           // 符合的商品 list
            List<OrderPaidDetailDto> productsList  = orderPaidDto.getOrderDetails().stream()
                    .filter(detail -> requiredSkuIds.contains(detail.getProductSkuId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(productsList)){
                log.info("订单中没有指定商品: {}", prize.getName());
                continue;
            }

            // 计算订单中指定商品的总金额
            BigDecimal orderAmount = prize.getOrderAmount();
            if (orderAmount!= null && orderAmount.compareTo(BigDecimal.ZERO)>0){
                BigDecimal amountForCheck = productsList.stream()
                        .map(product -> product.getRealAmount()
                                .multiply(new BigDecimal(product.getProductQuantity()+"")))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                //  金额是否满足门槛
                if (amountForCheck.compareTo(orderAmount) < 0) {
                    continue; // 设置了满额门槛，但订单金额不足，跳过此活动
                }
            }

            // 满足所有条件，增加抽奖机会
            addPrizeCountToRedis(orderPaidDto.getOrderId(),orderPaidDto.getMemberTypeId(), prize, shopUserId,SalePrizeEnums.GiftType.Order);

            log.info("用户 {} 满足参加抽奖活动： {} ", shopUserId, prize.getName());
        }
    }

    /**
     * 赠送抽奖机会
     * @param orderId
     * @param prize
     * @param shopUserId
     */
    private void addPrizeCountToRedis(Long orderId,Long memberTypeId, ShopSalePrize prize, String shopUserId,SalePrizeEnums.GiftType giftType) {
        String prizeId = prize.getId()+"";
        if (orderId == null){
            orderId = -1L;
        }
        if (memberTypeId == null){
            memberTypeId = -1L;
        }
        ShopsRedis shopsRedis = new ShopsRedis();
        LocalDateTime now = LocalDateTime.now();

        // 抽奖机会+1
        String redisKey = ShopsRedisKey.getPrizeKey(shopUserId, prizeId);
        Long incr = shopsRedis.incr(redisKey);



        // 订单Id队列
        String prizeOrderKey = ShopsRedisKey.getPrizeOrderKey(shopUserId, prizeId);
        shopsRedis.lpush(prizeOrderKey, orderId.toString());

        // 会员类型
        String prizeMemberTypeKey = ShopsRedisKey.getPrizeMemberTypeKey(shopUserId, prizeId);
        shopsRedis.lpush(prizeMemberTypeKey, memberTypeId.toString());


        // 统一设置过期时间
        String baseKey = shopsRedis.getBaseKey();
        //差值计算
        Duration expireDuration = Duration.between(now, prize.getEndTime());
        int seconds = (int)expireDuration.getSeconds();
        Long redisExpire = redisTemplate.getExpire(baseKey+":"+redisKey, TimeUnit.SECONDS);

        // 更新过期时间
        if (redisExpire < seconds){
            shopsRedis.expire(redisKey, seconds);
            shopsRedis.expire(prizeOrderKey, seconds);
            shopsRedis.expire(prizeMemberTypeKey, seconds);
        }

        String msg = orderId==-1L?"直推会员升级":"订单支付:"+orderId;
        log.info("checkPrizeMember: 用户 {} 通过{}获得抽奖机会，活动: {}, 总次数: {}",
                shopUserId, msg,prize.getId(), incr);
    }

    @Override
    public List<ShopSalePrizeVo> hasSalePrize(String shopUserId) {
        ArrayList<ShopSalePrizeVo> list = new ArrayList<>();
        if (StringUtil.isBlank(shopUserId)) {
            throw new ServiceException("用户ID不能为空");
        }
        // 查询符合条件的抽奖活动
        List<ShopSalePrize> activeList =  getShopSalePrizesByLeNowDate(null);
        if (CollectionUtils.isEmpty(activeList)) {
            return new ArrayList<>();
        }
        // 检查用户是否有抽奖机会
        for (ShopSalePrize prize : activeList) {
            String countStr = getPrizeCountFormRedis(shopUserId,  prize.getId());
            ShopSalePrizeVo vo = new ShopSalePrizeVo();
            BeanUtils.copyProperties(prize, vo);// 有抽奖机会
            vo.setPrizeCount(Integer.parseInt(countStr));
            list.add(vo);
        }
        return list;
    }

    private String getPrizeCountFormRedis(String shopUserId, Long prizeId) {
        String redisKey = ShopsRedisKey.getPrizeKey(shopUserId, prizeId + "");
        ShopsRedis shopsRedis = new ShopsRedis();
        String countStr = shopsRedis.get(redisKey);
        if (countStr == null ||"null".equals(countStr)){
            countStr = "0";
        }
        return countStr;
    }

    @Override
    public List<ShopSalePrizeItemVo> getPrizeItemsById(Long prizeId) {
        // 1. 验
        LocalDateTime now = LocalDateTime.now();
        LambdaQueryWrapper<ShopSalePrize> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShopSalePrize::getAuditStatus, SalePrizeEnums.AuditStatus.STATUS_APPROVED.getCode())
                .le(ShopSalePrize::getStartTime, now)
                .ge(ShopSalePrize::getEndTime, now)
                .eq(ShopSalePrize::getId,  prizeId);
        List<ShopSalePrize> activeList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(activeList)) {
            return new ArrayList<>();
        }
        List<ShopSalePrizeItemVo> resultItems = new ArrayList<>();

        // 2. 遍历活动
        for (ShopSalePrize prize : activeList) {
            // 获取活动的所有明细
            List<ShopSalePrizeItemVo> allItems = shopSalePrizeItemMapper.getListByMainId(prize.getId());
            if (CollectionUtils.isEmpty(allItems)) {
                continue;
            }
            // 设置图片
//            Set<Long> productIds = allItems.stream().map(ShopSalePrizeItemVo::getProductId).collect(Collectors.toSet());
//            Collection<Product> productList = remoteGoodsService.findProductListByIds(productIds);
//            Map<Long, Product> productMap = productList.stream().collect(Collectors.toMap(Product::getId, product -> product));
            for (ShopSalePrizeItemVo allItem : allItems) {
//               if (null != productMap.get(allItem.getProductId())){
//                   allItem.setPic(productMap.get(allItem.getProductId()).getPic());
//               }
                allItem.setPic(allItem.getLevelPic());
            }


            // 非间隔奖项
            List<ShopSalePrizeItemVo> notSpacingList = allItems.stream()
                    .filter(item -> item.getSpacingFlag() != null && item.getSpacingFlag() == 0)
                    .sorted(Comparator.comparing(ShopSalePrizeItemVo::getSeqBy))
                    .collect(Collectors.toList());
            // 间隔奖项
            List<ShopSalePrizeItemVo> isSpacingList = allItems.stream()
                    .filter(item -> item.getSpacingFlag() != null && item.getSpacingFlag() == 1)
                    .sorted(Comparator.comparing(ShopSalePrizeItemVo::getSeqBy))
                    .collect(Collectors.toList());
            // 为空直接使用另一个列表
            if (CollectionUtils.isEmpty(notSpacingList)) {
                resultItems.addAll(isSpacingList);
                continue;
            }
            if (CollectionUtils.isEmpty(isSpacingList)) {
                resultItems.addAll(notSpacingList);
                continue;
            }
            // 组装List
            List<ShopSalePrizeItemVo> resultList = new ArrayList<>();
            for (ShopSalePrizeItemVo itemA : notSpacingList) {
                resultList.add(itemA);
                resultList.addAll(isSpacingList);
            }
            resultItems.addAll(resultList);
        }
        return resultItems;
    }

    @Transactional(rollbackFor = Exception.class)
    public Result<MiniAccountSalePrizeVo> rafflePrize(RafflePrizeDto dto) {
        Long prizeId = dto.getPrizeId();


        //   获取当前登录用户
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (curUser == null ) {
            return Result.failed("找不到用户信息");
        }
        String shopUserId = curUser.getUserId();
        AccountInfoDto accountInfo = remoteMiniAccountService.accountInfo(shopUserId, Arrays.asList(1, 3));

        MiniAccountAddress address = null;

        if (accountInfo != null && accountInfo.getMiniAccountAddress() != null && !accountInfo.getMiniAccountAddress().isEmpty()) {
            for (MiniAccountAddress data : accountInfo.getMiniAccountAddress()) {
                if (data != null && data.getIsDefault() == 1) {
                    address = data;
                    break;
                }
            }
        }else {
            return Result.failed("没有设置收货地址");
        }

        // 如果没有找到默认地址，使用第一个可用地址
        if (null == address) {
             address = accountInfo.getMiniAccountAddress().get(0);
        }

        //  1 根据活动ID获取活动
        ShopSalePrize prize = this.getById(prizeId);
        if (prize == null || !prize.getAuditStatus().equals(SalePrizeEnums.AuditStatus.STATUS_APPROVED.getCode()) ) {
            return Result.failed("活动不存在");
        }
        ShopContextHolder.setShopId(prize.getShopId());
        LocalDateTime now = LocalDateTime.now();
        //  时间
        if (prize.getStartTime().isAfter(now) || prize.getEndTime().isBefore(now)) {
            return Result.failed("不在活动时间范围内");
        }

        // 限制次数
        boolean checked = checkParticipationCount(shopUserId, prize,false);
        if (!checked) {
            return Result.failed("已达到最大抽奖次数");
        }
        // 2 redis 中取数
        boolean checkRedis  = false;

        if (Objects.equals(prize.getProductFlag(), SalePrizeEnums.PrizeFlag.YES.getCode()) || prize.getDirectMemberQty()!=null){
            String countStr = getPrizeCountFormRedis(shopUserId, prizeId);
            if (countStr != null && Integer.parseInt(countStr) > 0) {
                checkRedis = true;
            }else  if (countStr != null && Integer.parseInt(countStr) <= 0){
                return Result.failed("没有抽奖次数了");
            }
        }

        //会员等级
        boolean checkUserLevel =  false;
        if (checkRedis){
            List<MemberLevelRelation> userLevels = remoteMiniAccountService.getMemberLevelsByUserId(accountInfo.getMiniAccountunt().getUserId());
            // 校验会员等级
            checkUserLevel = checkUserLevel(prize, userLevels);
            if (!checkUserLevel){
                return Result.failed("不满足会员等级要求");
            }
        }

        if (!checkRedis || !checkUserLevel){
            return Result.failed("您不能参加此次抽奖活动");
        }




        // 3. 生成随机数0-1 A
        double randomValue = RandomUtil.randomDouble(0.00001, 1.0);
        BigDecimal userRate = new BigDecimal(String.valueOf(randomValue));
        if (userRate.compareTo(BigDecimal.ONE) > 0) { //todo 这里应该没必要
            userRate = BigDecimal.ONE;
        } else if (userRate.compareTo(BigDecimal.ZERO) <= 0) {
            userRate = new BigDecimal("0.1");
        }

        // 4.  E 当前活动参与次数
        LambdaQueryWrapper<MiniAccountSalePrize> countWrapper = new LambdaQueryWrapper<>();
        countWrapper.eq(MiniAccountSalePrize::getPrizeId, prizeId);
        long totalParticipations = miniAccountSalePrizeService.count(countWrapper)+1;

        // 获取奖项明细 中奖概率>userRate的奖项 F
        LambdaQueryWrapper<ShopSalePrizeItem> itemWrapper = new LambdaQueryWrapper<>();
        itemWrapper.eq(ShopSalePrizeItem::getMainId, prizeId)
                .ge(ShopSalePrizeItem::getWinningRate, userRate)
                .orderByAsc(ShopSalePrizeItem::getWinningRate);
        List<ShopSalePrizeItem> eligibleItems = shopSalePrizeItemMapper.selectList(itemWrapper);


        boolean isWin = false;
        ShopSalePrizeItem shopSalePrizeItem  = null;
        //  遍历是否中奖
        for (ShopSalePrizeItem item : eligibleItems) {
            // 已中奖的记录数
            LambdaQueryWrapper<MiniAccountSalePrize> winWrapper = new LambdaQueryWrapper<>();
            winWrapper.eq(MiniAccountSalePrize::getPrizeId, prizeId)
                    .eq(MiniAccountSalePrize::getPrizeItemId, item.getId())
                    .eq(MiniAccountSalePrize::getPrizeFlag, SalePrizeEnums.PrizeFlag.YES.getCode());
            long winCount = miniAccountSalePrizeService.count(winWrapper);  //H 中将次数

            // G 多少人次参与后最多中奖一次
            long winningPersonTimes = item.getWinningPersonTimes() != null ? item.getWinningPersonTimes() : 0;
            if (winningPersonTimes <= 0) {
                continue;
            }

            // K   E当前活动参与次数 % G 取模 ->允许的最大中奖次数
            long remainder = totalParticipations / winningPersonTimes;

            if (winCount  < remainder) {
                shopSalePrizeItem = item;
                isWin =true;
                break;
            }
        }

        ShopsRedis shopsRedis = new ShopsRedis();
        // redis 减少机会
        Long decr =null;
        String orderId = null;
        String memberTypeId = null;
        if (checkRedis){
            String countStr = getPrizeCountFormRedis(shopUserId, prizeId);
            if (countStr != null && Integer.parseInt(countStr) > 0) {
                // 减少一次抽奖机会
                 decr = shopsRedis.decr(ShopsRedisKey.getPrizeKey(shopUserId, prizeId + ""));
                if (decr < 0){
                    shopsRedis.del(ShopsRedisKey.getPrizeKey(shopUserId, prizeId + ""));
                    throw new ServiceException("系统异常，抽奖次数已用完");
                }
                // 从redis中获取订单id 会员类型id
                String prizeOrderKey = ShopsRedisKey.getPrizeOrderKey(shopUserId, prizeId + "");
                orderId = shopsRedis.rpop(prizeOrderKey);
                String prizeMessageKey = ShopsRedisKey.getPrizeMemberTypeKey(shopUserId, prizeId + "");
                memberTypeId = shopsRedis.rpop(prizeMessageKey);
            }else {
                throw new ServiceException("系统异常，抽奖次数已用完");
            }
        }

        // 保持抽奖记录entity
        MiniAccountSalePrize miniAccountSalePrize = new MiniAccountSalePrize();
        miniAccountSalePrize.setUserId(shopUserId);
        miniAccountSalePrize.setPrizeId(prizeId);
        miniAccountSalePrize.setUserRate(userRate);
        miniAccountSalePrize.setStatus(SalePrizeEnums.PrizeStatus.STATUS_UNISSUED.getCode()); // 未发放
        miniAccountSalePrize.setUserName(curUser.getNikeName());
        miniAccountSalePrize.setUserPhone(curUser.getPhone());
        // 设置 赠送的订单Id

        miniAccountSalePrize.setOrderId(StringUtil.isNotBlank(orderId) ? Long.parseLong(orderId) : null);
        // 构建返回Vo
        MiniAccountSalePrizeVo  resultVo = new MiniAccountSalePrizeVo();;
        try {

            if (isWin){
                // 中奖
                BeanUtils.copyProperties(shopSalePrizeItem, miniAccountSalePrize,  "id","userId","status","prizeId");
                miniAccountSalePrize.setPrizeFlag(SalePrizeEnums.PrizeFlag.YES.getCode()); // 中奖
                miniAccountSalePrize.setPrizeItemId(shopSalePrizeItem.getId());
                /*miniAccountSalePrize.setPrizeItemId(shopSalePrizeItem.getId());
                miniAccountSalePrize.setProductId(shopSalePrizeItem.getProductId());
                miniAccountSalePrize.setPrizeName(shopSalePrizeItem.getProductName());
                miniAccountSalePrize.setSkuId(shopSalePrizeItem.getSkuId());*/
                miniAccountSalePrizeService.save(miniAccountSalePrize);


             /*   if (Objects.equals(shopSalePrizeItem.getPrizeType(), SalePrizeEnums.PrizeType.PRODUCT.getCode())){
                    // 获取会员类型
                    String memberLevelIds = prize.getMemberLevelIds();
                    Set<String> requiredLevels = new HashSet<>(Arrays.asList(memberLevelIds.split(",")));
                    List<MemberLevelRelation> memberLevelRelationList = remoteMiniAccountService.getMemberLevelsByUserId(accountInfo.getMiniAccountunt().getUserId());
                    if (!CollectionUtils.isEmpty(memberLevelRelationList)) {
                        // 获取交集中的会员等级关系，并提取会员类型ID
                        MemberLevelRelation memberLevelRelation = memberLevelRelationList.stream()
                                .filter(relation -> requiredLevels.contains(relation.getMemberLevelId()))
                                .findFirst().orElse(null);

                        if (memberLevelRelation!= null) {
                            memberTypeId = memberLevelRelation.getMemberTypeId();
                        }else {
                            throw new ServiceException("获取会员类型失败");
                        }
                    }
                }*/
                // 处理奖品发放 订单、优惠券
                handlePrizeDelivery(miniAccountSalePrize, memberTypeId,address,resultVo);
            }else {
                // 未中奖
                miniAccountSalePrize.setPrizeFlag(SalePrizeEnums.PrizeFlag.NO.getCode());
                miniAccountSalePrizeService.save(miniAccountSalePrize);
            }
        }catch (Exception e){
            log.error("抽奖 MiniShopSalePrizeServiceImpl rafflePrize异常 PrizeId:{},shopUserId:{}",  prizeId, shopUserId);
            log.error("异常信息",e);
            // 抽奖次数+回
            if (decr != null){
                shopsRedis.incr(ShopsRedisKey.getPrizeKey(shopUserId, prizeId+""));
            }
            throw new ServiceException("抽奖 MiniShopSalePrizeServiceImpl rafflePrize异常",e);
        }

        BeanUtils.copyProperties(miniAccountSalePrize,resultVo);
        resultVo.setMainPrizeName(prize.getName());

        return Result.ok(resultVo);
    }



    private void handlePrizeDelivery(MiniAccountSalePrize miniAccountSalePrize, String memberTypeId,MiniAccountAddress address, MiniAccountSalePrizeVo  resultVo ) {
        String tenantId = TenantContextHolder.getTenantId();
        String shopId = ShopContextHolder.getShopId();
        //优惠卷
        if (SalePrizeEnums.PrizeType.COUPON.getCode().equals(miniAccountSalePrize.getPrizeType())){
            PrizeCouponMessage message = new PrizeCouponMessage();
            message.setTenantId(tenantId);
            message.setShouId(shopId);
            BeanUtils.copyProperties(miniAccountSalePrize, message);
            message.setStatus(100);
            sender.sendPrizeCouponMessage(message);
        }

        // 线上 商品
        if (SalePrizeEnums.VerifyType.ONLINE_DELIVERY.getCode().equals(miniAccountSalePrize.getVerifyType()) &&
                SalePrizeEnums.PrizeType.PRODUCT.getCode().equals(miniAccountSalePrize.getPrizeType())){

            PrizeProductDto dto = new PrizeProductDto();
            CurUserDto curUser = CurUserUtil.getHttpCurUser();
            if (memberTypeId==null || Long.parseLong(memberTypeId)==-1L){
                throw new ServiceException("抽奖失败，会员类型获取失败");
            }
            dto.setMemberTypeId(Long.parseLong(memberTypeId));
            // 复制中奖记录的基本信息
            BeanUtils.copyProperties(miniAccountSalePrize, dto);
            dto.setTenantId(tenantId);
            dto.setShopId(shopId);
            dto.setPrizeName(miniAccountSalePrize.getPrizeName());
            dto.setCurUserDto(curUser);
            // 地址
            dto.setAddress(JSON.toJSONString(address));
            log.info("准备发送奖品商品订单数据：{}", dto);
            resultVo.setAddressInfo(address);
            // 发送商品奖品消息到订单服务创建特殊订单
            sender.sendPrizeProductMessage(dto);
        }

    }


    /**
     * 获取当前有效的抽奖活动列表
     */
    private List<ShopSalePrize> getShopSalePrizesByLeNowDate(Integer productFlat) {
        LocalDateTime now = LocalDateTime.now();
        LambdaQueryWrapper<ShopSalePrize> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShopSalePrize::getAuditStatus, SalePrizeEnums.AuditStatus.STATUS_APPROVED.getCode())
                .eq(ShopSalePrize::getStatus, SalePrizeEnums.Status.STATUS_RELEASED.getCode())
                .le(ShopSalePrize::getStartTime, now)
                .ge(ShopSalePrize::getEndTime, now);

        if (SalePrizeEnums.ProductFlat.YES.getCode().equals(productFlat)){
            queryWrapper
//                    .isNotNull(ShopSalePrize::getOrderAmount)
//                    .gt(ShopSalePrize::getOrderAmount, 0)
                    .eq(ShopSalePrize::getProductFlag,productFlat);
        }

        return this.list(queryWrapper);
    }
    public void checkPrizeMember(PrizeMemberMessage message) {
        // 设置content
        if (message.getTenantId()!=null)
            TenantContextHolder.setTenantId(message.getTenantId());
        if (message.getShopId()!=null)
            ShopContextHolder.setShopId(message.getShopId());
        Long upgradeMemberLeverId = message.getMemberLeverId();
        Long userId = message.getUserId();
        if (userId==null ||message.getMemberLeverId() ==null){
            log.error("checkPrizeMember: 缺少参数");
            return;
        }
        // 1. 获取到上级
        AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(userId + "", Arrays.asList(1));
        if (accountInfoDto == null || accountInfoDto.getMiniAccountunt()==null){
            log.error("checkPrizeMember: 没有找到用户信息 userId: "+userId);
            return;
        }
        MiniAccount account = accountInfoDto.getMiniAccountunt();
        String parentId = account.getParentId();
        if (StringUtil.isBlank(parentId)){
            log.info("checkPrizeMember: 当前用户没有上级 userId: "+userId);
            return;
        }

        List<MemberLevelRelation> parentLevels = remoteMiniAccountService.getMemberLevelsByUserId(parentId);
        if (parentLevels == null || CollectionUtils.isEmpty(parentLevels)){
            log.error("checkPrizeMember: 没有会员等级关系 userId: "+parentId);
            return;
        }
        List<MemberLevelVo> memberLevelList = remoteMiniAccountService.getMemberLevelList();
        ArrayList<Long> selectLevel = new ArrayList<>(memberLevelList.size());
        // 取[oneLevelId - upgradeMemberLeverId ]
        for (MemberLevelVo levelVo : memberLevelList) {
            selectLevel.add(Long.parseLong(levelVo.getId()));
            if (levelVo.getId().equals(upgradeMemberLeverId+"")){
                break;
            }
        }
        if (CollectionUtils.isEmpty(memberLevelList)){
            log.info("checkPrizeMember: 没有会员等级关系 userId: "+parentId);
            return;
        }
        // 2. 下级升级满足等级最低的要求
        LambdaQueryWrapper<ShopSalePrize> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ShopSalePrize::getDirectLowMemberLevelId,selectLevel); // 最低等级限制
        queryWrapper.ge(ShopSalePrize::getEndTime, LocalDateTime.now());
        queryWrapper.le(ShopSalePrize::getStartTime, LocalDateTime.now());
        queryWrapper.eq(ShopSalePrize::getStatus, SalePrizeEnums.Status.STATUS_RELEASED.getCode());
        queryWrapper.eq(ShopSalePrize::getAuditStatus, SalePrizeEnums.AuditStatus.STATUS_APPROVED.getCode()); // 状态审核通过
        queryWrapper.gt(ShopSalePrize::getDirectMemberQty, 0); // 直推》0
        //满足的活动
        List<ShopSalePrize> prizeList = this.list(queryWrapper);

        if (CollectionUtils.isEmpty(prizeList)){
            log.info("checkPrizeMember: 没有满足的抽奖活动 userid:"+userId);
            return;
        }

        ShopsRedis shopsRedis = new ShopsRedis();
        for (ShopSalePrize prize : prizeList) {
            // 3. for活动 上级Pid是否满足会员等级要求
            if (checkUserLevel(prize, parentLevels)){
                String memberKey = ShopsRedisKey.getPrizeDirectMemberKey(String.valueOf(userId), prize.getId() + "");
                Duration expireDuration = Duration.between(LocalDateTime.now(), prize.getEndTime());
                // 设置到redis 确保一个用户只能计算一次
                String memberNx = shopsRedis.setNxPx(memberKey, "1", expireDuration.toMillis());
                if(!CommonConstants.REDIS_SUCCESS.equals(memberNx)){
                    log.info("checkPrizeMember: 当前用户已参与直推数计算 userId: "+userId+" prizeId: "+prize.getId());
                    continue;
                }
                // 累加
                String parentDirectMemQtyKey = ShopsRedisKey.getPrizeDirectMemQtyKey(parentId, prize.getId() + "");
                Long currentMemQty = shopsRedis.incr(parentDirectMemQtyKey);

                // 对该活动记录的直推会员数分别进行取模、取余运算得到值K、M
                Integer directMemberQty = prize.getDirectMemberQty();

                long kNumber = currentMemQty % directMemberQty;
                long mNumber = currentMemQty / directMemberQty;
//
//                shopsRedis.set(parentDirectMemQtyKey, String.valueOf(mNumber));
                // 计算能参与多少次该活动 取模为1 说明次数可以加+1
                if (mNumber > 0 && kNumber == 1 ) {
                    String parentPrizeKey = ShopsRedisKey.getPrizeKey(parentId, prize.getId() + "");
                    String countStr = shopsRedis.get(parentPrizeKey);
                    Integer perCount = prize.getPerCount();
                    int prizeCount = (countStr != null) ? Integer.parseInt(countStr) : 0;
                    //todo 目前不确定 perCount 是限制作用还是赠送机会次数。 现在当做限制作用

                    /*
                    if (perCount != null && perCount > 0) {
                        int seconds = (int) expireDuration.getSeconds();
                        int newCount = prizeCount + perCount;
                        // 设置抽奖机会次数

                        shopsRedis.set(parentPrizeKey, String.valueOf(newCount));
                        shopsRedis.expire(parentPrizeKey, seconds);
                        log.info("用户 {} 通过直推会员升级获得抽奖机会，活动: {}, 总次数: {}",
                                parentId, prize.getId(), newCount);
                    }*/
                    if (perCount != null && perCount > 0) {
                        // 通过会员等级+userId 获取会员类型
                        Long memberLeverId = message.getMemberLeverId();
                        List<MemberLevelRelation> levels = remoteMiniAccountService.getMemberLevelsByUserId(parentId);
                        Long memberTypeId = null;
                        if (!CollectionUtils.isEmpty( levels)){
                            memberTypeId = levels.stream()
                                    .filter(level -> level.getMemberLevelId().equals(memberLeverId + ""))
                                    .map(MemberLevelRelation::getMemberTypeId).findFirst().orElse(null);
                        }
                        // 赠送抽奖机会
                        addPrizeCountToRedis(-1L,memberTypeId, prize, parentId,SalePrizeEnums.GiftType.Direct);
                   /*     int seconds = (int) expireDuration.getSeconds();
                        Long incr = shopsRedis.incr(parentPrizeKey);
                        shopsRedis.expire(parentPrizeKey, seconds);

                        log.info("checkPrizeMember: 用户 {} 通过直推会员升级获得抽奖机会，活动: {}, 总次数: {}",
                                parentId, prize.getId(), incr);
                    */
                    }
                }

                
            }
        }
    }

    /**
     * 检查用户参与次数
     * isCheckRedis 是否计算redis中的剩余次数
     */
    private boolean checkParticipationCount(String shopUserId, ShopSalePrize prize,boolean isCheckRedis) {
        Integer countRedis = 0;
        if (isCheckRedis){
            // 先从Redis中获取剩余次数
            String countFormRedis = getPrizeCountFormRedis(shopUserId, prize.getId());
            countRedis = countFormRedis == null || Integer.parseInt(countFormRedis)<0 ? 0  : Integer.parseInt(countFormRedis);
        }
        LambdaQueryWrapper<MiniAccountSalePrize> countWrapper = new LambdaQueryWrapper<>();
        countWrapper.eq(MiniAccountSalePrize::getUserId, shopUserId)
                .eq(MiniAccountSalePrize::getPrizeId, prize.getId());
        long participationCount = miniAccountSalePrizeService.count(countWrapper);

        Integer maxParticipations = prize.getPerCount();
        // 默认为1次
        if (maxParticipations == null) {
            maxParticipations = 1;
        }
        // redis剩余次数+已抽次数
        int used  =  Integer.parseInt(""+participationCount)+countRedis;
        return  used < maxParticipations;
    }
    private  boolean checkUserLevel(ShopSalePrize prize, List<MemberLevelRelation> userLevels) {
        String memberLevelIds = prize.getMemberLevelIds();
        if (StringUtil.isNotEmpty(memberLevelIds) ) {
            List<String> requiredLevels = Arrays.asList(memberLevelIds.split(","));
            return userLevels.stream().map(MemberLevelRelation::getMemberLevelId).anyMatch(requiredLevels::contains);
        }else {
            return true; //未配置参与会员等级
        }
    }



}