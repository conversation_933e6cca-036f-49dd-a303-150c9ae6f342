package com.medusa.gruul.shops.service;

import com.medusa.gruul.common.core.util.StringUtil;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.shops.api.entity.ShopSalePrize;
import com.medusa.gruul.shops.api.enums.SalePrizeEnums;
import com.medusa.gruul.shops.model.PrizeMessage;
import com.medusa.gruul.shops.service.impl.MiniShopSalePrizeServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 抽奖赠送模板方法抽象类
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
public abstract class AbstractPrizeGrantTemplate {

    @Autowired
    protected PrizeGrantStrategyFactory strategyFactory;

    @Autowired
    protected MiniShopSalePrizeServiceImpl miniShopSalePrizeService;

    /**
     * 模板方法 - 定义抽奖赠送处理的算法骨架
     * 
     * @param message 赠送消息
     */
    public final void handlePrizeGrant(PrizeMessage message) {
        try {
            // 1. 设置上下文
            setContext(message);
            
            // 2. 前置检查
            if (!preCheck(message)) {
                log.debug("前置检查失败，跳过处理: {}", message);
                return;
            }
            
            // 3. 查询活动
            List<ShopSalePrize> activePrizes = getActivePrizes(message);
            if (CollectionUtils.isEmpty(activePrizes)) {
                log.debug("没有找到符合条件的抽奖活动: {}", message.getGiftType());
                return;
            }
            
            // 4. 执行赠送逻辑
            executeGrant(message, activePrizes);
            
            // 5. 后置处理
            postProcess(message);
            
        } catch (Exception e) {
            log.error("处理抽奖赠送消息失败: {}", message, e);
            handleException(message, e);
        }
    }

    /**
     * 设置上下文信息
     * 
     * @param message 赠送消息
     */
    protected void setContext(PrizeMessage message) {
        if (StringUtil.isNotEmpty(message.getTenantId())) {
            TenantContextHolder.setTenantId(message.getTenantId());
        }
        if (StringUtil.isNotEmpty(message.getShopId())) {
            ShopContextHolder.setShopId(message.getShopId());
        }
    }

    /**
     * 前置检查
     * 
     * @param message 赠送消息
     * @return 是否通过检查
     */
    protected boolean preCheck(PrizeMessage message) {
        if (message == null) {
            log.warn("赠送消息为空");
            return false;
        }
        
        if (StringUtil.isEmpty(message.getShopUserId())) {
            log.warn("用户ID为空");
            return false;
        }
        
        if (message.getGiftType() == null) {
            log.warn("赠送类型为空");
            return false;
        }
        
        return true;
    }

    /**
     * 获取当前生效的抽奖活动
     * 
     * @param message 赠送消息
     * @return 活动列表
     */
    protected List<ShopSalePrize> getActivePrizes(PrizeMessage message) {
        // 查询所有进行中、已审核的活动
        return miniShopSalePrizeService.getShopSalePrizesByLeNowDate(SalePrizeEnums.ProductFlat.YES.getCode());
    }

    /**
     * 执行赠送逻辑
     * 
     * @param message 赠送消息
     * @param activePrizes 活动列表
     */
    protected void executeGrant(PrizeMessage message, List<ShopSalePrize> activePrizes) {
        PrizeGrantStrategy strategy = strategyFactory.getStrategy(message.getGiftType());
        if (strategy == null) {
            log.warn("未找到赠送类型 {} 对应的策略实现", message.getGiftType());
            return;
        }
        
        int grantCount = 0;
        for (ShopSalePrize prize : activePrizes) {
            if (strategy.canGrant(message, prize)) {
                strategy.grantPrize(message, prize);
                grantCount++;
            }
        }
        
        log.info("用户 {} 通过 {} 方式参与了 {} 个抽奖活动", 
                message.getShopUserId(), message.getGiftType().getDesc(), grantCount);
    }

    /**
     * 后置处理
     * 
     * @param message 赠送消息
     */
    protected void postProcess(PrizeMessage message) {
        // 默认空实现，子类可重写
    }

    /**
     * 异常处理
     * 
     * @param message 赠送消息
     * @param e 异常
     */
    protected void handleException(PrizeMessage message, Exception e) {
        // 默认空实现，子类可重写进行特殊处理
    }

}
