package com.medusa.gruul.shops.mq;

import cn.hutool.core.util.IdUtil;
import com.medusa.gruul.account.api.enums.AccountQueueEnum;
import com.medusa.gruul.platform.api.constant.QueueNameConstant;
import com.medusa.gruul.platform.api.enums.QueueEnum;
import com.medusa.gruul.platform.api.model.dto.MessageTemplateCopyDto;
import com.medusa.gruul.platform.api.model.dto.WxSendMessageDto;
import com.medusa.gruul.shops.api.model.PrizeCouponMessage;
import com.medusa.gruul.shops.api.model.PrizeProductDto;
import com.medusa.gruul.shops.api.model.SendCouponMessage;
import com.medusa.gruul.shops.model.PrizeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:48 2025/4/3
 */
@Slf4j
@Component
public class Sender {

    @Autowired
    private AmqpTemplate amqpTemplate;

    @Autowired
    private RabbitTemplate rabbitTemplate;


    public void sendCouponMessage(SendCouponMessage sendCouponMessage){
        log.info("send message:" + sendCouponMessage.toString());
        convertAndSend(AccountQueueEnum.QUEUE_SEND_COUPON,sendCouponMessage);
    }

    private void convertAndSend(AccountQueueEnum queue, Object message){
        CorrelationData correlationData = new CorrelationData(IdUtil.fastSimpleUUID());
        rabbitTemplate.convertAndSend(queue.getExchange(), queue.getRouteKey(), message, correlationData);
    }
    public void sentCopyTemplateMessage(MessageTemplateCopyDto message){
        CorrelationData correlationData = new CorrelationData(IdUtil.fastSimpleUUID());
        rabbitTemplate.convertAndSend(QueueEnum.PLATFORM_COPY_TEMPLATE_MESSAGE.getExchange(),
                QueueEnum.PLATFORM_COPY_TEMPLATE_MESSAGE.getRouteKey(), message, correlationData);
    }
    public void sendPrizeCouponMessage(PrizeCouponMessage sendCouponMessage){
        CorrelationData correlationData = new CorrelationData(IdUtil.fastSimpleUUID());
        log.info("send message:" + sendCouponMessage.toString());
        com.medusa.gruul.shops.api.enums.QueueEnum prizeEnum = com.medusa.gruul.shops.api.enums.QueueEnum.QUEUE_SHOP_PRIZE_DISTRIBUTED_COUPON;
        rabbitTemplate.convertAndSend(prizeEnum.getExchange(), prizeEnum.getRouteKey(),sendCouponMessage,correlationData);
    }


    /**
     * 发送商品奖品消息到订单服务
     * @param prizeProductDto 商品奖品数据
     */
    public void sendPrizeProductMessage(PrizeProductDto prizeProductDto){
        CorrelationData correlationData = new CorrelationData(IdUtil.fastSimpleUUID());
        log.info("发送商品奖品消息到订单服务: {}", prizeProductDto.toString());
        com.medusa.gruul.shops.api.enums.QueueEnum prizeEnum = com.medusa.gruul.shops.api.enums.QueueEnum.QUEUE_SHOP_PRIZE_DISTRIBUTED_PRODUCT;
        rabbitTemplate.convertAndSend(prizeEnum.getExchange(), prizeEnum.getRouteKey(), prizeProductDto, correlationData);
    }

    /**
     * 发送抽奖赠送消息
     * @param prizeMessage 抽奖赠送消息
     */
    public void sendPrizeMessage(PrizeMessage prizeMessage) {
        try {
            CorrelationData correlationData = new CorrelationData(IdUtil.fastSimpleUUID());
            log.info("发送抽奖赠送消息: {}", prizeMessage);

            // 直接发送到队列，不需要交换机和路由键
            rabbitTemplate.convertAndSend("prize.grant.queue", prizeMessage, correlationData);

        } catch (Exception e) {
            log.error("发送抽奖赠送消息失败: {}", prizeMessage, e);
            throw e;
        }
    }

}
