package com.medusa.gruul.shops.service.handler;

import com.medusa.gruul.shops.api.conf.ShopsRedis;
import com.medusa.gruul.shops.api.entity.ShopSalePrize;
import com.medusa.gruul.shops.api.enums.SalePrizeEnums;
import com.medusa.gruul.shops.model.PrizeMessage;
import com.medusa.gruul.shops.service.PrizeGrantStrategy;
import com.medusa.gruul.shops.service.impl.MiniShopSalePrizeServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 登录赠送抽奖机会策略
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class LoginPrizeGrantHandler implements PrizeGrantStrategy {

    @Autowired
    private MiniShopSalePrizeServiceImpl miniShopSalePrizeService;

    @Override
    public SalePrizeEnums.GiftType getGiftType() {
        return SalePrizeEnums.GiftType.Login;
    }

    @Override
    public boolean canGrant(PrizeMessage message, ShopSalePrize prize) {
        // 检查活动是否配置了登录赠送
        if (prize.getLoginNumber() == null || prize.getLoginNumber() <= 0) {
            log.debug("活动 {} 未配置登录赠送或赠送次数为0", prize.getName());
            return false;
        }
        
        // 检查是否满足登录条件（最后登录日期是昨天）
        if (!checkLoginCondition(message)) {
            log.debug("用户 {} 不满足登录赠送条件", message.getShopUserId());
            return false;
        }
        
        // 检查今天是否已经赠送过登录奖励
        if (hasGrantedTodayLogin(message.getShopUserId(), prize.getId())) {
            log.debug("用户 {} 今天已经获得过活动 {} 的登录奖励", message.getShopUserId(), prize.getName());
            return false;
        }
        
        return true;
    }

    @Override
    public void grantPrize(PrizeMessage message, ShopSalePrize prize) {
        try {
            // 赠送指定次数的抽奖机会
            for (int i = 0; i < prize.getLoginNumber(); i++) {
                miniShopSalePrizeService.addPrizeCountToRedis(
                        -1L, // 订单ID，登录赠送时不需要
                        -1L, // 会员类型ID，登录赠送时不需要
                        prize,
                        message.getShopUserId(),
                        getGiftType()
                );
            }
            
            // 记录今天已赠送登录奖励
            recordTodayLoginGrant(message.getShopUserId(), prize.getId());
            
            log.info("用户 {} 登录获得抽奖机会，活动: {}, 赠送次数: {}", 
                    message.getShopUserId(), prize.getName(), prize.getLoginNumber());
                    
        } catch (Exception e) {
            log.error("登录赠送抽奖机会失败: 用户={}, 活动={}", 
                    message.getShopUserId(), prize.getName(), e);
            throw e;
        }
    }

    /**
     * 检查登录条件
     * 
     * @param message 赠送消息
     * @return 是否满足条件
     */
    private boolean checkLoginCondition(PrizeMessage message) {
        LocalDate lastLoginDate = message.getLastLoginDate();
        if (lastLoginDate == null) {
            return false;
        }
        
        // 检查最后登录日期是否是昨天
        LocalDate yesterday = LocalDate.now().minusDays(1);
        return lastLoginDate.equals(yesterday);
    }

    /**
     * 检查今天是否已经赠送过登录奖励
     * 
     * @param shopUserId 用户ID
     * @param prizeId 活动ID
     * @return 是否已赠送
     */
    private boolean hasGrantedTodayLogin(String shopUserId, Long prizeId) {
        String key = "login_grant:" + shopUserId + ":" + prizeId + ":" + LocalDate.now();
        ShopsRedis shopsRedis = new ShopsRedis();
        return shopsRedis.exists(key);
    }

    /**
     * 记录今天已赠送登录奖励
     * 
     * @param shopUserId 用户ID
     * @param prizeId 活动ID
     */
    private void recordTodayLoginGrant(String shopUserId, Long prizeId) {
        String key = "login_grant:" + shopUserId + ":" + prizeId + ":" + LocalDate.now();
        ShopsRedis shopsRedis = new ShopsRedis();
        
        // 设置过期时间为明天凌晨
        LocalDateTime tomorrow = LocalDate.now().plusDays(1).atStartOfDay();
        long seconds = Duration.between(LocalDateTime.now(), tomorrow).getSeconds();
        
        shopsRedis.setex(key, (int) seconds, "1");
    }
}
