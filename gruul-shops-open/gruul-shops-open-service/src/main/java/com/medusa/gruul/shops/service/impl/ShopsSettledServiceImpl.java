package com.medusa.gruul.shops.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.ApproveStatusEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.*;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.dto.CurMiniUserInfoDto;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.platform.api.model.dto.MessageTemplateCopyDto;
import com.medusa.gruul.shops.api.entity.ShopsPartner;
import com.medusa.gruul.shops.api.entity.ShopsSettled;
import com.medusa.gruul.shops.api.enums.PartnerModelEnum;
import com.medusa.gruul.shops.mapper.ShopsSettledMapper;
import com.medusa.gruul.shops.model.dto.ShopsPartnerDto;
import com.medusa.gruul.shops.model.dto.ShopsSettledDto;
import com.medusa.gruul.common.dto.ApproveDataParam;
import com.medusa.gruul.shops.model.param.ShopsSettledParam;
import com.medusa.gruul.shops.model.vo.ShopBaseSettingVo;
import com.medusa.gruul.shops.model.vo.ShopsSettledExcelVo;
import com.medusa.gruul.shops.model.vo.ShopsSettledVo;
import com.medusa.gruul.shops.mq.Sender;
import com.medusa.gruul.shops.service.IShopBaseSettingService;
import com.medusa.gruul.shops.service.IShopsSettledService;
import com.medusa.gruul.shops.service.ShopsPartnerService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: plh
 * @Description: 商家入驻服务实现类
 * @Date: Created in 16:43 2023/8/30
 */
@Service
public class ShopsSettledServiceImpl extends ServiceImpl<ShopsSettledMapper, ShopsSettled> implements IShopsSettledService {

    @Autowired
    private ShopsPartnerService shopsPartnerService;
    @Autowired
    private IShopBaseSettingService shopBaseSettingService;
    @Autowired
    private Sender sender;
    @Autowired
    private RemoteMiniAccountService remoteMiniAccountService;

    @Override
    public ShopsSettled add(ShopsSettledDto dto) {
        Long id = dto.getId();
        if(id!=null){//编辑
            ShopsSettled shopsSettled = this.baseMapper.selectById(id);
            if(shopsSettled==null){
                throw new ServiceException("id对应的商家入驻信息不存在！");
            }
            BeanUtils.copyProperties(dto,shopsSettled);
            shopsSettled.setApprovalStatus(ApproveStatusEnum.TEMPORARY.getStatus()+"");
            this.updateById(shopsSettled);
            return shopsSettled;
        }else{//新增
            CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
            if (ObjectUtil.isNull(curUserDto)) {
                throw new ServiceException(SystemCode.UNAUTHORIZED);
            }
            ShopsSettled shopsSettled = new ShopsSettled();
            BeanUtils.copyProperties(dto,shopsSettled);
            shopsSettled.setShopUserId(curUserDto.getUserId());
            shopsSettled.setApprovalStatus(ApproveStatusEnum.TEMPORARY.getStatus()+"");
            this.baseMapper.insert(shopsSettled);
            return shopsSettled;
        }
    }

    @Override
    public ShopsSettled submit(ShopsSettledDto dto) {
        Long id = dto.getId();

        LambdaQueryWrapper<ShopsSettled>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShopsSettled::getPhone,dto.getPhone());
        if(id!=null){
            wrapper.ne(ShopsSettled::getId,id);
        }
        ShopsSettled oldShopsSettled = this.getOne(wrapper);
        if(oldShopsSettled!=null){
            throw new ServiceException("商家入驻表手机号已经存在！");
        }
        LambdaQueryWrapper<ShopsPartner> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(ShopsPartner::getPhone,dto.getPhone());
        ShopsPartner oldShopsPartner = shopsPartnerService.getOne(wrapper1);
        if(oldShopsPartner!=null){
            throw new ServiceException("商家表手机号已经存在！");
        }else  if (StrUtil.isBlank(dto.getStoreEnvironment())){
            throw new ServiceException("请上传门店环境图片！",SystemCode.PARAM_MISS.getCode());
        }else if (dto.getStoreEnvironment().split(",").length<4){
            throw new ServiceException("门店环境图片至少需要上传4张图片！",SystemCode.PARAM_MISS.getCode());
        }
        // 8.2.5  todo app没起来 没得测试 1、检查能否升级商家
        // 获取当前登录用户
        CurMiniUserInfoDto curUserDto = CurUserUtil.getMiniReqeustAccountInfo();
        if (ObjectUtil.isNull(curUserDto)) {
            throw new ServiceException(SystemCode.UNAUTHORIZED);
        }

        // 8.2.5 如果是运营中心申请，需要校验会员等级和辐射距离
        if (  PartnerModelEnum.OPERATION_CENTER.getType().equals(dto.getPartnerModel()) && !dto.getIsContinue()) {
            // 1. 校验会员等级是否有申请资格
            Boolean canApply = remoteMiniAccountService.checkApplyShopByUserId(curUserDto.getUserId());
            if (!canApply) {
                throw new ServiceException("您没有申请权限，请先升级");
            }
            // 2. 校验辐射距离 判断能否申请
            if (dto.getMapX() != null && dto.getMapY() != null) {
                Result<ShopBaseSettingVo> checkResult = shopBaseSettingService.checkRadiationDistance(
                        Double.valueOf(dto.getMapX()), Double.valueOf(dto.getMapY()));
                // 不能申请
                if (!checkResult.getData().getCanApply()) {
                    throw new ServiceException(checkResult.getMsg());
                }
            }
        }
        if(id!=null){//编辑
            ShopsSettled shopsSettled = this.baseMapper.selectById(id);
            if(shopsSettled==null){
                throw new ServiceException("id对应的商家入驻信息不存在！");
            }
            if(Integer.valueOf( shopsSettled.getApprovalStatus())!=ApproveStatusEnum.TEMPORARY.getStatus()&&
                    Integer.valueOf( shopsSettled.getApprovalStatus())!=ApproveStatusEnum.REJECT.getStatus()){
                throw new ServiceException("不能提交正在审核，已审核数据！");
            }
            BeanUtils.copyProperties(dto,shopsSettled);
            shopsSettled.setApprovalStatus(ApproveStatusEnum.AUDIT.getStatus()+"");
            shopsSettled.setApplyTime(LocalDateTime.now());
            this.updateById(shopsSettled);
            return shopsSettled;
        }else{
            ShopsSettled shopsSettled = new ShopsSettled();
            BeanUtils.copyProperties(dto,shopsSettled);
            shopsSettled.setShopUserId(curUserDto.getUserId());
            shopsSettled.setApprovalStatus(ApproveStatusEnum.AUDIT.getStatus()+"");
            shopsSettled.setApplyTime(LocalDateTime.now());
            this.baseMapper.insert(shopsSettled);
            return shopsSettled;
        }
    }

    @Override
    public ShopsSettledVo getCurUserDraft(String partnerModel) {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        if (curUserDto == null) {
            throw new ServiceException("找不到用户信息",SystemCode.UNAUTHORIZED.getCode());
        }

        LambdaQueryWrapper<ShopsSettled> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShopsSettled::getShopUserId,curUserDto.getUserId());
        queryWrapper.eq(ShopsSettled::getApprovalStatus,ApproveStatusEnum.TEMPORARY.getStatus());

        if (partnerModel!=null){
            queryWrapper.eq(ShopsSettled::getPartnerModel,partnerModel);
        }
        queryWrapper.orderByDesc(ShopsSettled::getId).last("limit 1");
        ShopsSettled shopsSettled = getOne(queryWrapper);
        if (shopsSettled==null){
          return null;
        }
        ShopsSettledVo shopsSettledVo = new ShopsSettledVo();
        BeanUtils.copyProperties(shopsSettled,shopsSettledVo);

        return shopsSettledVo;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approve(ApproveDataParam approveDataParam) {
        String id = approveDataParam.getId();
        ShopsSettled shopsSettled = this.baseMapper.selectById(id);
        if(shopsSettled==null){
            throw new ServiceException("商家入驻信息不存在！");
        }
        shopsSettled.setApprovalStatus(approveDataParam.getApprovalStatus());
        shopsSettled.setApprovalReason(approveDataParam.getApprovalReason());
        shopsSettled.setApprovalTime(LocalDateTime.now());
        this.baseMapper.updateById(shopsSettled);
        String approvalStatus = shopsSettled.getApprovalStatus();
        if(Integer.valueOf(approvalStatus)==ApproveStatusEnum.APPROVED.getStatus()){//审核通过
            ShopsPartnerDto shopsPartnerDto = new ShopsPartnerDto();
            BeanUtils.copyProperties(shopsSettled,shopsPartnerDto);
            shopsPartnerDto.setId(null);
            if(StrUtil.isNotBlank(shopsSettled.getMapX())){
                shopsPartnerDto.setMapX(new Double(shopsSettled.getMapX()));
            }
            if(StrUtil.isNotBlank(shopsSettled.getMapY())){
                shopsPartnerDto.setMapY(new Double(shopsSettled.getMapY()));
            }
            shopsPartnerDto.setName(shopsSettled.getShopName());
            // TODO 没测试 8.2.5 复制职位 复制服务
            shopsPartnerDto.setPartnerModel(shopsSettled.getPartnerModel() != null ? shopsSettled.getPartnerModel() : PartnerModelEnum.JOIN.getType());
            shopsPartnerDto.setShopUserId(shopsSettled.getShopUserId());
//            shopsPartnerDto.setPartnerModel(PartnerModelEnum.JOIN.getType());
            shopsPartnerDto.setShopsSettledId(shopsSettled.getId());
            shopsPartnerService.addShopsPartner(shopsPartnerDto);
            // 运营中心 mq copy模版
  /*          if (PartnerModelEnum.OPERATION_CENTER.getType().equals(shopsPartnerDto.getPartnerModel())){*/
            if (shopsPartnerDto.getPartnerModel().equals(PartnerModelEnum.OPERATION_CENTER.getType())){
                MessageTemplateCopyDto dto = new MessageTemplateCopyDto();
                dto.setShopId(shopsPartnerDto.getShopId());
                dto.setPartnerModel(shopsSettled.getPartnerModel());
                dto.setTenantId(shopsSettled.getTenantId());
                sender.sentCopyTemplateMessage(dto);
            }
        }
    }

    @Override
    public PageUtils<ShopsSettledVo> getShopsSettledVo(ShopsSettledParam shopsSettledParam) {
        IPage<ShopsSettledVo> shopsSettledVo = this.baseMapper.getShopsSettledVo(new Page<>(shopsSettledParam.getCurrent(), shopsSettledParam.getSize()), shopsSettledParam);
        return new PageUtils<>(shopsSettledVo);
    }


    @Override
    public ShopsSettledVo getShopsSettledVoByShopUserId(String shopUserId) {
        if (StrUtil.isBlank(shopUserId)){
            CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
            if (ObjectUtil.isNull(curUserDto)) {
                throw new ServiceException(SystemCode.UNAUTHORIZED);
            }
            shopUserId = curUserDto.getUserId();
        }
        ShopsSettledVo shopsSettledVo = null;
        List<ShopsSettledVo> list = this.baseMapper.getShopsSettledVoByShopUserId(shopUserId);
        if(list!=null&&list.size()>0){
            shopsSettledVo= list.get(0);
        }
        return shopsSettledVo;
    }

    @Override
    public void exportShopsSettled(ShopsSettledParam param) {
        HuToolExcelUtils.exportParamToMax(param);
        // 获取数据
        PageUtils<ShopsSettledVo> pageUtils = this.getShopsSettledVo(param);
        List<ShopsSettledVo> dataList = pageUtils.getList();

        // 导出Excel
        HuToolExcelUtils.exportData(dataList, "商家申请", (ShopsSettledVo source) -> {
            ShopsSettledExcelVo target = new ShopsSettledExcelVo();
            String status = source.getApprovalStatus();
            String statusText = "0".equals(status) ? "临时保存" :
                    "100".equals(status) ? "未审核" :
                            "101".equals(status) ? "已审核" :
                                    "200".equals(status) ? "驳回" : "";
            target.setApprovalStatus(statusText);
            target.setApplyTime(source.getApplyTime() != null ? DateUtil.format(source.getApplyTime(), "yyyy-MM-dd HH:mm:ss") : "");
            return target;
        });
    }

}
