package com.medusa.gruul.shops.service;

import com.medusa.gruul.common.core.util.StringUtil;
import com.medusa.gruul.shops.model.PrizeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

/**
 * 抽奖赠送消息处理器
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class PrizeGrantHandler extends AbstractPrizeGrantTemplate {

    /**
     * 处理抽奖赠送消息
     * 
     * @param message 赠送消息
     */
    @RabbitListener(queues = "prize.grant.queue")
    public void handleMessage(PrizeMessage message) {
        log.info("收到抽奖赠送消息: {}", message);
        handlePrizeGrant(message);
    }

    @Override
    protected boolean preCheck(PrizeMessage message) {
        // 调用父类的基础检查
        if (!super.preCheck(message)) {
            return false;
        }
        
        // 根据不同的赠送类型进行特定检查
        switch (message.getGiftType()) {
            case Order:
                return checkOrderMessage(message);
            case Direct:
                return checkDirectMessage(message);
            case Login:
                return checkLoginMessage(message);
            case Register:
                return checkRegisterMessage(message);
            case Share:
                return checkShareMessage(message);
            default:
                log.warn("未知的赠送类型: {}", message.getGiftType());
                return false;
        }
    }

    /**
     * 检查订单赠送消息
     */
    private boolean checkOrderMessage(PrizeMessage message) {
        if (message.getOrderId() == null) {
            log.warn("订单赠送消息缺少订单ID");
            return false;
        }
        return true;
    }

    /**
     * 检查直推赠送消息
     */
    private boolean checkDirectMessage(PrizeMessage message) {
        if (message.getMemberLevelId() == null) {
            log.warn("直推赠送消息缺少会员等级ID");
            return false;
        }
        return true;
    }

    /**
     * 检查登录赠送消息
     */
    private boolean checkLoginMessage(PrizeMessage message) {
        if (message.getLastLoginDate() == null) {
            log.warn("登录赠送消息缺少最后登录日期");
            return false;
        }
        return true;
    }

    /**
     * 检查注册赠送消息
     */
    private boolean checkRegisterMessage(PrizeMessage message) {
        // 注册赠送只需要基础的用户ID检查
        return true;
    }

    /**
     * 检查分享赠送消息
     */
    private boolean checkShareMessage(PrizeMessage message) {
        if (StringUtil.isEmpty(message.getParentId())) {
            log.warn("分享赠送消息缺少新用户ID");
            return false;
        }
        return true;
    }

    @Override
    protected void handleException(PrizeMessage message, Exception e) {
        // 记录异常信息，可以考虑发送到死信队列或告警系统
        log.error("处理抽奖赠送消息异常，消息将被丢弃: message={}", message, e);
        
        // 这里可以添加异常处理逻辑，比如：
        // 1. 发送到死信队列
        // 2. 记录到数据库
        // 3. 发送告警通知
    }
}
