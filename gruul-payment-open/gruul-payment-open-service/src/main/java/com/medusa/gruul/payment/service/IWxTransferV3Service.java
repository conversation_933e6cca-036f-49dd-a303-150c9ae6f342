package com.medusa.gruul.payment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.payment.api.entity.WxTransferV3;
import com.medusa.gruul.payment.api.model.message.WxTransferSceneV3Message;
import com.medusa.gruul.payment.api.model.message.WxTransferV3Message;
import com.medusa.gruul.payment.api.transfer.InitiateBatchTransferResponseNew;
import com.medusa.gruul.payment.api.transfer.TransferDetailEntityNew;
import com.medusa.gruul.payment.model.dto.InitiateBatchTransferRequestNew;

import javax.servlet.http.HttpServletRequest;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:18 2025/4/22
 */

public interface IWxTransferV3Service extends IService<WxTransferV3> {

    /**
     * 接收微信转账消息
     * @param message
     */
    void batchTransfer(WxTransferV3Message message);

    /**
     * 处理微信转零钱
     * @param request
     * @return
     */
    void initiateBatchTransferNew(WxTransferV3 wxTransferV3,InitiateBatchTransferRequestNew request);


    /**
     * 更新微信转零钱订单状态
     */
    void updateTransfersStatus();

    /**
     * 商户单号查询转账单
     * @param wxTransferV3
     * @return
     */
    TransferDetailEntityNew getTransferDetailByOutNoNew(WxTransferV3 wxTransferV3);

    /**
     * 验证单号是否为待客户收款
     * @param id
     * @return
     */
    Boolean vailReceiveMoneyState(String id);

    /**
     * 更新微信转账单状态
     * @param id
     * @return
     */
    String updateTransferState(String id);

    /**
     * 微信转账成功回调
     * @param request
     */
    void wxPaySuccessCallback(HttpServletRequest request);
}
